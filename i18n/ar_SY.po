# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* olivery_dispatcher_map
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0-20211011\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-14 06:25+0000\n"
"PO-Revision-Date: 2024-08-14 06:25+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_default_group_by_sequence__create_uid
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_date_rule__create_uid
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_filter__create_uid
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_map_config__create_uid
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__create_uid
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__create_uid
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_select_route_state__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_default_group_by_sequence__create_date
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_date_rule__create_date
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_filter__create_date
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_map_config__create_date
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__create_date
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__create_date
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_select_route_state__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__direction
msgid "Direction"
msgstr "الاتجاه"

#. module: olivery_dispatcher_map
#: model:ir.ui.menu,name:olivery_dispatcher_map.menu_rb_delivery_location
msgid "Dispatcher Map"
msgstr "لوحة الموزع"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__done_locations
msgid "Done Locations"
msgstr "الوجهات المنتهيه"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__driver_id
msgid "Driver"
msgstr "المندوب"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_user__nearest_driver_orders
msgid "Driver Orders Assigned"
msgstr "الطلبيات الموكلة للمندوب"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__id
msgid "ID"
msgstr "المعرف"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__locations
msgid "Locations"
msgstr "مواقع التخزين"

#. module: olivery_dispatcher_map
#: code:addons/olivery_dispatcher_map/models/order/order_model.py:84
#: code:addons/olivery_market/olivery_dispatcher_map/models/order/order_model.py:84
#, python-format
msgid "Make sure agent is sharing location!\n"
""
msgstr "الرجاء التأكد ان السائق يشارك موقعه ! "

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_order__agent_mobile_number
msgid "Mobile Number"
msgstr "رقم الموبايل"

#. module: olivery_dispatcher_map
#: model:ir.model,name:olivery_dispatcher_map.model_rb_delivery_order
msgid "Order Model"
msgstr "الطلبات"

#. module: olivery_dispatcher_map
#: code:addons/olivery_dispatcher_map/models/routes/routes_model.py:45
#: code:addons/olivery_dispatcher_map/models/routes/routes_model.py:58
#: code:addons/olivery_market/olivery_dispatcher_map/models/routes/routes_model.py:45
#: code:addons/olivery_market/olivery_dispatcher_map/models/routes/routes_model.py:58
#, python-format
msgid "Order has been updated while creating a route"
msgstr "تم تعديل الطلب من خلال انشاء مسار"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__order_ids
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__order_ids
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_tree_rb_delivery_route_collection
msgid "Orders"
msgstr "الطلبيات"

#. module: olivery_dispatcher_map
#: code:addons/olivery_dispatcher_map/models/route_collection/route_collection_model.py:205
#, python-format
msgid "Orders %s are in route collections of sequences %s"
msgstr "الطلبيات %s في كشف تحصيل المسار بالرقم %s"

#. module: olivery_dispatcher_map
#: code:addons/olivery_dispatcher_map/models/order/order_model.py:82
#: code:addons/olivery_market/olivery_dispatcher_map/models/order/order_model.py:82
#, python-format
msgid "Please set agent to the order!\n"
""
msgstr "يرجى اضافة سائق للطلبية !"

#. module: olivery_dispatcher_map
#: model:ir.model,name:olivery_dispatcher_map.model_rb_delivery_user
msgid "User Model"
msgstr "المستخدم"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_default_group_by_sequence__default_group_by_fields
msgid "Default Group By Fields"
msgstr "اعدادات التجميع الافتراضيه"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_map_config__default_group_sequence
msgid "Default Grouping Fields"
msgstr "الحقول للافتراضيه للتجميع"

#. module: olivery_dispatcher_map
#: model:ir.actions.act_window,name:olivery_dispatcher_map.action_config
#: model:ir.ui.menu,name:olivery_dispatcher_map.menu_config
msgid "Dispatcher Map Config"
msgstr "اعدادات لوحة الموزع"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_map_config__group_by_fields
msgid "Group By Fields"
msgstr "حقول التجميع حسب"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_map_config__role
msgid "Role"
msgstr "الصلاحيه"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_default_group_by_sequence__dispatcher_config_id
msgid "Map Config"
msgstr "اعدادات الخريطه"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_order__zone_id
msgid "Zone"
msgstr "نطاق"

#. module: olivery_dispatcher_map
#: code:addons/olivery_dispatcher_map/models/dispatcher_map_config/dispatcher_map_config_model.py:17
#: code:addons/olivery_market/olivery_dispatcher_map/models/dispatcher_map_config/dispatcher_map_config_model.py:17
#: sql_constraint:rb_delivery.dispatcher_map_config:0
#, python-format
msgid "Configuration for the same role already exist!"
msgstr "هنالك اعدادات لنفس الصلاحية موجوده بالفعل!"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_user__dispatcher_business_orders
msgid "Business Orders For Dispatcher Map"
msgstr "طلبات المرسل لخريطة الموزع"


#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_date_rule__dispatcher_filter_date_filter_field
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_filter__dispatcher_filter_date_filter_field
msgid "Date Filter Field"
msgstr "حقل فلترة التاريخ"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_date_rule__dispatcher_filter_date_filter_selection
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_filter__dispatcher_filter_date_filter_selection
msgid "Date Filter Selection"
msgstr "اختيارات فلترة الترايخ"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_filter__dispatcher_date_rule_ids
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_form_dispatcher_filter
msgid "Dispatcher Date Rules"
msgstr "قواعد تاريخ الموزع"

#. module: olivery_dispatcher_map
#: model:ir.actions.act_window,name:olivery_dispatcher_map.action_dispatcher_map_dispatcher_filter
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_date_rule__dispatcher_filter_id
#: model:ir.ui.menu,name:olivery_dispatcher_map.menu_dispatcher_map_dispatcher_filter
msgid "Dispatcher Filter"
msgstr "فلتر الموزع"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_filter__filter_domain
msgid "Filter Domain"
msgstr "مجال الفلترة"

#.module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_dispatcher_filter__dispatcher_filter_model_selection
msgid "Model Selection"
msgstr "اختيار مودل"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,help:olivery_dispatcher_map.field_rb_delivery_dispatcher_filter__name
msgid "Name of the filter"
msgstr "اسم الفلتر"

#. module: olivery_dispatcher_map
#: selection:rb_delivery.dispatcher_date_rule,dispatcher_filter_date_filter_selection:0
#: selection:rb_delivery.dispatcher_filter,dispatcher_filter_date_filter_selection:0
msgid "None"
msgstr "غر معرف"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_default_group_by_sequence__sequence
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__sequence
msgid "Sequence"
msgstr "التسلسل"

#. module: olivery_dispatcher_map
#: selection:rb_delivery.dispatcher_date_rule,dispatcher_filter_date_filter_selection:0
#: selection:rb_delivery.dispatcher_filter,dispatcher_filter_date_filter_selection:0
msgid "This Month"
msgstr "هذا الشهر"

#. module: olivery_dispatcher_map
#: selection:rb_delivery.dispatcher_date_rule,dispatcher_filter_date_filter_selection:0
#: selection:rb_delivery.dispatcher_filter,dispatcher_filter_date_filter_selection:0
msgid "This Quarter"
msgstr " هذا الربع"

#. module: olivery_dispatcher_map
#: selection:rb_delivery.dispatcher_date_rule,dispatcher_filter_date_filter_selection:0
#: selection:rb_delivery.dispatcher_filter,dispatcher_filter_date_filter_selection:0
msgid "This Week"
msgstr "هذا الاسبوع"

#. module: olivery_dispatcher_map
#: selection:rb_delivery.dispatcher_date_rule,dispatcher_filter_date_filter_selection:0
#: selection:rb_delivery.dispatcher_filter,dispatcher_filter_date_filter_selection:0
msgid "Today"
msgstr "اليوم"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_form_rb_delivery_route_collection
msgid "<span>All orders</span>"
msgstr "<span>كل الطلبيات</span>"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_form_rb_delivery_route_collection
msgid "<span>Done orders</span>"
msgstr "<span>الطلبيات التامة</span>"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_form_rb_delivery_route_collection
msgid "<span>Not done orders</span>"
msgstr "<span>الطلبيات الغير تامة</span>"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__assign_to_agent
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_select_route_state__agent
msgid "Agent"
msgstr "السائق"

#. module: olivery_dispatcher_map
#: model:ir.actions.act_window,name:olivery_dispatcher_map.action_olivery_collection_all_route
#: model:ir.ui.menu,name:olivery_dispatcher_map.menu_olivery_collection_all_route
msgid "All Route collections"
msgstr "كل كشوفات المسار"

#. module: olivery_dispatcher_map
#: model:ir.actions.act_window,name:olivery_dispatcher_map.action_olivery_collection_route
#: model:ir.ui.menu,name:olivery_dispatcher_map.menu_olivery_collection_route_collections
msgid "Active Route collections"
msgstr "كشوفات المسار"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__barcode
msgid "Barcode"
msgstr "باركود"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_search_rb_delivery_route_collection
msgid "By Agent"
msgstr "السائق"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_search_rb_delivery_route_collection
msgid "By Previous Agent"
msgstr "حسب السائق السابق"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_search_rb_delivery_route_collection
msgid "By Previous Status"
msgstr "الحالة السابقة"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_form_rb_delivery_route_collection
msgid "Change status"
msgstr "تغير الحالات"

#. module: olivery_dispatcher_map
#: code:addons/olivery_dispatcher_map/models/route_collection/route_collection_model.py:321
#, python-format
msgid "Collection has been updated by %s through function write_jq."
msgstr ""

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__status_color
msgid "Colour code"
msgstr "رمز اللون"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_search_rb_delivery_route_collection
msgid "Completed Collections"
msgstr "تحصيلات مكتملة "

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__delivered_orders
msgid "Delivered orders"
msgstr "الطلبيات الموصلة"

#. module: olivery_dispatcher_map
#: selection:rb_delivery.routes,status:0
#: model:rb_delivery.status,title:olivery_dispatcher_map.status_route_collection_done
msgid "Done"
msgstr "تم"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_search_rb_delivery_route_collection
msgid "Filters"
msgstr "الفلاتر"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_order__route_collection_id
msgid "In route collection"
msgstr "في كشف المسار"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_order__route_collection_state
msgid "Route collection state"
msgstr "حالة كشف المسار"

#. module: olivery_dispatcher_map
#: model:rb_delivery.status,title:olivery_dispatcher_map.status_route_collection_open
msgid "Open"
msgstr "مفتوح"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__order_count
msgid "Order count"
msgstr "عدد الطلبيات"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__previous_agent
msgid "Previous Agent"
msgstr "السائق السابق"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__previous_status
msgid "Previous Status"
msgstr "الحالة السابقة"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__previous_status_title
msgid "Previous Status Name"
msgstr "اسم الحالة السابقة"

#. module: olivery_dispatcher_map
#: model_terms:ir.ui.view,arch_db:olivery_dispatcher_map.view_form_rb_delivery_route_collection
msgid "Sequence Number"
msgstr "الرقم المتسلسل"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__state
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_routes__status
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_select_route_state__state
msgid "Status"
msgstr "الحالة"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__state_id
msgid "Status ID"
msgstr "رقم تعريف الحالات"

#. module: olivery_dispatcher_map
#: model:ir.model,name:olivery_dispatcher_map.model_rb_delivery_route_collection
msgid "Route collection Model"
msgstr ""

#. module: olivery_dispatcher_map
#: code:addons/olivery_dispatcher_map/models/route_collection/route_collection_model.py:242
#, python-format
msgid "Route collection generated"
msgstr "انشاء كشف المسار"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__use_qr_code
msgid "Use QR code"
msgstr "استخدام رمز QR"

#. module: olivery_dispatcher_map
#: code:addons/olivery_dispatcher_map/models/route_collection/route_collection_model.py:374
#: code:addons/olivery_dispatcher_map/models/route_collection/route_collection_model.py:395
#, python-format
msgid "route collection"
msgstr "كشف المسار"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:46
#: code:addons/rb_delivery/models/error_log/error_log_model.py:46
#, python-format
msgid "Error Creating route collection"
msgstr "خطأ اثناء انشاء كشف المسار"

#. module: rb_delivery
#: code:addons/delivery_modules/rb_delivery/models/error_log/error_log_model.py:48
#: code:addons/rb_delivery/models/error_log/error_log_model.py:48
#, python-format
msgid "Please remove the orders from the route collection first."
msgstr "ازل الطلبيات من كشف المسار القديم"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__route
msgid "Route"
msgstr "المسار"

#. module: olivery_dispatcher_map
#: model:ir.model.fields,field_description:olivery_dispatcher_map.field_rb_delivery_route_collection__non_delivered_orders
msgid "Not delivered orders"
msgstr "الطلبيات الغير الموصلة"