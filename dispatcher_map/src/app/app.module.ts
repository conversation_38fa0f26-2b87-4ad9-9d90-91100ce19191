import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppComponent } from './app.component';
import { GoogleMapsModule } from '@angular/google-maps';
import { SideMenuComponentComponent } from './components/side-menu-component/side-menu-component.component';
import { MapDashboardComponent } from './components/map-dashboard/map-dashboard.component';
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import {HttpClient, HttpClientModule} from '@angular/common/http';
import { DriverListComponent } from './components/driver-list/driver-list.component';
import { FormsModule } from '@angular/forms';
import { QuickOrderComponent } from './components/quick-order/quick-order.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { ReactiveFormsModule } from '@angular/forms';
import { StatusListComponent } from './components/status-list/status-list.component';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { OrderItemComponent } from './components/order-item/order-item.component';
import { GroupContainerComponent } from './components/group-container/group-container.component';



@NgModule({
    declarations: [
        AppComponent,
        SideMenuComponentComponent,
        MapDashboardComponent,
        DriverListComponent,
        QuickOrderComponent,
        StatusListComponent,
        OrderItemComponent,
        GroupContainerComponent
    ],
    imports: [
        BrowserModule,
        GoogleMapsModule,
        HttpClientModule,
        FormsModule,
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: HttpLoaderFactory,
                deps: [HttpClient]
            }
        }),
        DragDropModule,
        ReactiveFormsModule,
        ScrollingModule
    ],
    providers: [],
    bootstrap: [AppComponent]
})
export class AppModule { }

export function HttpLoaderFactory(http: HttpClient): TranslateHttpLoader {
    return new TranslateHttpLoader(http,window.location.origin+'/olivery_dispatcher_map/static/src/dispatcher_map/assets/i18n/','.json');
}
