<div *ngIf="googleApiLoaded" class="main-app-container">
    <google-map
        *ngIf="googleApiLoaded"
        height="100vh"
        width="calc(100vw - 570px)"
        style="position: absolute; right: 0; width: calc(100vw - 570px);"
        [options]="mapOptions"
    ></google-map>
    <app-map-dashboard
        (onClickDeliveryOverdueOrders)="goToOrders($event,'delivery_overdue',['DELIVERY_OVERDUE'])"
        (onClickPickupOverdueOrders)="goToOrders($event,'pickup_overdue',['PICKUP_OVERDUE'])"
        [deliveryOverdueOrders]="deliveryOverdueOrders"
        [pickupOverdueOrders]="pickupOverdueOrders"
    ></app-map-dashboard>
    <app-side-menu-component *ngIf="userRole && isUsersFetched"
        [activeContent]="activeContent"
        [groupByFields]="groupByFields.length>0?groupByFields:[['state_id','many2one','Status',true]]"
        [isFilteredOnOrders]="isFilteredOnOrders"
        [filterTexts]="filterTexts"
        [loading]="loading"
        [sessionId]="sessionId"
        [orders]="allOrders"
        [users]="filteredUsers"
        [routes]="routes"
        [doneRoutes]="doneRoutes"
        [currentOrder]="currentOrder"
        [statuses]="visibleStatuses"
        [subAreas]="allSubAreas"
        [activeAgent]="filteredOnAgent"
        [activeRoute]="filteredOnRoute"
        [activeSender]="filteredOnSender"
        [aciveOrder]="filteredOnOrder"
        [timeDurationConfig]="timeDurationConfig"
        [userRole]="userRole"
        [maxNumberOfShipments]="maxNumberOfShipments"
        [driversToSelect]="(driversToSelect$ | async) ?? []"
        (onClickAgent)="showAgentOnMap($event)"
        (onClickAssignDriver)="fetchAgentsSorted($event)"
        (onClickChooseStatus)="activateChangeStatus($event)"
        (onClickShowOnMap)="$event.agent?showAgentOnMap($event.agent,$event.showOnlyRelatedMarkers):$event.sender?showSenderOnMap($event.sender,$event.showOnlyRelatedMarkers):$event.route?showRouteOnMap($event.route,$event.showOnlyRelatedMarkers):showOrderOnMap($event.order,$event.showOnlyRelatedMarkers)"
        (onClickShowDriversInRange)="filteredOnRange = $event;filterOnRange();"
        (onSearch)="search($event)"
        (onActiveContentChange)="changeActiveContent($event)"
        (onQuickOrderDismiss)="showQuickOrder = $event"
        (onClickFilter)="updateDomain($event)"
        (onGroupOrdersChecked)="setCheckedOrderIds($event)"
        (onAction)="handleActionOnSelectedOrders($event)"
        (onShowToast)="showToast($any($event).text,$any($event).type)"
        [dispatcherOrderFilters]="dispatcherOrderFilters"
        [orderDefaultFilters]="orderDefaultFilters"
        [showDriverStatusInSideMenuForBusiness]="showDriverStatusInSideMenuForBusiness"
    ></app-side-menu-component>
    <app-driver-list 
        (onClickDriver)="updateCurrentOrder($event)" 
        (onClickShowOnMap)="showAgentOnMap($event.agent)"
        (onClose)="closeDriverList()"
        *ngIf="showDriverList && currentOrder" 
        class="app-driver-container" 
        [currentOrder]="currentOrder" 
        [activeAgent]="filteredOnAgent"
        [driversToSelect]="(driversToSelect$ | async) ?? []"
        [loading]="loading"
        [ngStyle]="{'top':getTopOffset(currentOrder)}"
        [nextStatusList]="getNextStatus(currentOrder)"
    ></app-driver-list>
     <app-status-list 
        (onClickStatus)="updateCurrentOrder($event)" 
        (onClose)="closeStatusList()"
        *ngIf="isChooseStatusActive" 
        class="app-driver-container" 
        [currentOrder]="currentOrder" 
        [loading]="loading"
        [ngStyle]="{'top':getTopOffset(currentOrder)}"
        [nextStatusList]="getNextStatus(currentOrder)"
     ></app-status-list>

    <div 
    *ngIf="filteredOnRange" 
    class="app-slider-container" 
    >
        <div style="display: flex; justify-content: space-between;">
            <label>{{'RANGE' | translate}}: {{driversRange/1000}}<span style="font-size: 8px;">{{'KM'}}</span></label>
            <div>
                <label>{{'FIT_TO_BOUNDS' | translate}}</label>
                <input [(ngModel)]="fitGeofenceToBounds" (ngModelChange)="filterOnRange()" type="checkbox">
            </div>
        </div>
        
        <input [(ngModel)]="driversRange" (ngModelChange)="filterOnRange()" step="100"  type="range" min="1000" max="50000" value="5000" class="slider" id="driversRange">
    </div>

     <app-quick-order 
     *ngIf="showQuickOrder"
     class="app-quick-order"
     cdkDrag
     [cdkDragBoundary]="'.main-app-container'"
     [areas]="allAreas"
     [allSubAreas]="allSubAreas"
     [users]="allUsers"
     [userRole]="userRole"
     [currentUserId]="currentUserId"
     (formSubmitted)="sendOrder($event)"
     (fetchDeliveryCost)="fetchDeliveryCost($event)"
     [deliveryCost]="deliveryCost"
     (closeOrder)="showQuickOrder = false"
     [showCustomerDetails]="showCustomerDetails"
     [showPaymentSection]="showPaymentSection"
     [showNotesSection]="showNotesSection"
     [showExtraCost]="showExtraCost"
     ></app-quick-order>
     
    <div class="toast" [ngStyle]="{'background-color':toastType == 'success'?'green':toastType == 'fail'?'red':'orange'}" *ngIf="isToastVisible">
        <p *ngIf="toastTitle" style="font-weight: bolder;">{{toastTitle}}</p>
        <p style="font-size: 15px;">{{toastMessage}}</p>
    </div>
</div>
