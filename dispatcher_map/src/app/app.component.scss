#map{
    position: absolute; 
    right: 0;
    width: calc(100vw - 570px);
    height: 100vh;
}
.app-driver-container{
    
    flex-direction: column;
    display: flex;
    position: absolute;
    left: 570px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 0 10px #ccc;
    width: 20vw;
    overflow: scroll;
    padding: 12px;
    padding-top: 25px;
    max-height: 40vh;
    top: 247px;
    padding: 0;

}

.app-slider-container{
    flex-direction: column;
    display: flex;
    position: absolute;
    right: calc(100vw - 570px - (100vw - 570px) / 2 - 10vw);
    background: white;
    border-radius: 10px;
    box-shadow: 0 0 10px #ccc;
    width: 20vw;
    overflow: scroll;
    padding: 12px;
    padding-top: 25px;
    max-height: 40vh;
    bottom: 20px;

}

.app-quick-order{
    flex-direction: column;
    position: absolute;
    height: 80vh;
    left: 40vw;
    background: #fcefe7;
    width: 20vw;
    overflow: scroll;
    padding: 12px;
    height: fit-content;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1);
}

.toast {
    position: fixed;
    right: 15px;
    top: 15px;
    height: fit-content;
    padding: 15px;
    color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;  
    text-align: center;
    max-width: 570px;

}

::ng-deep .cdk-virtual-scroll-content-wrapper {
    max-width: 100%;
}