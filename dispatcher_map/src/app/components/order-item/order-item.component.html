<div [class.fade_animation]="order['add_fade_animation']" [id]="order.id" class="list-item" style="width: 95%; margin: 15px auto;">
    <div class="order-item-header">
        
        <div class="row-col" style="color: white; font-weight: bold;">
            <label (click)="$event.stopPropagation()" class="custom-checkbox">
                <input
                    type="checkbox"
                    #checkbox
                    (click)="$event.stopPropagation()"
                    (change)="toggleOrderSelection($event)"
                    [checked]="getChecked()"
                />
                <span class="checkmark"></span>
            </label>
            <img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/barcode.svg"/>
            <span>{{ order.sequence }}</span>
        </div>
        <button class="item-row" (click)="showStatusList.emit(order)" style="cursor: pointer; font-size: 15px; font-weight: bolder; border: 1px solid white; border-radius: 15px; background: white; padding-inline: 5px;">
            <strong>{{ order.state_id[1] }}</strong>
        </button>
    </div>
    <div class="order-item-content">
        <div class="item-row">
            <div class="item-col" style="height: 50%;">
                <div class="row-col"> <img style="border-radius:50%; box-shadow: 0 0 10px #ccc; width: 40px;" [src]="getImageUrl(order.assign_to_business[0]!)"/> <span style="width: 150px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">{{ order.assign_to_business[1] }}</span></div> 
                <div class="row-col"> <img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/phone.webp"/><a style="color: #1c8adb;" href="tel:{{ order.business_mobile_number }}">{{ order.business_mobile_number }}</a> </div> 
                <div class="row-col"> <img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/whatsapp.png"/><a style="color: #3ab600;" target="_blank" href="https://wa.me/{{ order.business_whatsapp_mobile }}">{{ order.business_whatsapp_mobile }}</a> </div> 
                
            </div>
        </div>

        <div class="item-row" style="justify-content: center;">
            <img style="width: 50%;" [ngStyle]="{'transform':translate.currentLang=='ar'? 'rotateY(180deg)' : 'none'}" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/long-arrow.png"/>
        </div>

        <div class="item-row" style="justify-content: flex-end;">
            <div class="item-col" style="height: 50%;">
                <div class="row-col"> <img style="border-radius:50%; box-shadow: 0 0 10px #ccc; width: 40px;" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/customer.png"/>
                    <span style="width: 70px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">{{ order.customer_name }}<br/>{{order.customer_sub_area?order.customer_sub_area[1]:''}}</span>
                </div> 
                <div class="row-col"> <img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/phone.webp"/><a style="color: #1c8adb;" href="tel:{{ order.customer_mobile }}">{{ order.customer_mobile }}</a> </div> 
                <div class="row-col"> <img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/whatsapp.png"/> <a style="color: #3ab600;" target="_blank" href="https://wa.me/{{ order.cus_whatsapp_mobile }}">{{ order.cus_whatsapp_mobile }}</a></div> 
                
            </div>
        </div>

        <div class="item-row" >
            <div class="item-col" style="height: 50%;" *ngIf="order.assign_to_agent">
                <div class="row-col"> <img style="border-radius:50%; box-shadow: 0 0 10px #ccc; width: 40px;" [src]="getImageUrl(order.assign_to_agent[0]!)"/> <span style="width: 150px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">{{ order.assign_to_agent[1] }}</span></div> 
                <div class="row-col"> <img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/phone.webp"/><a style="color: #1c8adb;" href="tel:{{ getAgentMobile(order) }}">{{ getAgentMobile(order) }}</a> </div> 
                <div class="row-col"> <img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/whatsapp.png"/><a style="color: #3ab600;" target="_blank" href="https://wa.me/{{ order.business_whatsapp_mobile }}">{{ order.agent_whatsapp_mobile }}</a> </div> 
                <div *ngIf="nearestDriverTimeOut>0 || (order.notification_timer && order.notification_timer>0 && startNearestDriverTimer(order.notification_timer))" class="row-col"><div style="background-color: red; color: white; border-radius: 50%; height: 27px; width:27px; display: flex; align-items: center; justify-content: center;">{{nearestDriverTimeOut}}</div>{{'TIME_TO_ACCEPT' | translate}}</div>
            </div>
        </div>
        <div class="item-row" style="justify-content: flex-end; padding-inline-end: 5px;">     
            <div class="item-col" >
                <strong class="row-col" style="font-size: 10px; justify-content: end;">{{'CREATE_DATE' |translate}}</strong>
                <strong class="row-col" style="font-size: 10px; justify-content: end;" *ngIf="order.state=='picked_up' && order.eta_for_delivered">{{'ETA_FOR_DELIVERED' | translate}}</strong>
                <strong class="row-col" style="font-size: 10px; justify-content: end;" *ngIf="order.state=='picking_up' && order.eta_for_picked_up">{{'ETA_FOR_PICKED_UP' | translate}}</strong>
            </div>
        </div>
        <div class="item-row">
            <div class="item-col">
                <span class="row-col">{{getLocalDateTime(order.create_date)}}</span>
                <span class="row-col" *ngIf="order.state=='picked_up' && order.eta_for_delivered">{{ order.eta_for_delivered }}</span> 
                <span class="row-col" *ngIf="order.state=='picking_up' && order.eta_for_picked_up">{{ order.eta_for_picked_up}}</span> 
            </div>
            
        </div>
    </div>
    
    <div style="background-color: white; display: flex; flex-direction: row; padding: 5px; gap: 5px;">
        <button *ngIf="userRole!='rb_delivery.role_business'" class="select-btn" (click)="showDriverList.emit(order)">{{ 'ASSIGN_AGENT' | translate }}</button>
        <button (click)="showOrderOnMap.emit(order)" class="select-btn" [ngStyle]="{'background': aciveOrder && aciveOrder.id == order.id ? 'red' : '#3ab600'}">
            {{ (aciveOrder && aciveOrder.id == order.id ? 'DISMISS' : 'SHOW_ON_MAP') | translate }}
        </button>
        <button *ngIf="(!aciveOrder || aciveOrder.id != order.id) && order.longitude && order.latitude && userRole!='rb_delivery.role_business'" 
            (click)="showDriversInRange.emit(order); showOrderOnMap.emit(order)" 
            class="select-btn">
            {{ 'SHOW_DRIVERS_IN_RANGE' | translate }}
        </button>

    </div>
</div>