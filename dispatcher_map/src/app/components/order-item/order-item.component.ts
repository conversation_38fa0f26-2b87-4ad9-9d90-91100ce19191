import { Component, Input, Output, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-order-item',
  templateUrl: './order-item.component.html',
  styleUrls: ['./order-item.component.scss']
})
export class OrderItemComponent {
  @Input() order: any;
  @Input() aciveOrder: any;
  @Input() userRole!: string;
  @Input() nearestDriverTimeOut: number = 0;
  @Input()timeDurationConfig:number=0
  @Input()checked:boolean=false
  @Input()checkedOrderIds:number[]=[]

  @Output() showStatusList = new EventEmitter<any>();
  @Output() showDriverList = new EventEmitter<any>();
  @Output() showOrderOnMap = new EventEmitter<any>();
  @Output() showDriversInRange = new EventEmitter<any>();
  @Output() selectedGroupOrders = new EventEmitter<any[]>();

  isTimerStarted!: boolean;

  constructor(
    public translate:TranslateService
  ){

  }

  getImageUrl(userID:number){
    return `${window.location.origin}/web/image/rb_delivery.user/${userID}/user_image/50x50`;
  }

  getAgentMobile(order:any){
    if(order.agent_commercial_number){
        return order.agent_commercial_number
    }else if(order.agent_mobile_number){
        return order.agent_mobile_number
    }else{
        return false
    }
  }

  getLocalDateTime(createDate:string) {
    const utcDateTime = new Date(createDate);
    const localTimeOffset = utcDateTime.getTimezoneOffset();
    const localDateTime = new Date(utcDateTime.getTime() - (localTimeOffset * 60000));
    const formattedDateTime = localDateTime.toLocaleString(undefined, {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false // Use 24-hour format
    });

    return formattedDateTime;
  }

  startNearestDriverTimer(timer:number) {
    
    if(this.isTimerStarted || (!timer || timer==0)){
        return false
    }
    this.nearestDriverTimeOut=this.timeDurationConfig-timer
    let timerInterval = setInterval(() => {

        if (this.nearestDriverTimeOut > 0) {
            this.isTimerStarted=true
            this.nearestDriverTimeOut -= 1;
        }
        else{
            this.isTimerStarted=false
            clearInterval(timerInterval)
        }
    }, 1000);
    return true
  }

  toggleOrderSelection( event: any) {
    this.checked=event.target.checked
    if(this.checked){
      this.checkedOrderIds = this.checkedOrderIds.concat([this.order.id])
    }else{

      this.checkedOrderIds = this.checkedOrderIds.filter(id=>this.order.id != id)
    }
    this.selectedGroupOrders.emit(this.checkedOrderIds);
  }


  getChecked(){
    return [this.order.id].every((element: number) => this.checkedOrderIds.includes(element));
  }


  // Add other necessary logic here
}
