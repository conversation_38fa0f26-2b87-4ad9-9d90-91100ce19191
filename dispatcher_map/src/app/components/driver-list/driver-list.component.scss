.driver-container {
    display: flex;
    align-items: center;
    margin: 10px 0;
    padding: 10px;
    background-color: #fff;
    height: 6vh;
}

.online-btn {
    color: #fff;
    width: fit-content;
    white-space: nowrap;
    background-color: #25D366;
    border-radius: 15px;
    margin: 0px 6px;
    padding: 5px 10px;
    zoom: .7;
}

.offline-btn {
    color: #fff;
    width: fit-content;
    white-space: nowrap;
    background-color: #FF0000;
    border-radius: 15px;
    margin: 0px 6px;
    padding: 5px 10px;
    zoom: .7;
}

.select-btn {
    width: 100%;
    height: 20px;
    border-radius: 10px;
    background-color: #3ab600;
    border: none;
    cursor: pointer;
    color: #ffffff;
}

.select-btn-outline {
    width: 100%;
    height: 20px;
    border-radius: 10px;
    background-color: white;
    border: 1px solid #3ab600;
    cursor: pointer;
    color: #3ab600;
}

.fixed-to-top {
    position: sticky;
    left: 0;
    top: 0;
    align-items: center;
    display: flex;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    button{
        all: unset;
        margin-inline: 16px;
        margin-top: 3px;
        cursor: pointer;
    }
    h4{
        all: unset;
        margin-inline-start:16px ;
    }
}

.loading{
    background-color: black;
    justify-content: center;
    color: white;
}

.search-container{
    display: flex;
    height: 48px;
    border-radius: 10px;
    width: 100%;
    padding: 5px;
    border: 1px solid #E3E3E3;
    input{
        all: unset;
        height: 100%;
        width: -webkit-fill-available;
    }
    img{
        width: 20px;
        margin-inline:12px;
    }
}

.list-item {
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    border: 1px solid #E3E3E3;
    margin: 10px 0;
    background-color: white;
}

.icon{
    width: 20px;
}