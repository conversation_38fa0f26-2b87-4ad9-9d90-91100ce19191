
<div *ngIf="!loading"  class="fixed-to-top" style="justify-content: space-between; background-color: white; padding-block: 15px;">
    <h4>{{'SELECT_DRIVER' | translate}}</h4>
    <ng-container *ngIf="!hideStatusSelector">
        <input type="checkbox" [checked]="isChooseStatusEnabled" (change)="isChooseStatusEnabled=!isChooseStatusEnabled" />
        <span [ngStyle]="{'color':isChooseStatusEnabled?'black':'grey'}">{{'SELECT_STATUS' | translate}}</span>
        <div >
            <select [disabled]="!isChooseStatusEnabled" [(ngModel)]="selectedState">
                <ng-container *ngFor="let status of nextStatusList" >
                    <option  [value]="status.name">{{status.title}}</option>
                </ng-container>
            </select>
        </div>
    </ng-container>
    
    <button (click)="close()"><img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/close.svg"/></button>
</div>
<div style="padding: 10px;">
    <div class="search-container">
        <img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/search.svg"/>
        <input type="text" placeholder="{{'SEARCH_BY_NAME_OR_MOBILE_NUMBER'|translate}}" [(ngModel)]="searchQuery" (input)="filterDrivers()">
    </div>
    <div *ngIf="loading" class="fixed-to-top loading">{{'LOADING' | translate}}</div>
    
    <div *ngFor="let driver of filteredDrivers" class="list-item">
        <div class="driver-container">
            <img style="border-radius: 50%; box-shadow: 0 0 10px #ccc; width: 45px; margin-inline-end: 15px;" [src]="getImageUrl(driver.id!)" alt="{{driver.commercial_name}}"/>
            <div style="width:45vw;">
                <div style="display: flex;">
                    <span >{{driver.commercial_name}}</span>
                </div>
                <div>
                    <span style="width:45vw;">{{driver.mobile_number}}</span>
                </div>
            </div>
            <div class="offline-btn">
                {{driver.nearest_driver_orders.length}} {{'ORDERS' | translate}}
            </div>
            <div [class]="driver.online ? 'online-btn' : 'offline-btn'">
                {{(driver.online ? 'ONLINE' : 'OFFLINE') | translate}}
            </div>
            
            </div>
        <div style="background-color: white; display: flex; flex-direction: row; padding: 5px; gap: 5px; border-radius: 0 0 10px 10px;">
            <button (click)="showAgentOnMap(driver)" class="select-btn-outline" [ngStyle]="{'border-color':activeAgent && activeAgent.id == driver.id?'red':'#3ab600','color':activeAgent && activeAgent.id == driver.id?'red':'#3ab600'}">{{(activeAgent && activeAgent.id == driver.id?'DISMISS':'SHOW_ON_MAP') | translate}}</button>
            <button [disabled]="loading" (click)="currentOrder && currentOrder.assign_to_agent[0]==driver.id?selectDriverFromList(false):selectDriverFromList(driver.id)" class="select-btn" [ngStyle]="{'background':currentOrder && currentOrder.assign_to_agent[0]==driver.id && !loading?'red':!loading?'#3ab600':'grey'}">{{currentOrder && currentOrder.assign_to_agent[0]==driver.id?'DESELECT':'SELECT' | translate }}</button>
        </div>
        
    </div>
</div>
