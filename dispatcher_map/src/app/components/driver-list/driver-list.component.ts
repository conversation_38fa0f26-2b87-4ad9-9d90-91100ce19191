import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';

@Component({
  selector: 'app-driver-list',
  templateUrl: './driver-list.component.html',
  styleUrls: ['./driver-list.component.scss']
})
export class DriverListComponent implements OnInit,OnChanges {


  @Input() driversToSelect: any[] = []; 


  @Input()
  currentOrder!: any

  @Output() onClickDriver = new EventEmitter<any>();

  @Output() onClose = new EventEmitter<any>();
  @Output() onClickShowOnMap= new EventEmitter<any>();
  @Input() loading!: boolean;

  @Input() activeAgent!: any;

  @Input() currentStatus:any

  @Input() nextStatusList:any[]=[]

  @Input() hideStatusSelector:boolean=false

  isChooseStatusEnabled!:boolean

  selectedState: any;

  searchQuery: string = '';
  filteredDrivers: any[] = [];

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['driversToSelect'] && this.driversToSelect) {
      this.filteredDrivers = [...this.driversToSelect];
      this.filterDrivers(); 
    }
  }
  getImageUrl(userID: number) {
    return `${window.location.origin}/web/image/rb_delivery.user/${userID}/user_image/50x50`;
  }
  selectDriverFromList(driverId: any) {
    if(this.isChooseStatusEnabled){
      this.onClickDriver.emit({assign_to_agent:driverId,state:this.selectedState})
    }else{
      this.onClickDriver.emit({assign_to_agent:driverId})
    }
    

  }

  close(){
    this.onClose.emit()
    if(this.activeAgent){
      this.onClickShowOnMap.emit({agent:this.activeAgent})
    }
    
  }

  showAgentOnMap(agent:any){
    this.onClickShowOnMap.emit({agent:agent})
  }

  filterDrivers() {
    this.filteredDrivers = this.driversToSelect.filter(driver => {
      const name = driver.commercial_name || '';
      const mobile = driver.mobile_number || ''; 
  
      return name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
             mobile.toLowerCase().includes(this.searchQuery.toLowerCase());
    });
  }
}
