<div *ngIf="!loading"  class="fixed-to-top" style="justify-content: space-between; background-color: white; padding-block: 15px;">
    <h4>{{'SELECT_STATUS' | translate}}</h4>
    <button (click)="close()"><img class="icon" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/close.svg"/></button>
</div>
<div *ngIf="loading" class="fixed-to-top loading">{{'LOADING' | translate}}</div>
<div style="padding: 10px;">
    <div *ngFor="let status of nextStatusList" class="list-item">
        <div class="status-container">
            <span style="width:45vw;">{{status.title}}</span>
        </div>
        <div style="background-color: white; display: flex; flex-direction: row; padding: 5px; gap: 5px; border-radius: 0 0 10px 10px;">
            <button [disabled]="loading" (click)="selectStatusFromList(status.name)" class="select-btn" [ngStyle]="{'background':currentOrder.state==status.name && !loading?'red':!loading?'#3ab600':'grey'}">{{currentOrder.state==status.name?'DESELECT':'SELECT' | translate }}</button>
        </div>
        
    </div>
    <div class="fixed-to-top" style="font-size: 12px;" *ngIf="nextStatusList.length==0">
        {{'YOU_CANT_CHANGE_THE_STATUS' | translate}}
    </div>
</div>
