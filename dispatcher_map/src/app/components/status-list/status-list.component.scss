.status-container {
    padding: 10px;
}


.select-btn {
    width: 100px;
    height: 20px;
    border-radius: 10px;
    background-color: #3ab600;
    border: none;
    cursor: pointer;
    color: #ffffff;
}

.fixed-to-top {
    position: sticky;
    left: 0;
    top: 0;
    align-items: center;
    display: flex;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    button{
        all: unset;
        margin-inline: 16px;
        margin-top: 3px;
        cursor: pointer;
    }
    h4{
        all: unset;
        margin-inline-start:16px ;
    }
}

.loading{
    background-color: black;
    justify-content: center;
    color: white;
}

.list-item {
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #E3E3E3;
    justify-content: space-between;
    align-items: center;
}

.icon{
    width: 20px;
}