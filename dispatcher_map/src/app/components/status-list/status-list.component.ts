import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-status-list',
  templateUrl: './status-list.component.html',
  styleUrls: ['./status-list.component.scss']
})
export class StatusListComponent implements OnInit {


  @Input()
  driversToSelect!: any[]

  @Input()
  currentOrder!: any

  @Output() onClickStatus = new EventEmitter<any>();

  @Output() onClose = new EventEmitter<any>();
  @Input() loading!: boolean;

  @Input() currentStatus:any

  @Input() nextStatusList:any[]=[]

  selectedState: any;


  ngOnInit(): void {
  }

  selectStatusFromList(state:string) {
      this.onClickStatus.emit({state})
  }

  close(){
    this.onClose.emit()
  }

}
