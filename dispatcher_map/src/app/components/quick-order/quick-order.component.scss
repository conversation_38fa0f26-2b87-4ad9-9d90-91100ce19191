.quick-order-form {
    max-width: 570px;
    margin: 10px auto;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    
    .section {
        margin-bottom: 8px;
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }

        select, input, textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
    }

}

.is-invalid {
    border-color: red;
}

.error-message {
    color: red;
    font-size: 0.8em;
    margin-top: 5px;
    min-height: 10px;  
    margin-bottom: 5px;
}


.fixed-to-top {
    position: fixed;
    left: inherit;
    top: inherit;
    width: inherit;
    height: inherit;
    justify-content: center;
    align-items: center;
    display: flex;
    background-color: #00000050;
}
.quick-order-form h3 {
    margin: 10px 0;
    padding: 0;     
}

.button-group {
    display: flex;
    justify-content: space-between;
    gap: 20px;  
}

button {
    flex: 1;
    padding: 10px 10px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #0056b3;
}
