import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-quick-order',
  templateUrl: './quick-order.component.html',
  styleUrls: ['./quick-order.component.scss']
})
export class QuickOrderComponent implements OnInit {

  destroyed$ = new Subject<void>();
  @Input() areas: any[] = []
  @Input() allSubAreas: any[] = []
  subAreas: any[] = []
  @Input() users: any[] = []
  vehicleTypes: string[][] = [['car', 'CAR'],['motorcycle', 'MOTORCYCLE']]
  @Input() userRole!: string
  @Input() currentUserId !:number
  @Output() formSubmitted = new EventEmitter<any>();
  @Output() closeOrder = new EventEmitter<void>();
  @Output() fetchDeliveryCost = new EventEmitter<any>();
  @Input() deliveryCost = 0;
  @Input() showCustomerDetails = false;
  @Input() showPaymentSection = false;
  @Input() showNotesSection = false;
  @Input() showExtraCost = false;
  quickOrderForm!: FormGroup;
  errorMessageBusiness: string = '';
  errorMessageDriver: string = '';
  errorMessageVehicleTypes: string = '';
  errorMessageArea: string = '';
  errorMessageSubArea: string = '';


  constructor(
    private fb: FormBuilder,
    private translate:TranslateService
  ) { }

  ngOnInit() {
    let default_area = null 
    if(this.areas.length > 0){
      default_area = this.areas.filter(area=>area.is_default)
      if(default_area && default_area[0] && default_area[0].name){
        default_area = default_area[0].name
        this.filterSubAreas(false,default_area)
      }
      else{
        default_area = null
      }
    }
    let deafult_business = null
    if(this.userRole == 'rb_delivery.role_business' && this.currentUserId){
      deafult_business = this.users.filter(user=>user.id == this.currentUserId)[0].username
    }
    this.quickOrderForm = this.fb.group({
      customer_area: [default_area],
      customer_sub_area: [null],
      assign_to_business: [deafult_business],
      assign_to_agent: [null],
      vehicle_type: [null],
      customer_name: [''],
      customer_mobile: [''],
      cost: [''],
      extra_cost:[''],
      reference_id: [''],
      note: ['']
    });

    this.quickOrderForm.valueChanges.pipe(takeUntil(this.destroyed$)).subscribe(values=>{
      let senderId = this.users.filter(user=>user.username==values['assign_to_business'])[0]?.id | 0
      let toAreaId = this.areas.filter(area=>area.name==values['customer_area'])[0]?.id | 0
      let subAreaId = this.subAreas.filter(subArea=>subArea.name==values['customer_sub_area'])[0]?.id | 0
      if(values)
      this.computeDeliveryCost({'sender_id':senderId,'to_area_id':toAreaId,'sub_area_id':subAreaId})
    })

  }


  submit() {
    this.errorMessageBusiness = '';
    this.errorMessageDriver = '';
    this.errorMessageVehicleTypes = '';
    this.errorMessageArea = '';
    this.errorMessageSubArea = '';
  
    if (this.quickOrderForm.valid) {
      let businessId, driverId , vehicleType, areaId, subAreaId;  
      if (this.quickOrderForm.value &&this.quickOrderForm.value.assign_to_business){
        let senderSelected=this.users.filter(sender=>sender.role_code=="rb_delivery.role_business"&&sender.username==this.quickOrderForm.value.assign_to_business)
        if (senderSelected && senderSelected[0] && senderSelected[0].id){
          businessId=senderSelected[0].id;
        }
        else{
          this.errorMessageBusiness = 'INVALID_BUSINESS_SELECTION';

        }
      }  
      if (this.quickOrderForm.value &&this.quickOrderForm.value.assign_to_agent && this.userRole != 'rb_delivery.role_business'){
        let agentSelected=this.users.filter(sender=>sender.role_code=="rb_delivery.role_driver"&&sender.username==this.quickOrderForm.value.assign_to_agent)
        if (agentSelected && agentSelected[0] && agentSelected[0].id){
          driverId = agentSelected[0].id;
        }
        else{
          this.errorMessageDriver = 'INVALID_DRIVER_SELECTION';

        }
      }
      if (this.quickOrderForm.value &&this.quickOrderForm.value.vehicle_type){
        let vehicleTypeSelected=this.vehicleTypes.filter(vt=>this.quickOrderForm.value.vehicle_type == this.translate.instant(vt[1]))
        if (vehicleTypeSelected && vehicleTypeSelected[0]){
          vehicleType = vehicleTypeSelected[0][0];
        }
        else{
          this.errorMessageVehicleTypes = 'INVALID_VEHICLE_TYPE_SELECTION';

        }
      }   
      if (this.quickOrderForm.value &&this.quickOrderForm.value.customer_area){
        let areaSelected=this.areas.filter(area=>area.name==this.quickOrderForm.value.customer_area)
        if (areaSelected && areaSelected[0] && areaSelected[0].id){
          areaId = areaSelected[0].id;
        } 
        else{
          this.errorMessageArea = 'INVALID_AREA_SELECTION';

        }
      }
      if (this.quickOrderForm.value &&this.quickOrderForm.value.customer_sub_area){
        let subAreaSelected=this.subAreas.filter(sub_area=>sub_area.name==this.quickOrderForm.value.customer_sub_area)
        if (subAreaSelected && subAreaSelected[0] && subAreaSelected[0].id){
          subAreaId = subAreaSelected[0].id;
        } 
        else{
          this.errorMessageSubArea = 'INVALID_SUB_AREA_SELECTION';

        }
      }
  
      if(this.errorMessageBusiness || this.errorMessageDriver || this.errorMessageArea || this.errorMessageSubArea || this.errorMessageVehicleTypes){
        return;
      }
      let formData = { ...this.quickOrderForm.value };

      if(this.userRole == 'rb_delivery.role_business'){
        delete formData['assign_to_agent']
      }
      if (businessId) formData.assign_to_business = businessId;
      if (driverId) formData.assign_to_agent = driverId;
      if (vehicleType) formData.vehicle_type = vehicleType;
      if (areaId) formData.customer_area = areaId;
      if (subAreaId) formData.customer_sub_area = subAreaId;
      this.formSubmitted.emit(formData);

    }
  }

  filterSubAreas(event: any,area?:string) {
    let areaName = area?area:event&&event.target?event.target.value:''
    if(areaName){
      this.subAreas = this.allSubAreas.filter(area=>area.parent_id[1]==areaName)
    }
    else{
      this.subAreas = []
    }
    
    
  }
  
  computeDeliveryCost(values:any){
    this.fetchDeliveryCost.emit(values)
  }

  close() {
    this.closeOrder.emit();

  }
  resetForm() {
    this.quickOrderForm.reset();
  }

}
