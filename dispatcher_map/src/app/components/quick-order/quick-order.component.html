<form [formGroup]="quickOrderForm" class="quick-order-form" (ngSubmit)="submit()">
  <div class="section">
    <label>{{'SELECT_SENDER' | translate}}:</label>
    <div class="section">
      <input list="senders" formControlName="assign_to_business">
      <datalist id="senders">
        <ng-container *ngFor="let sender of users">
          <option *ngIf="sender.role_code=='rb_delivery.role_business'" [value]="sender.username"></option>
        </ng-container>
      </datalist>
    </div>
        
    <div *ngIf="errorMessageBusiness" class="error-message">
      {{ errorMessageBusiness | translate  }}
    </div>
  </div>

  <div class="section" *ngIf="userRole != 'rb_delivery.role_business'">
    <label>{{'SELECT_DRIVER' | translate}}:</label>
    <div class="section">
      <input list="drivers" formControlName="assign_to_agent">
      <datalist id="drivers">
        <ng-container *ngFor="let driver of users" >
            <option *ngIf="driver.role_code=='rb_delivery.role_driver'" [value]="driver.username"></option>
        </ng-container>
      </datalist>
    </div>

    <div *ngIf="errorMessageDriver" class="error-message">
      {{ errorMessageDriver | translate }}
    </div>
  </div>

  <div class="section">
    <label>{{'SELECT_VEHICLE_TYPE' | translate}}:</label>
    <div class="section">
      <input list="vehicleTypes" formControlName="vehicle_type">
      <datalist id="vehicleTypes">
        <ng-container *ngFor="let vehicleType of vehicleTypes" >
            <option [value]="vehicleType[1] | translate"></option>
        </ng-container>
      </datalist>
    </div>

    <div *ngIf="errorMessageVehicleTypes" class="error-message">
      {{ errorMessageVehicleTypes | translate }}
    </div>
  </div>
  
  <ng-container *ngIf="showCustomerDetails">
    <div class="section">
      <h3>{{'CUSTOMER_DETAILS' | translate}}</h3>
      <input type="text" placeholder="{{'CUSTOMER_NAME'|translate}}" formControlName="customer_name">
      <div *ngIf="quickOrderForm.get('customer_name')?.invalid && quickOrderForm.get('customer_name')?.touched"
        class="error-message">
        {{'CUSTOMER_NAME_REQUIRED' | translate}}
      </div>

      <input type="text" pattern="^[\u0621-\u064A\u0660-\u06690-9 ]+$" placeholder="{{'CUSTOMER_MOBILE'|translate}}" formControlName="customer_mobile">
      <div *ngIf="quickOrderForm.get('customer_mobile')?.invalid && quickOrderForm.get('customer_mobile')?.touched"
        class="error-message">
        {{'CUSTOMER_MOBILE_NUMBER_INVALID' | translate}}
      </div>
    </div> <!-- This closing div was missing -->
  </ng-container>
  <div class="section">
    <label>{{'SELECT_AREA' | translate}}</label>
    <input list="areas" formControlName="customer_area" (change)="filterSubAreas($event)">
    <datalist id="areas">
        <option *ngFor="let area of areas" [value]="area.name"></option>
    </datalist>
  </div>
  <div *ngIf="errorMessageArea" class="error-message">
    {{ errorMessageArea | translate}}
  </div>


  <div class="section">
    <label>{{'SELECT_SUB_AREA' | translate}}</label>
    <input list="sub_areas" formControlName="customer_sub_area">
    <datalist id="sub_areas">
        <option *ngFor="let subArea of subAreas" [value]="subArea.name"></option>
    </datalist>
  </div>
  <div *ngIf="errorMessageSubArea" class="error-message">
    {{ errorMessageSubArea | translate }}
  </div>

  <ng-container *ngIf="showPaymentSection">
    <div class="section">
      <h3>{{'PAYMENTS' | translate}}</h3>
      <input type="text" pattern="^[\u0621-\u064A\u0660-\u06690-9 ]+(\.[0-9]+)?$" placeholder="{{'COST'|translate}}" formControlName="cost">
      <div *ngIf="quickOrderForm.get('cost')?.invalid && quickOrderForm.get('cost')?.touched"
        class="error-message">
        {{'PAYMENTS_INVALID' | translate}}
      </div>
      <ng-container *ngIf="showExtraCost">
        <input type="text" pattern="^[\u0621-\u064A\u0660-\u06690-9 ]+(\.[0-9]+)?$" placeholder="{{'EXTRA_COST'|translate}}" formControlName="extra_cost">
        <div *ngIf="quickOrderForm.get('extra_cost')?.invalid && quickOrderForm.get('extra_cost')?.touched"
          class="error-message">
          {{'EXTRA_COST_INVALID' | translate}}
        </div>
      </ng-container>
      

    </div>
  </ng-container>

  <ng-container *ngIf="showNotesSection">
    <div class="section">
      <h3>{{'NOTES' | translate}}</h3>
      <textarea placeholder="{{'NOTE'|translate}}" formControlName="note"></textarea>
    </div>
  </ng-container>

  <div class="button-group" style="margin-bottom: 10px;">
    <div style="align-content:center">
      <h3>{{'DELIVERY_COST' | translate}} :</h3>
    </div>
    <div>
      <h4 style="font-size: 18px;
      font-family: Arial, sans-serif;
      color: white;
      background-color: #4CAF50;
      padding: 10px 20px;
      margin: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      text-align: center;">{{deliveryCost}}</h4>
    </div>
  </div>

  <div class="button-group">
    <button type="submit">{{'SUBMIT' | translate}}</button>
    <button style="background-color:#e7453d;" (click)="close()">{{'CLOSE' | translate}}</button>
  </div>
</form>
