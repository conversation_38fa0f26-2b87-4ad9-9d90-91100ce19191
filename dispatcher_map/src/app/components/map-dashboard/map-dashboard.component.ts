import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-map-dashboard',
  templateUrl: './map-dashboard.component.html',
  styleUrls: ['./map-dashboard.component.scss']
})
export class MapDashboardComponent implements OnInit{
  
  @Input()
  deliveryOverdueOrders!:any[]

  @Input()
  pickupOverdueOrders!:any[]

  @Output()
  onClickDeliveryOverdueOrders=new EventEmitter<any>()

  @Output()
  onClickPickupOverdueOrders=new EventEmitter<any>()

  deliveryOverdueOrdersLength: number=0;
  pickupOverdueOrdersLength: number=0;

  ngOnInit(): void {
  }

  emitPickupOverdueClick(){
    this.pickupOverdueOrdersLength=this.pickupOverdueOrders.length
    this.onClickPickupOverdueOrders.emit(this.pickupOverdueOrders[this.pickupOverdueOrders.length-1])
  }

  emitDeliveryOverdueClick(){
    this.deliveryOverdueOrdersLength=this.deliveryOverdueOrders.length
    this.onClickDeliveryOverdueOrders.emit(this.deliveryOverdueOrders[this.deliveryOverdueOrders.length-1])
  }

}
