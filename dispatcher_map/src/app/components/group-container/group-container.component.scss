.group-button{
    width: 100%;
    height: 50px;
    flex-shrink: 0;
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0px 2px 48px 0px rgba(0, 0, 0, 0.04);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    border-width: 0;
    cursor: pointer;
}

.checkbox-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 0.5em;
    border: 2px solid #007bff;
    border-radius: 4px;
    position: relative;
}

/* Hide the default checkbox */
.custom-checkbox input[type="checkbox"] {
  display: none;
}

/* Create a custom checkbox */
.custom-checkbox {
  display: inline-block;
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-size: 22px;
  user-select: none;
}

/* Style the box */
.custom-checkbox .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: transparent;
  border: 2px solid #D97348; /* Orange border color */
  border-radius: 3px;
}

/* When the checkbox is checked, display the checkmark */
.custom-checkbox input:checked + .checkmark::after {
  display: block;
}

/* Style the checkmark (a tick) */
.custom-checkbox .checkmark::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 0px;
  width: 7px;
  height: 14px;
  border: solid #D97348; /* Orange checkmark */
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
  display: none;
}

/* Hide the default checkbox */
.custom-checkbox input[type="checkbox"] {
    display: none;
  }
  
  /* Create a custom checkbox */
  .custom-checkbox {
    display: inline-block;
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    font-size: 22px;
    user-select: none;
  }
  
  /* Style the box */
  .custom-checkbox .checkmark {
    position: absolute;
    top: -15px;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: transparent;
    border: 2px solid #D97348; /* Orange border color */
    border-radius: 3px;
  }
  
  /* When the checkbox is checked, display the checkmark */
  .custom-checkbox input:checked + .checkmark::after {
    display: block;
  }
  
  /* Style the checkmark (a tick) */
  .custom-checkbox .checkmark::after {
    content: "";
    position: absolute;
    top: 3.5px;
    left: 5.5px;
    width: 2px;
    height: 6px;
    border: solid #D97348;
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);
    display: none;
  }
  
  /* Add CSS for indeterminate state */
  .custom-checkbox input:indeterminate + .checkmark::before {
    content: "";
    position: absolute;
    top: 7.5px;
    left: 4.5px;
    width: 7px;
    height: 2px;
    background-color: #d97348; /* Orange hyphen */
    display: block;
  }
  
  /* When in indeterminate state, hide the checkmark */
  .custom-checkbox input:indeterminate + .checkmark::after {
    display: none;
  }
  
