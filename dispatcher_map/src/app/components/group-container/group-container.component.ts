import { Component, EventEmitter, Input , Output } from '@angular/core';

@Component({
  selector: 'app-group-container',
  templateUrl: './group-container.component.html',
  styleUrls: ['./group-container.component.scss']
})
export class GroupContainerComponent {
  
  @Input() groupSet: any[] = [];
  @Input() level: number = 0;
  @Input() parentIndexes: number[] = [];
  @Input() parentGroup: any = false;
  @Input() parentOrders: any[] = [];
  @Input() groupingRecordsSet: any[] = [];
  @Input() order: any;
  @Input() aciveOrder: any;
  @Input() userRole!: string;
  @Input() nearestDriverTimeOut: number = 0;
  @Input() timeDurationConfig:number=0
  @Input() checked:boolean=false
  @Input() checkedOrderIds:number[]=[]

  @Output() showStatusList = new EventEmitter<any>();
  @Output() showDriverList = new EventEmitter<any>();
  @Output() showOrderOnMap = new EventEmitter<any>();
  @Output() showDriversInRange = new EventEmitter<any>();
  @Output() selectedGroupOrders = new EventEmitter<any[]>();
  @Output() onParentToggled = new EventEmitter<any>();

  selectedGroup: number = 0;


  toggleGroupSelection(group: any, event: any) {
    this.checked=event.target.checked
    if(this.checked){
      this.checkedOrderIds = this.checkedOrderIds.concat(this.getOrdersByField(group.id, group.field, this.parentOrders).map(el=>el.id as number))
    }else{

      this.checkedOrderIds = this.checkedOrderIds.filter(id=>!this.getOrdersByField(group.id, group.field, this.parentOrders).map(el=>el.id as number).includes(id))
    }
    this.selectedGroupOrders.emit(this.checkedOrderIds);
  }

  getParentIndexes(indexes:number[]){
    return this.parentIndexes.concat(indexes)
  }

  getNumberOfOrdersByField(fieldId: number, field: string, orders: any[]): number {
      return orders.filter(order => ( field[1] == 'many2one' ? order[field[0]][0] || false : field[1] == 'date' || field[1] == 'datetime' ? order[field[0]].split(' ')[0] : order[field[0]]) == fieldId ).length;
  }

  getOrdersByField(fieldId: number, field: string, orders: any[]): any[] {
    return orders.filter(order => (field[1] == 'many2one' ? order[field[0]][0] || false : field[1] == 'date' || field[1] == 'datetime' ? order[field[0]].split(' ')[0] : order[field[0]]) == fieldId )
  }

  getChecked(group:any){
    return this.getOrdersByField(group.id, group.field, this.parentOrders).map(el=>el.id as number).every((element: number) => this.checkedOrderIds.includes(element));
  }

  getCheckedOrdersLength(group:any){
    return this.getOrdersByField(group.id, group.field, this.parentOrders)
    .map(el => el.id as number)
    .filter((element: number) => this.checkedOrderIds.includes(element))
    .length;
  }

  


}
