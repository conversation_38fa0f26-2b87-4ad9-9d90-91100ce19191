<div [id]="level == 0?'groupContainer':false" style="padding-block: 1px; background: #F3E8E2;" [ngStyle]="level != 0 ?{'background-color': '#00000014', 'border-radius': '10px','margin-block': '15px'}:{'overflow': 'scroll', 'height': 'calc(100vh - 156px)'}">
    <div *ngFor="let group of groupSet; let i = index" style="width: 95%; margin: 15px auto;">
        <ng-container *ngIf="getNumberOfOrdersByField(group.id, group.field, parentOrders) > 0">

            <!-- Group Button -->
            <button 
                (click)="selectedGroup = selectedGroup === group.id ? 0 : group.id" 
                class="group-button">
                
                <!-- Group Checkbox -->
                <div style="overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;">
                    <label (click)="$event.stopPropagation()" class="custom-checkbox">
                      <input
                        type="checkbox"
                        #checkbox
                        [indeterminate]="getCheckedOrdersLength(group) > 0 && getCheckedOrdersLength(group) != getNumberOfOrdersByField(group.id, group.field, parentOrders)"
                        (click)="$event.stopPropagation()"
                        (change)="toggleGroupSelection(group, $event)"
                        [checked]="getChecked(group)"
                      />
                      <span class="checkmark"></span>
                    </label>
                  
                    <span style="font-weight: bold; font-size: 16px;">
                      {{ (group.label ? group.label : 'UNDEFINED') | translate }}
                      {{ !group.label ? group.field[2] : '' }}
                    </span>
                </div>
                <div style="display: flex; flex-direction: row; gap: 5px;">
                    <span style="font-weight: bold; font-size: 12px; align-self: center; color: white; background: #D97348; border-radius: 10px; padding: 2px 5px;white-space: nowrap;" *ngIf="getCheckedOrdersLength(group)>0">{{getCheckedOrdersLength(group)}} {{'SELECTED' | translate}}</span>
                    <span style="font-weight: bold; font-size: 16px; align-self: center; white-space: nowrap;">
                        {{ getNumberOfOrdersByField(group.id, group.field, parentOrders) }} {{ 'ORDERS' | translate }}
                    </span>
                    <img style="width: 25px;" *ngIf="selectedGroup === group.id" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/down.png"/>
                    <img style="width: 25px;" *ngIf="selectedGroup !== group.id" src="/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/right.png"/>
                </div>
            </button>
        
            <!-- Recursively display subgroups if any -->
            <ng-container *ngIf="selectedGroup === group.id && level < groupingRecordsSet.length - 1">
                <app-group-container 
                    [groupSet]="groupingRecordsSet[level + 1]" 
                    [level]="level + 1" 
                    [parentIndexes]="getParentIndexes([i].concat(parentIndexes))"
                    [parentGroup]="group"
                    [checked]="checked"
                    [checkedOrderIds]="checkedOrderIds"
                    [parentOrders]="getOrdersByField(group.id, group.field, parentOrders)"
                    [groupingRecordsSet]="groupingRecordsSet"
                    [aciveOrder]="aciveOrder"
                    [userRole]="userRole"
                    [nearestDriverTimeOut]="nearestDriverTimeOut"
                    (showStatusList)="showStatusList.emit($event)"
                    (showDriverList)="showDriverList.emit($event)"
                    (showOrderOnMap)="showOrderOnMap.emit($event)"
                    (showDriversInRange)="showDriversInRange.emit($event)"
                    (selectedGroupOrders)="selectedGroupOrders.emit($event);checkedOrderIds = $event"
                    >
                </app-group-container>
            </ng-container>
        
            <!-- Display orders if no further subgroups -->
            <div *ngIf="selectedGroup === group.id && level === groupingRecordsSet.length - 1" [ngStyle]="level == 0 ?{'background-color': '#00000014', 'border-radius': '10px','margin-block': '15px', 'padding-block': '1px'}:{}">
                <app-order-item 
                    *ngFor="let order of getOrdersByField(group.id, group.field, parentOrders)"
                    [order]="order"
                    [aciveOrder]="aciveOrder"
                    [userRole]="userRole"
                    [nearestDriverTimeOut]="nearestDriverTimeOut"
                    (showStatusList)="showStatusList.emit($event)"
                    (showDriverList)="showDriverList.emit($event)"
                    (showOrderOnMap)="showOrderOnMap.emit($event)"
                    (showDriversInRange)="showDriversInRange.emit($event);getChecked(group);"
                    [checked]="checked"
                    [checkedOrderIds]="checkedOrderIds"
                    (selectedGroupOrders)="selectedGroupOrders.emit($event);checkedOrderIds = $event"
                    >
                </app-order-item>
            </div>
        </ng-container>
    </div>
</div>
