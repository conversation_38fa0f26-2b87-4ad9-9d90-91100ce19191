import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subject, debounceTime } from 'rxjs';



@Component({
  selector: 'app-side-menu-component',
  templateUrl: './side-menu-component.component.html',
  styleUrls: ['./side-menu-component.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})

export class SideMenuComponentComponent implements OnInit , OnChanges{
  selectedGroup!: number;

  selectedSubGroup!: number;

  selectedSubArea: string | null = null;

  @Input()activeAgent:any

  @Input()activeRoute:any

  @Input()activeSender:any

  @Input()aciveOrder:any

  @Input()users: any[] = [];

  @Input()routes: any[] = [];

  @Input()doneRoutes: any[] = [];

  @Input() orders:any[] = [];

  @Input() statuses:any[]=[]

  @Input() subAreas:any[]=[]

  @Input() groupByFields:any[]=[]

  activeGroupByFields:any[]=[]

  activeFilters:any[]=[]

  @Input()sessionId!:string;

  @Input()timeDurationConfig:number=0

  @Input()maxNumberOfShipments:number=0

  @Input() userRole!:string

  @Input() currentOrder!:any


//   @Input() numberOfOrdersMap: any[] = [];

  @Input() filterTexts: string[] = [''];

  @Output() onClickAgent= new EventEmitter<any>();

  @Output() onClickAssignDriver= new EventEmitter<any>();

  @Output() onClickChooseStatus= new EventEmitter<any>();

  @Output() onClickShowOnMap= new EventEmitter<any>();

  @Output() onClickShowDriversInRange= new EventEmitter<any>();

  @Output() onSearch = new EventEmitter<any>();

  @Output() onActiveContentChange = new EventEmitter<any>();

  @Output() onAction = new EventEmitter<any>();

  @Output() onShowToast = new EventEmitter<any>();

  searchDebouncer: Subject<string> = new Subject<string>();


  @Output() onQuickOrderDismiss = new EventEmitter<boolean>();

  @Output() onClickFilter = new EventEmitter<string[]>();

  @Output() onGroupOrdersChecked = new EventEmitter<number[]>();

  @Input() dispatcherOrderFilters: { [key: string]: any } = {};
  @Input() orderDefaultFilters:{[key: string]: any} = {};
  
  showFilters = false;
  showContentSelector = false
  showActionSelector = false
  showGroupSelector = false
  showFilterSelector=false
  selectedFilters: string[] = ['DEFAULT'];
  selectedDriverFilter: string = 'ONLINE'
  selectedRouteFilter: string = 'ACTIVE'
  filterTypes: string[] = [];

  @Input() activeContent: string="orders";

  @Input() isFilteredOnOrders:boolean=false

  @Input() loading:boolean=false

  @Input() driversToSelect: any[]=[];

  groupingRecordsSet:any[] = []


  searchValue:string=''
  nearestDriverTimeOut: number=0
  isTimerStarted!: boolean;

  offlineDriversLength:number=0
  onlineDriversLength:number=0
  busyDriversLength:number=0
  drivers: any[]=[];
  activeContentLabel!: string;
  checkedOrders: number[]=[];
  showAgentList!: boolean;
  @Input() showDriverStatusInSideMenuForBusiness:boolean=false

  
  constructor(
    public translate:TranslateService
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['orders'] && this.orders) {
        
        if(this.activeGroupByFields.length==0){
            this.activeGroupByFields=this.groupByFields.filter(el=>el[3])
            if(this.activeGroupByFields.length==0){
                this.activeGroupByFields=[this.groupByFields[0]]
            }
        }
        this.groupRecords()
    }
  }

  ngOnInit(){

    this.activeFilters = Object.keys(this.orderDefaultFilters);
    if(this.userRole == 'rb_delivery.role_business'){
        this.showContent('senders')
    }
    else{
        this.showContent('orders')
        
    }
    this.searchListener()
    

    }
    toggleGroupOption(groupOption:any[]){
        if(this.activeGroupByFields.length>1 && this.activeGroupByFields.includes(groupOption)){
            this.activeGroupByFields = this.activeGroupByFields.filter(groupOpt=>groupOption!=groupOpt)
        }else if(!this.activeGroupByFields.includes(groupOption)){
            this.activeGroupByFields.push(groupOption)
        }
        else{
            this.onShowToast.emit({text:this.translate.instant('MUST_KEEP_AT_LEAST_ONE_GROUPING_OPTION'),type:'fail'})
        }
        this.groupRecords()
    }
    toggleFilterOption(filterOption:string){
        this.showFilterSelector=true
        if(this.activeFilters.includes(filterOption)){
            this.activeFilters = this.activeFilters.filter(filterOpt=>filterOption!=filterOpt)
            this.selectFilter(this.activeFilters)
        }else if(!this.activeFilters.includes(filterOption)){
            this.activeFilters.push(filterOption)
            this.selectFilter(this.activeFilters)
        }
        
    }
    groupRecords() {
        this.groupingRecordsSet = [];
        
        // Loop over each group field and generate the group sets dynamically
        for (let groupField of this.activeGroupByFields) {
            let records: any[] = [];
            let addedIds: number[] = [];
            let groupingRecords: any[] = [];
            
            // Collect unique records for the current group field
            this.orders.forEach(order => {
                if (groupField[1] == 'many2one' && !addedIds.includes(order[groupField[0]][0])) {
                    records.push(order[groupField[0]]);
                    addedIds.push(order[groupField[0]][0]);
                }
                else if (groupField[1] != 'many2one' && groupField[1] != 'date' && groupField[1] != 'datetime' && !addedIds.includes(order[groupField[0]])) {
                    records.push(order[groupField[0]]);
                    addedIds.push(order[groupField[0]]);
                }
                else if (groupField[1] == 'date' || groupField[1] == 'datetime' && !addedIds.includes(order[groupField[0]].split(' ')[0])){
                    records.push(order[groupField[0]].split(' ')[0]);
                    addedIds.push(order[groupField[0]].split(' ')[0]);
                }
            });
            
            // Prepare the group records for the current field
            for (let rec of records) {
                
                groupingRecords.push({ 
                    id: typeof rec == 'object' ? rec[0] : rec, 
                    label: typeof rec == 'object' ? rec[1] : rec, 
                    field: groupField
                });
            }
            // Push the current grouping records to the set
            this.groupingRecordsSet.push(groupingRecords);
            
            
        }
    }

    handleSelectedGroupOrders(orderIds: any[]) {
        this.showContentSelector=false;
        this.showGroupSelector = false;
        this.showActionSelector = false;
        this.checkedOrders = orderIds
        this.onGroupOrdersChecked.emit(this.checkedOrders)
    }

    assignAgentAction(event:any){
        this.onAction.emit({'action':'assign_agent','values':{'assign_to_agent':event.assign_to_agent}});
        this.checkedOrders=[]
        this.handleSelectedGroupOrders([])
        this.showAgentList = false
    }

    showAssignAgent(){
        this.onClickAssignDriver.emit()
        this.showAgentList = true
    }

    closeDriverList(){
        this.showAgentList=false
    }

    searchListener() {
        this.searchDebouncer.pipe(debounceTime(1000)).subscribe(value=>{
            this.onSearch.emit(value)
            this.drivers=[]
        })
    }
    search(event:any){
        if(event.target?.value){
            this.searchDebouncer.next(event.target?.value)
        }
        else{
            this.searchDebouncer.next('')
        }
    }
    getImageUrl(userID:number){
        return `${window.location.origin}/web/image/rb_delivery.user/${userID}/user_image/50x50`;
    }
  showContent(content: string): void {
    this.showContentSelector = false
    if(content=='senders'  && this.activeContentLabel != 'BUSINESSES'){
        this.filterTypes = ['DEFAULT', 'ALL', 'ONLINE_BUSINESSES', 'OFFLINE_BUSINESSES']
        this.activeContentLabel='BUSINESSES'
    }
    else if(content=='orders' && this.activeContentLabel != 'ORDERS'){
        this.filterTypes = ['TODAY_ORDERS', ...Object.keys(this.dispatcherOrderFilters)];
        this.activeContentLabel='ORDERS'
    }
    else if(content=='drivers' && this.activeContentLabel != 'DRIVERS'){
        this.activeContentLabel='DRIVERS'
        this.filterTypes = []
    }
    else if(content=='routes' && this.activeContentLabel != 'ROUTES'){
        this.activeContentLabel='ROUTES'
        this.filterTypes = []
    } else if (content=='all' && this.activeContentLabel != 'ALL'){
        this.activeContentLabel='ALL'
        this.filterTypes = []
    }
    else{
        return
    }
      this.activeContent = content;
      this.filterTexts=['']
      this.onActiveContentChange.emit(content)
      this.checkedOrders=[]
      this.searchValue=''
      this.selectedFilters = ['DEFAULT'];
      
      
  }



  


getKeys(obj: any): string[] {
    return Object.keys(obj);
}

showAgentOnMap(agent:any,showOnlyRelatedMarkers?:boolean){
    this.onClickShowOnMap.emit({agent:agent,showOnlyRelatedMarkers})
}
showRouteOnMap(route:any,showOnlyRelatedMarkers?:boolean){
    this.onClickShowOnMap.emit({route:route,showOnlyRelatedMarkers})
}
showSenderOnMap(sender:any,showOnlyRelatedMarkers?:boolean){
    this.onClickShowOnMap.emit({sender:sender,showOnlyRelatedMarkers})
}
showDriverList(order:any){
    this.onClickAssignDriver.emit(order)
}

showStatusList(order:any){
    this.onClickChooseStatus.emit(order)
}

showOrderOnMap(order:any,showOnlyRelatedMarkers?:boolean){
    this.onClickShowOnMap.emit({order:order,showOnlyRelatedMarkers})
}
toggleQuickOrder() {
    this.onQuickOrderDismiss.emit(true); 
}

startNearestDriverTimer(timer:number) {
    
    if(this.isTimerStarted || (!timer || timer==0)){
        return false
    }
    this.nearestDriverTimeOut=this.timeDurationConfig-timer
    let timerInterval = setInterval(() => {

        if (this.nearestDriverTimeOut > 0) {
            this.isTimerStarted=true
            this.nearestDriverTimeOut -= 1;
        }
        else{
            this.isTimerStarted=false
            clearInterval(timerInterval)
        }
    }, 1000);
    return true
}

getOrdersByField(fieldId: number, fieldName: string,orders=this.orders): any[] {
    return orders.filter(order => order[fieldName][0] === fieldId);
}

getNumberOfOrdersByField(fieldId: number, fieldName: string,orders=this.orders) {

    return orders.filter(order => order[fieldName][0] === fieldId).length;

}

editUser(user:any){
    window.open("http://"+window.location.hostname+"/web#id="+user.id+"&model=rb_delivery.user&view_type=form")
}

showDriversInRange(order:any){
    this.onClickShowDriversInRange.emit(new google.maps.LatLng({lat:Number(order.latitude),lng:Number(order.longitude)}))
}
  
  toggleFilterDropdown(): void {
    this.showFilters = !this.showFilters;
}

selectFilter(filterType: any[]): void {
    this.selectedFilters = filterType;
    this.filterTexts = filterType
    this.showFilters = false;
    this.onClickFilter.emit(filterType)
}

clearFilter() {
    this.selectedFilters = ['DEFAULT'];
    this.onClickFilter.emit(['DEFAULT'])
}


updateDriversLength(){
    this.filterDrivers('OFFLINE',false)
    this.filterDrivers('ONLINE',false)
    this.filterDrivers('BUSY',false)
    return true
}

clearOrdersFilter(){
    this.onActiveContentChange.emit('orders')
    this.checkedOrders=[]
}

filterDrivers(filterType:string,updatSelectedFilter=true){
    if(updatSelectedFilter){
        this.selectedDriverFilter=filterType
    }
    if (filterType == "OFFLINE") {
        this.offlineDriversLength=0
        this.drivers = this.users.filter(user=>{
            
            if(user.role_code=='rb_delivery.role_driver' && !user.online){
                this.offlineDriversLength+=1
                return true
            }
            else{
                return false
            }
        })
    }
    else if (filterType == "ONLINE") {
        this.onlineDriversLength=0
        this.drivers = this.users.filter(user=>{
            
            if(user.role_code=='rb_delivery.role_driver' && user.online && this.getNumberOfOrders(user,'agent')<this.maxNumberOfShipments){
                this.onlineDriversLength+=1
                return true
            }
            else{
                return false
            }
        })
    }
    else if (filterType == "BUSY") {
        this.busyDriversLength=0
        this.drivers = this.users.filter(user=>{
            if(user.role_code=='rb_delivery.role_driver' && user.online && this.getNumberOfOrders(user,'agent')>=this.maxNumberOfShipments){
                this.busyDriversLength+=1
                return true
            }
            else{
                return false
            }
        })
    }else{
        this.drivers=this.users.filter(user=>user.role_code=='rb_delivery.role_driver')
    }
    return true
}

getAgentMobile(order:any){
    if(order.agent_commercial_number){
        return order.agent_commercial_number
    }else if(order.agent_mobile_number){
        return order.agent_mobile_number
    }else{
        return false
    }
}

getNumberOfOrders(object:any,type:'business'|'agent'|'status'|'subArea'|'route'){
    if(type == 'business' ){
        return object['dispatcher_business_orders'].length
    }
    else if(type == 'agent' ){
        return object['nearest_driver_orders'].length
    }
    else if(type == 'status'){
        return this.orders.filter(order=>order.state_id[0] == object).length
    }
    else if(type == 'route'){
        if(object['order_ids'].length){
            return object['order_ids'].length
        }
        else{
            return typeof object['done_locations'] == 'string'? JSON.parse(object['done_locations']).length : object['done_locations'].length
        }
    }
    else if(type == 'subArea'){
        return this.orders.filter(order=>order.customer_sub_area[0] == object).length
    }
    return 0
}

getLocalDateTime(createDate:string) {
    const utcDateTime = new Date(createDate);
    const localTimeOffset = utcDateTime.getTimezoneOffset();
    const localDateTime = new Date(utcDateTime.getTime() - (localTimeOffset * 60000));
    const formattedDateTime = localDateTime.toLocaleString(undefined, {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false // Use 24-hour format
    });

    return formattedDateTime;
}

}
