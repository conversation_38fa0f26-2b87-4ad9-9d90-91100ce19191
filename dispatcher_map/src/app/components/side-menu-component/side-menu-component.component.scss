// General styles
.container {
    display: flex;
    flex-direction: column;
    background-color: #fcefe7;
    border-radius: 10px;
}

.flex-space-between {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-inline: 23px;
    padding-top: 22px;
}

.app-driver-container{
    
    flex-direction: column;
    display: flex;
    position: absolute;
    left: 570px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 0 10px #ccc;
    width: 20vw;
    overflow: scroll;
    max-height: 40vh;
    top: 247px;

}

.button-base {
    flex: 1;
    height: 50px !important;
    border-radius: 10px;
    border: none;
    cursor: pointer;
    background-color: #fcefe7;
    height: 4rem;
}

.button-orange {
    background-color: #e55604;
    color: #ffffff;
}

// Dropdown styles
.dropdown-container {
    margin: 10px 0;
    position: relative;

}
.filter-btn-inside-input {
    position: absolute;
    right: 28px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
  }
.dropdown-btn {
    width: 100%;
    height: 44px;
    border-radius: 10px;
    padding-inline: 10px;
    background-color: #fff;
    border: none;
    cursor: pointer;
}

// Input styles
.input-filter {
    margin-top: 20px;
    padding: 5px;
    width: 100%;
    box-sizing: border-box;
}

// Courier styles
.user-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 0;
    padding: 10px;
}

.order-item-content {
    display: grid;
    padding: 10px;
    background-color: #ffff;
    grid-template-columns: 35% 30% 35%;
}

.order-item-header{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-inline: 10px;
    background: #e55604;
}


.courier-img {
    width: 570px;
    height: 70px;
    border-radius: 10px;
}

.select-btn {
    width: 100%;
    height: 20px;
    border-radius: 10px;
    font-size: 13px;
    background-color: #3ab600;
    border: none;
    cursor: pointer;
    color: #ffffff;
}

.button-base.active {
    background-color: #ffffff;
    color: #e55604;
    /* or any other color you prefer */
}

// side-menu container
.side-menu.container {
    position: absolute;
    height: 100vh;
    width: 570px;
    left: 0;
    direction: ltr;
}


.online-btn {
    color: #fff;
    /* display: flex; */
    background-color: green;
    width: 14vw;
    border-radius: 15px;
    margin: 0px 6px;
    padding-inline: 5px;
}

.busy-btn {
    color: #fff;
    background-color: orange;
    width: 14vw;
    border-radius: 15px;
    margin: 0px 6px;
    padding-inline: 5px;
}

.offline-btn {
    color: #fff;
    /* display: flex; */
    background-color: red;
    width: 14vw;
    border-radius: 15px;
    margin: 0px 6px;
    padding-inline: 5px;
}

.list-item {
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 10px #ccc;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
    background-color: white;
}
.dropdown-container {
    position: relative;
}

.dropdown-btn {
    width: 100%;
    padding-inline-end: 15px; 
}

.filter-dropdown {
    position: absolute;
    inset-inline-end: 10px;
    top: 100%;
    list-style: none;
    padding: 0;
    margin: 0;
    z-index: 1;
    width: 40%;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), /* Basic shadow */
    0 1px 2px rgba(0, 0, 0, 0.24), /* Slightly darker shadow */
    0 2px 4px rgba(0, 0, 0, 0.18); /* Larger, softer shadow */
    padding: 10px;
}

.filter-dropdown::before {
    content: '';
    position: absolute;
    top: -10px; /* Adjust based on the desired tail position */
    right: 20px; /* Aligns the tail with the right edge */
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid white;
}

.filter-dropdown li {
    padding: 5px;
    cursor: pointer;
    background-color: white;
    text-align: start;
    margin: 5px 0;
    border-radius: 5px;
}

.filter-dropdown li:hover {
    background: #f3f3f3;
}


.applied-filter .filter-icon {
    color: red;  
}

.clear-filter-btn {
    position: absolute;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: bold;
    color: white;
    inset-inline-end: 52px;

}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    padding-inline: 23px;
    margin-bottom: 10px;
}
.input-with-filter{
    width: 100%;
    padding-right: 50px; // Adjust
}
.dropdown-btn {
    flex-grow: 1; 
    border: none;
    outline: none;
    padding-inline-end: 50px; 
}

.filter-filter{
    position: absolute;
    inset-inline-end: 30px;
    background: none;
    border: none;
    cursor: pointer;
}

.x-button{
    cursor: pointer;
    background: red;
    display: inline-block;
    border-radius: 50%;
    width: 15px;
    height: 15px;
}

.applied-filter .filter-icon img {
    filter: hue-rotate(0deg) brightness(50%); 
}


.item-row{
    display: flex;
    font-size: 14px;
    margin-block: 5px;
    align-self: center;
}

.row-col{
    display: flex;
    font-size: 14px;
    margin-block: 5px;
    gap: 5px;
    align-items: center;
}

.item-col{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 14px;
}

.icon{
    width: 24px;
}

.fade_animation{
    animation: fadeOut 0.5s forwards;
}


@keyframes fadeOut {

    from {
        opacity: 100;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(400px);
    }
}


.active-content-dropdown {
    background-color: white;
    list-style: none;
    padding: 10px;
    margin: 0;
    border-radius: 10px;
    border: 1px solid #E3E3E3;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 75px;
    left: 23px;
    z-index: 1000;
    width: 200px; /* Adjust width as needed */
}
.active-action-dropdown {
    background-color: white;
    list-style: none;
    padding: 10px;
    margin: 0;
    border-radius: 10px;
    border: 1px solid #E3E3E3;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 75px;
    right: 42px;
    z-index: 1000;
    width: 200px; /* Adjust width as needed */
}

.active-group-dropdown {
    background-color: white;
    list-style: none;
    padding: 10px;
    margin: 0;
    border-radius: 10px;
    border: 1px solid #E3E3E3;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 75px;
    right: 23px;
    z-index: 1000;
    width: 200px; /* Adjust width as needed */
}
.active-filter-dropdown {
    background-color: white;
    list-style: none;
    padding: 10px;
    margin: 0;
    border-radius: 10px;
    border: 1px solid #E3E3E3;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 33px;
    right: 0px;
    z-index: 1000;
    width: 200px;
}

li {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

li:hover {
    background-color: #f0f0f0; /* Hover effect */
}

.button-select {
    display: flex;
    align-items: center;
    border-radius: 10px;
    background-color: white;
    color: black;
    border: 1px solid #E3E3E3;
    font-size: 16px;
    cursor: pointer;
    position: relative;
    transition: box-shadow 0.3s ease;
}

.button-select:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.button-dropdown {
    display: flex;
    align-items: center;
    justify-content: space-around;
    min-width: 150px;
    padding: 0 !important;
}

.button-icon {
    width: 24px;
    height: 24px;
}

.dropdown-arrow {
    margin-left: 8px;
    font-size: 14px;
    color: black;
}

.separator {
    width: 1px;
    height: 80%;
    background-color: #E0E0E0;
    margin-inline: 15px;
    margin-block: auto;
}

.tag-right{
    position: absolute;
    right: 10px;
    border-radius: 50%;
    background-color: coral;
    width: 18px;
    height: 18px;
    text-align: center;
    color: white;
}

.disabled {
    background-color: #ccc; /* Lighter gray to indicate disabled */
    color: #999;
    cursor: not-allowed;
    opacity: 0.7;
}

