<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="45" height="45" viewBox="2484.44 2208.89 1080 1080" xml:space="preserve">
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="55f2d65d-7d5a-47cd-8be8-c6da4b2aa905"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(72,177,28); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(0 0 0 0 0 0)" id="5b525494-1039-443f-8e22-99395f00bf8f"  >
</g>
<g transform="matrix(16.62 0 0 16.62 -7402.34 -7370.08)"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-opacity: 0; fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  x="-50" y="-50" rx="0" ry="0" width="100" height="100" />
</g>
<g transform="matrix(16.62 0 0 16.62 741.46 773.72)"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(16.62 0 0 16.62 -8241.76 -8209.27)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,128,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-31.99, -32)" d="M 54.987 22.533 C 52.187 10.213 41.44 4.667 32 4.667 C 32 4.667 32 4.667 31.973 4.667 C 22.56 4.667 11.787 10.187 8.987 22.507 C 5.867 36.267 20.244 46.119 27.871 53.452 C 30.698 56.172 31.839 59.319 32 59.333 C 32.161 59.347 33.276 55.945 36.076 53.225 C 43.703 45.892 58.107 36.293 54.987 22.533 Z M 31.641 41.465 C 25.121 41.447 16.996 35.851 16.891 26.345 C 16.786 16.839 25.32 11.976 31.641 11.996 C 37.962 12.016 46.68 16.254 46.978 26.345 C 47.276 36.436 38.161 41.483 31.641 41.465 Z" stroke-linecap="round" />
</g>
<g transform="matrix(16.62 0 0 16.62 -8357.9 -8292.42)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(15,15,15); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-25, -27)" d="M 26.9999 18 C 26.4476 18 25.9999 18.4477 25.9999 19 C 25.9999 19.5523 26.4476 20 26.9999 20 L 28.4088 20 C 28.8745 20 29.2785 20.3214 29.3832 20.7751 L 29.6978 22.1384 L 22.6039 22.9266 C 22.2496 22.966 21.891 22.9419 21.5451 22.8554 L 16.3241 21.5502 C 14.6356 21.128 13 22.4051 13 24.1455 C 13 25.407 13.8812 26.4971 15.1147 26.7614 L 19.8505 27.7762 C 20.0629 27.8217 20.2522 27.9345 20.3925 28.0944 L 19.4433 28.8327 C 18.7675 28.3105 17.92 27.9999 16.9999 27.9999 C 14.7908 27.9999 13 29.7908 13 31.9999 C 13 34.209 14.7908 35.9998 16.9999 35.9998 C 19.2091 35.9998 20.9999 34.209 20.9999 31.9999 C 20.9999 31.4352 20.8829 30.8979 20.6719 30.4108 L 21.331 29.8981 L 22.0527 31.3414 C 22.5609 32.3578 23.5997 32.9998 24.736 32.9998 L 25.2638 32.9998 C 26.4001 32.9998 27.4389 32.3578 27.9471 31.3415 L 30.5981 26.0395 L 31.1544 28.4501 C 29.8741 29.1171 28.9999 30.4564 28.9999 31.9999 C 28.9999 34.209 30.7907 35.9998 32.9998 35.9998 C 35.209 35.9998 36.9998 34.209 36.9998 31.9999 C 36.9998 29.8254 35.2646 28.0561 33.1033 28.0012 L 32.8723 26.9999 L 33.5277 26.9999 C 33.6829 26.9999 33.836 27.0361 33.9749 27.1055 L 35.5526 27.8943 C 36.0466 28.1413 36.6472 27.9411 36.8942 27.4471 C 37.1412 26.9532 36.941 26.3525 36.447 26.1055 L 34.8693 25.3167 C 34.4527 25.1084 33.9934 24.9999 33.5277 24.9999 L 32.4107 24.9999 L 32.18 23.9999 L 32.9998 23.9999 C 33.5521 23.9999 33.9998 23.5522 33.9998 22.9999 C 33.9998 22.4477 33.5521 21.9999 32.9998 21.9999 L 31.7184 21.9999 L 31.332 20.3254 C 31.0179 18.9642 29.8058 18 28.4088 18 L 26.9999 18 Z M 31.6355 30.535 L 32.0254 32.2247 C 32.1496 32.7629 32.6865 33.0984 33.2247 32.9743 C 33.7628 32.8501 34.0984 32.3132 33.9742 31.775 L 33.5841 30.0847 C 34.4046 30.3348 35.0016 31.0976 35.0016 31.9999 C 35.0016 33.1054 34.1054 34.0016 32.9998 34.0016 C 31.8943 34.0016 30.9981 33.1054 30.9981 31.9999 C 30.9981 31.4215 31.2433 30.9005 31.6355 30.535 Z M 16.9999 29.9981 C 17.2672 29.9981 17.5223 30.0505 17.7553 30.1455 L 16.386 31.2106 C 15.9501 31.5496 15.8715 32.1779 16.2106 32.6139 C 16.5497 33.0498 17.1779 33.1283 17.6139 32.7893 L 18.9829 31.7244 C 18.9953 31.8145 19.0017 31.9064 19.0017 31.9999 C 19.0017 33.1054 18.1055 34.0016 16.9999 34.0016 C 15.8944 34.0016 14.9982 33.1054 14.9982 31.9999 C 14.9982 30.8943 15.8944 29.9981 16.9999 29.9981 Z M 14.9999 24.1455 C 14.9999 23.7062 15.4128 23.3839 15.839 23.4904 L 21.06 24.7957 C 21.6365 24.9398 22.2342 24.98 22.8248 24.9144 L 29.2834 24.1968 L 26.1582 30.447 C 25.9888 30.7858 25.6426 30.9998 25.2638 30.9998 L 24.736 30.9998 C 24.3572 30.9998 24.0109 30.7858 23.8415 30.447 L 22.3242 27.4124 C 21.9167 26.5973 21.1607 26.0116 20.2696 25.8206 L 15.5338 24.8058 C 15.2224 24.7391 14.9999 24.464 14.9999 24.1455 Z" stroke-linecap="round" />
</g>
<g transform="matrix(16.62 0 0 16.62 -8241.76 -8209.27)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,128,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-31.99, -32)" d="M 54.987 22.533 C 52.187 10.213 41.44 4.667 32 4.667 C 32 4.667 32 4.667 31.973 4.667 C 22.56 4.667 11.787 10.187 8.987 22.507 C 5.867 36.267 20.244 46.119 27.871 53.452 C 30.698 56.172 31.839 59.319 32 59.333 C 32.161 59.347 33.276 55.945 36.076 53.225 C 43.703 45.892 58.107 36.293 54.987 22.533 Z M 31.641 41.465 C 25.121 41.447 16.996 35.851 16.891 26.345 C 16.786 16.839 25.32 11.976 31.641 11.996 C 37.962 12.016 46.68 16.254 46.978 26.345 C 47.276 36.436 38.161 41.483 31.641 41.465 Z" stroke-linecap="round" />
</g>
<g transform="matrix(16.62 0 0 16.62 -8357.9 -8292.42)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(15,15,15); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-25, -27)" d="M 26.9999 18 C 26.4476 18 25.9999 18.4477 25.9999 19 C 25.9999 19.5523 26.4476 20 26.9999 20 L 28.4088 20 C 28.8745 20 29.2785 20.3214 29.3832 20.7751 L 29.6978 22.1384 L 22.6039 22.9266 C 22.2496 22.966 21.891 22.9419 21.5451 22.8554 L 16.3241 21.5502 C 14.6356 21.128 13 22.4051 13 24.1455 C 13 25.407 13.8812 26.4971 15.1147 26.7614 L 19.8505 27.7762 C 20.0629 27.8217 20.2522 27.9345 20.3925 28.0944 L 19.4433 28.8327 C 18.7675 28.3105 17.92 27.9999 16.9999 27.9999 C 14.7908 27.9999 13 29.7908 13 31.9999 C 13 34.209 14.7908 35.9998 16.9999 35.9998 C 19.2091 35.9998 20.9999 34.209 20.9999 31.9999 C 20.9999 31.4352 20.8829 30.8979 20.6719 30.4108 L 21.331 29.8981 L 22.0527 31.3414 C 22.5609 32.3578 23.5997 32.9998 24.736 32.9998 L 25.2638 32.9998 C 26.4001 32.9998 27.4389 32.3578 27.9471 31.3415 L 30.5981 26.0395 L 31.1544 28.4501 C 29.8741 29.1171 28.9999 30.4564 28.9999 31.9999 C 28.9999 34.209 30.7907 35.9998 32.9998 35.9998 C 35.209 35.9998 36.9998 34.209 36.9998 31.9999 C 36.9998 29.8254 35.2646 28.0561 33.1033 28.0012 L 32.8723 26.9999 L 33.5277 26.9999 C 33.6829 26.9999 33.836 27.0361 33.9749 27.1055 L 35.5526 27.8943 C 36.0466 28.1413 36.6472 27.9411 36.8942 27.4471 C 37.1412 26.9532 36.941 26.3525 36.447 26.1055 L 34.8693 25.3167 C 34.4527 25.1084 33.9934 24.9999 33.5277 24.9999 L 32.4107 24.9999 L 32.18 23.9999 L 32.9998 23.9999 C 33.5521 23.9999 33.9998 23.5522 33.9998 22.9999 C 33.9998 22.4477 33.5521 21.9999 32.9998 21.9999 L 31.7184 21.9999 L 31.332 20.3254 C 31.0179 18.9642 29.8058 18 28.4088 18 L 26.9999 18 Z M 31.6355 30.535 L 32.0254 32.2247 C 32.1496 32.7629 32.6865 33.0984 33.2247 32.9743 C 33.7628 32.8501 34.0984 32.3132 33.9742 31.775 L 33.5841 30.0847 C 34.4046 30.3348 35.0016 31.0976 35.0016 31.9999 C 35.0016 33.1054 34.1054 34.0016 32.9998 34.0016 C 31.8943 34.0016 30.9981 33.1054 30.9981 31.9999 C 30.9981 31.4215 31.2433 30.9005 31.6355 30.535 Z M 16.9999 29.9981 C 17.2672 29.9981 17.5223 30.0505 17.7553 30.1455 L 16.386 31.2106 C 15.9501 31.5496 15.8715 32.1779 16.2106 32.6139 C 16.5497 33.0498 17.1779 33.1283 17.6139 32.7893 L 18.9829 31.7244 C 18.9953 31.8145 19.0017 31.9064 19.0017 31.9999 C 19.0017 33.1054 18.1055 34.0016 16.9999 34.0016 C 15.8944 34.0016 14.9982 33.1054 14.9982 31.9999 C 14.9982 30.8943 15.8944 29.9981 16.9999 29.9981 Z M 14.9999 24.1455 C 14.9999 23.7062 15.4128 23.3839 15.839 23.4904 L 21.06 24.7957 C 21.6365 24.9398 22.2342 24.98 22.8248 24.9144 L 29.2834 24.1968 L 26.1582 30.447 C 25.9888 30.7858 25.6426 30.9998 25.2638 30.9998 L 24.736 30.9998 C 24.3572 30.9998 24.0109 30.7858 23.8415 30.447 L 22.3242 27.4124 C 21.9167 26.5973 21.1607 26.0116 20.2696 25.8206 L 15.5338 24.8058 C 15.2224 24.7391 14.9999 24.464 14.9999 24.1455 Z" stroke-linecap="round" />
</g>
<g transform="matrix(18.83 0 0 18.83 3018.73 2756.79)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(219,157,44); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-31.99, -32)" d="M 54.987 22.533 C 52.187 10.213 41.44 4.667 32 4.667 C 32 4.667 32 4.667 31.973 4.667 C 22.56 4.667 11.787 10.187 8.987 22.507 C 5.867 36.267 20.244 46.119 27.871 53.452 C 30.698 56.172 31.839 59.319 32 59.333 C 32.161 59.347 33.276 55.945 36.076 53.225 C 43.703 45.892 58.107 36.293 54.987 22.533 Z M 31.641 41.465 C 25.121 41.447 16.996 35.851 16.891 26.345 C 16.786 16.839 25.32 11.976 31.641 11.996 C 37.962 12.016 46.68 16.254 46.978 26.345 C 47.276 36.436 38.161 41.483 31.641 41.465 Z" stroke-linecap="round" />
</g>
<g transform="matrix(8.34 0 0 8.34 3018.78 2656.59)" id="d2e22c2a-3b1d-441e-bb8b-d407643d762b"  >
<circle style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(219,157,44); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  cx="0" cy="0" r="35" />
</g>
<g transform="matrix(0.51 0 0 0.51 3016.4 2663.23)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1.56 0 0 1.56 -82.97 100)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(224,183,110); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="173.91,-132.12 53.1,192 -173.91,64.01 -173.91,-192 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 0 0)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(244,206,143); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="0,-256 -227,-128.01 0,0 0,256 227,128.01 227,-128.01 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 177.35 100)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(242,202,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-113.5,-64 -113.5,192 113.5,64.01 113.5,-192 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -109.06 58.61)"  >
<polyline style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(140,193,234); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-11.77,172.5 -11.77,-83.51 81.99,-136.38 82,-136.38 82,-136.38 5.2,-136.38 5.59,-172.5 5.6,-172.5 -82,-123.11 -82,132.01 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 -182.32 97.2)"  >
<polyline style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(121,177,214); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="35.12,147.81 35.12,-108.2 -35.12,-147.81 -35.12,107.32 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 99.9 -264.59)"  >
<polyline style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(140,193,234); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="11.77,-44.5 -81.99,8.37 -82,8.37 -82,8.37 -5.2,8.37 -5.59,44.5 -5.6,44.5 82,-4.89 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 131.86 139.63)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(137,134,134); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="34.48,-53.17 0.54,-76.76 -34.48,-14.4 -8.7,-28.89 -8.7,76.76 9.24,66.64 9.24,-38.98 " />
</g>
		<g transform="matrix(1.56 0 0 1.56 232.5 82.12)"  >
<polygon style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(137,134,134); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  points="-34.48,53.17 -0.54,76.76 34.48,14.4 8.7,28.89 8.7,-76.76 -9.25,-66.64 -9.25,38.98 " />
</g>
</g>
</g>
</svg>