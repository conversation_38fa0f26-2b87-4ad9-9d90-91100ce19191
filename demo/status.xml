<odoo>
    <data>
        <record id="status_route_collection_done" model="rb_delivery.status">
            <field name="name">done_route</field>
             <field name="title">Done</field>
            <field name="title_ar">تم</field>
            <field name="description"></field>
            <field name="sequence">1</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">route_collection</field>
            <field name="lock_status">True</field>
            <field name="pass_lock_allowed_group_ids" eval="[(6,0,[ref('rb_delivery.role_configuration_manager')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_driver'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager'),ref('rb_delivery.role_data_entry')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
        </record>

        <record id="status_route_collection_open" model="rb_delivery.status">
            <field name="name">open_route</field>
             <field name="title">Open</field>
            <field name="title_ar">مفتوح</field>
            <field name="description"></field>
            <field name="sequence">1</field>
            <field name="default">True</field>
            <field name="status_type">olivery_collection</field>
            <field name="collection_type">route_collection</field>
            <field name="lock_status">True</field>
            <field name="next_state_ids" eval="[(6, 0,[ref('olivery_dispatcher_map.status_route_collection_done')])]" />
            <field name="group_ids" eval="[(6,0,[ref('rb_delivery.role_driver'),ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_manager')])]"/>
            <field name="role_action_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
            <field name="role_segment_status_ids" eval="[(6,0,[ref('rb_delivery.role_super_manager'),ref('rb_delivery.role_driver'),ref('rb_delivery.role_manager')])]" />
        </record>
    </data>
</odoo>