<odoo >
    <data noupdate="1">
        <record id="client_configuration_type_dispatcher_map" model="rb_delivery.client_configuration_type">
            <field name="name">Dispatcher map</field>
            <field name="description">Dispatcher map configurations</field>
        </record>

        <record id="client_configuration_type_route_optimization" model="rb_delivery.client_configuration_type">
            <field name="name">Route Optimization</field>
            <field name="description">Route Optimization configurations</field>
        </record>

        <record id="client_configuration_default_status_olivery_dispatcher_map" model="rb_delivery.client_configuration">
            <field name="key">default_status_olivery_dispatcher_map</field>
            <field name="value">False</field>
            <field name="description">the statuses that are shown in dispatcher map</field>
            <field name="status" eval="[(6, 0,[
            ref('rb_delivery.status_waiting'),
            ref('rb_delivery.status_picked_up'),
            ref('rb_delivery.status_picking_up'),
            ref('rb_delivery.status_in_progress'),
            ref('rb_delivery.status_reschedule'),
            ref('rb_delivery.status_rejected'),
            ref('rb_delivery.status_stuck'),
            ref('rb_delivery.status_delivered')
            ])]" />
            <field name="related_to_status">True</field>
            <field name="platform_type" >web</field>
            <field name="configuration_type_id" ref="olivery_dispatcher_map.client_configuration_type_dispatcher_map"/>
        </record>  
        <record id="client_configuration_show_customer_details_on_dispatcher_quick_order" model="rb_delivery.client_configuration">
            <field name="key">show_customer_details_on_dispatcher_quick_order</field>
            <field name="value">True</field>
            <field name="platform_type" >web</field>
            <field name="description">Show customer details in quick order in dispatcher map</field>
            <field name="configuration_type_id" ref="olivery_dispatcher_map.client_configuration_type_dispatcher_map"/>
        </record> 
        <record id="client_configuration_show_payment_section_on_dispatcher_quick_order" model="rb_delivery.client_configuration">
            <field name="key">show_payment_section_on_dispatcher_quick_order</field>
            <field name="value">True</field>
            <field name="platform_type" >web</field>
            <field name="description">Show payment section in quick order in dispatcher map</field>
            <field name="configuration_type_id" ref="olivery_dispatcher_map.client_configuration_type_dispatcher_map"/>
        </record> 
        <record id="client_configuration_show_extra_cost_on_dispatcher_quick_order" model="rb_delivery.client_configuration">
            <field name="key">show_extra_cost_on_dispatcher_quick_order</field>
            <field name="value">True</field>
            <field name="platform_type" >web</field>
            <field name="description">Show extra cost field in quick order in dispatcher map</field>
            <field name="configuration_type_id" ref="olivery_dispatcher_map.client_configuration_type_dispatcher_map"/>
        </record> 
        <record id="client_configuration_show_note_section_on_dispatcher_quick_order" model="rb_delivery.client_configuration">
            <field name="key">Show_note_section_on_dispatcher_quick_order</field>
            <field name="value">True</field>
            <field name="platform_type" >web</field>
            <field name="description">Show notes section in quick order in dispatcher map</field>
            <field name="configuration_type_id" ref="olivery_dispatcher_map.client_configuration_type_dispatcher_map"/>
        </record>
        <record id="client_configuration_match_driver_color_to_last_order_status" model="rb_delivery.client_configuration">
            <field name="key">match_driver_color_to_last_order_status</field>
            <field name="value">False</field>
            <field name="platform_type" >web</field>
            <field name="description">match driver color to last order status in dispatcher map</field>
            <field name="configuration_type_id" ref="olivery_dispatcher_map.client_configuration_type_dispatcher_map"/>
        </record> 
        <record id="client_configuration_show_route_optimization_for_driver" model="rb_delivery.client_configuration">
            <field name="key">show_route_optimization_for_driver</field>
            <field name="value">False</field>
            <field name="platform_type" >mobile</field>
            <field name="description">show the route optimization button for driver on mobile</field>
            <field name="configuration_type_id" ref="olivery_dispatcher_map.client_configuration_type_route_optimization"/>
        </record> 

        <record id="client_configuration_show_driver_status_in_side_menu_for_business" model="rb_delivery.client_configuration">
            <field name="key">show_driver_status_in_side_menu_for_business</field>
            <field name="value">True</field>
            <field name="platform_type" >web_mobile</field>
            <field name="description">Controls visibility of driver status in the Dispatcher Map side menu for Business users. Enabled by default.</field>
            <field name="configuration_type_id" ref="olivery_dispatcher_map.client_configuration_type_route_optimization"/>
        </record> 

    </data>
</odoo>