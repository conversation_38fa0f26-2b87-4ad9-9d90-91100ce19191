<odoo>
    <data>
        <record id="create_route_error_1700" model="rb_delivery.error_log">
            <field name="error_title">Error Creating route collection</field>
            <field name="name">{exist_message}.</field>
            <field name="what_to_do">Please remove the orders from the route collection first.</field>
            <field name="error_code">1700</field>
            <field name="error_type">warning</field>
            <field name="model_name">olivery_paths.create_route</field>
        </record>
    </data>
</odoo>