<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data noupdate="1">

        <record id="rb_delivery_driver_access_routes" model="ir.rule">
            <field name="name">driver see only his record for routes</field>
            <field name="model_id" ref="model_rb_delivery_routes"/>
            <field name="domain_force">[('create_uid' ,'=', user.id)]</field>
            <field name="groups" eval="[(4,ref('rb_delivery.role_manager'))]"/>
        </record>

    </data>
</odoo>
