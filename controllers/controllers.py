# -*- coding: utf-8 -*-
from odoo import http,_

from odoo.http import request, Response

from openerp import http

import json

class olivery_dispatcher_map(http.Controller):
     

	@http.route("/users_stream/<string:context>", auth='user', cors='*', csrf=False, type='http', website='true') 
	def users_stream(self,context):
		context = json.loads(context)
		user_ref=request.env['rb_delivery.user']
		return Response(user_ref.with_context(context).event_stream(), mimetype="text/event-stream")

		
	@http.route("/orders_stream/<string:context>", auth='user', cors='*', csrf=False, type='http', website='true') 
	def orders_stream(self,context):
		context = json.loads(context)
		fields = context['fields']
		order_ref=request.env['rb_delivery.order']
		return Response(order_ref.with_context(context).event_stream(fields), mimetype="text/event-stream")
	
	@http.route("/routes_stream/<string:context>", auth='user', cors='*', csrf=False, type='http', website='true') 
	def routes_stream(self,context):
		context = json.loads(context)
		route_ref=request.env['rb_delivery.routes']
		return Response(route_ref.with_context(context).event_stream(), mimetype="text/event-stream")

	@http.route("/drivers_order_location/<string:context>", auth='user', cors='*', csrf=False, type='http', website='true') 
	def driver_order_location_stream(self,context):
		context = json.loads(context)
		driver_location_ref=request.env['rb_delivery.driver_order_location']
		return Response(driver_location_ref.with_context(context).event_stream(), mimetype="text/event-stream")




