# -*- coding: utf-8 -*-

import json
import logging

from openerp import models,fields, api,_
from openerp import sql_db as sql_db
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from odoo.exceptions import ValidationError


class olivery_dispatcher_map_config(models.Model):

    _name = 'rb_delivery.dispatcher_map_config'

    _sql_constraints = [('group_id', 'unique(group_id)', _('Configuration for the same role already exist!'))]

    def get_groups(self):
        groups = self.env['res.groups'].sudo().search([('category_id.code','=','model_rb_delivery')])
        return [('id', 'in', groups.ids)]

    group_by_fields = fields.Many2many('ir.model.fields', string="Group By Fields",required=True, domain=[('model', '=', 'rb_delivery.order'),('ttype','!=','many2many'),('ttype','!=','one2many')])

    default_group_sequence = fields.One2many('rb_delivery.default_group_by_sequence',inverse_name='dispatcher_config_id',string='Default Grouping Fields')

    group_id = fields.Many2one('res.groups', string="Role", domain=get_groups, track_visibility="on_change")

    # ----------------------------------------------------------------------
    # Computed
    # ----------------------------------------------------------------------                 


    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------
        
    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------  
    # 

    
      
    @api.model
    def get_order_grouping_config(self):
        config = self.env['rb_delivery.dispatcher_map_config'].search([['group_id', 'in', self.env.user.groups_id.ids]], limit=1)
        
        # Create a list of pairs containing 'name', 'field_description' (display name), 'is_default', and 'sequence'
        group_fields = []
        for field in config.group_by_fields:
            field_info = self.env['rb_delivery.order']._fields[field.name]
            display_name = field_info.string  # Fetch the display name of the field
            sequence_config = config.default_group_sequence.filtered(lambda seq: seq.default_group_by_fields == field)
            sequence = sequence_config.sequence if sequence_config else 0  # Get sequence, default to 0 if not found
            is_default = True if field.id in config.default_group_sequence.mapped('default_group_by_fields').ids else False
            group_fields.append([field.name,field.ttype, display_name, is_default, sequence])

        group_fields.sort(key=lambda x: x[3])

        return group_fields


        

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------

class olivery_dispatcher_map_default_group_by(models.Model):

    _name = 'rb_delivery.default_group_by_sequence'

    dispatcher_config_id = fields.Many2one('rb_delivery.dispatcher_map_config',string='Map Config')

    default_group_by_fields = fields.Many2one(
        comodel_name='ir.model.fields',
        string="Default Group By Fields", domain=[('model', '=', 'rb_delivery.order')]
    )

    sequence = fields.Integer('Sequence')

    @api.onchange('dispatcher_config_id')
    def form_to_open_domain(self):
        return {
            'domain':{
                'default_group_by_fields':[['id','in',self.dispatcher_config_id.group_by_fields.ids]]
            }
        }
