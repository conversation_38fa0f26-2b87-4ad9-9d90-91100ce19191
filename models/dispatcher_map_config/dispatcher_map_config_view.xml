<odoo>
    <data>
      <record id="view_form_rb_delivery_dispatcher_map_config" model="ir.ui.view">

        <field name="name">view_form_rb_delivery_dispatcher_map_config</field>
        <field name="model">rb_delivery.dispatcher_map_config</field>

        <field name="arch" type="xml">
          <form>

            <header>
              <!-- Buttons and status widget -->
            </header>

            <sheet>

              <group name="group_top">
                <group name="group_left">
                  <field name="group_id" string="Role"/>
                </group>

                <group name="group_right">
                  <field name="group_by_fields" widget="many2many_tags"/>
                  <field name="default_group_sequence">
                    <tree editable='bottom'>
                      <field name='dispatcher_config_id' invisible='1'/>
                      <field name='sequence' widget='handle'/>
                      <field name='default_group_by_fields'/>
                    </tree>
                  </field>
                </group>
            </group>

            </sheet>
          </form>

        </field>
      </record>


      <record id="view_tree_rb_delivery_dispatcher_map_config" model="ir.ui.view">
        <field name="name">view_tree_rb_delivery_dispatcher_map_config</field>
        <field name="model">rb_delivery.dispatcher_map_config</field>

        <field name="arch" type="xml">
          <tree>
            <field name="group_id"/>
          </tree>
        </field>

      </record>

    </data>
  </odoo>
