<odoo>
    <data>
        <record id="view_form_dispatcher_filter" model="ir.ui.view">
            <field name="name">view_form_dispatcher_filter</field>
            <field name="model">rb_delivery.dispatcher_filter</field>
      
            <field name="arch" type="xml">
              <form>
      
                <header>
                  <!-- Buttons and status widget -->
                </header>
      
                <sheet>
                  <group name="group_top">
                    <group name="group_left">
                        <field name="default"/>
                        <field name="name"/>
                        <field name="dispatcher_filter_model_selection" invisible="1"/>
                        <field name="group_id"/>
                    </group>
                    <group name="group_right">
                        <field name="filter_domain" widget="domain" options="{'model': 'rb_delivery.order', 'in_dialog': True}"/>
                    </group>
                  </group>
                </sheet>
                <!-- History and communication: -->
                <div class="oe_chatter">
                  <field name="message_follower_ids" widget="mail_followers"/>
                  <field name="message_ids" widget="mail_thread"/>
                </div>
              </form>
      
            </field>
          </record>
          <record id="view_tree_dispatcher_filter" model="ir.ui.view">
            <field name="name">view_tree_dispatcher_filter</field>
            <field name="model">rb_delivery.dispatcher_filter</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name" />
                    <field name="group_id"/>
                </tree>
            </field>
        </record>
      
    </data>
</odoo>
