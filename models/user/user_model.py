# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, api,_,fields
from openerp import sql_db as sql_db
from datetime import datetime, timedelta

import base64

_logger = logging.getLogger(__name__)


class olivery_dispatcher_map_user(models.Model):

    _inherit = 'rb_delivery.user'

    def _get_nearest_driver_orders_domain(self):
        status_ids = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'shipment_statuses')
        return [('state_id','in',status_ids)]
    
    def _get_business_dispatcher_orders_domain(self):
        status_ids = self.env['rb_delivery.client_configuration'].get_param('default_status_olivery_dispatcher_map')
        return [('state_id','in',status_ids)]

    nearest_driver_orders = fields.One2many('rb_delivery.order',inverse_name='assign_to_agent',domain=_get_nearest_driver_orders_domain,string='Driver Orders Assigned')

    dispatcher_business_orders = fields.One2many('rb_delivery.order',inverse_name='assign_to_business',domain=_get_business_dispatcher_orders_domain,string='Business Orders For Dispatcher Map')

    # ----------------------------------------------------------------------
    # Computed
    # ----------------------------------------------------------------------                 


    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------
        
    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------  
    # 
    

    @api.model                    
    def event_stream(self):
        
        with api.Environment.manage():
            new_cr = sql_db.db_connect(self.env.cr.dbname).cursor()
            uid, context = self.env.uid, self.env.context
            self.env = api.Environment(new_cr, uid, context)
            minus_four_secounds=datetime.now()- timedelta(hours=0, minutes=0,seconds=4)
            drivers_to_hide=[]
            fields_to_return=[
                    "longitude",
                    "latitude",
                    "username",
                    "mobile_number",
                    "commercial_number",
                    "area_id",
                    "address",
                    "whatsapp_mobile",
                    "role_code",
                    "online",
                    "nearest_driver_orders",
                    "dispatcher_business_orders",
                    "order_ids",
                    "commercial_name",
                    "vehicle_type"
                  ]
            orders_with_agent_changed = self.env['rb_delivery.order'].search([['write_date','>',str(minus_four_secounds)]])
            changed_users=self.env['rb_delivery.user'].search_read([['write_date','>',str(minus_four_secounds)],'|',['role_code','=','rb_delivery.role_driver'],['role_code','=','rb_delivery.role_business']],fields_to_return)
            if self.env.user.rb_user.role_code == 'rb_delivery.role_business':
                shipment_statuses_ids = self.env['rb_delivery.nearest_driver'].get_param('one_by_one', 'shipment_statuses')
                drivers = self.env.user.rb_user.order_ids.filtered(lambda x:x.state_id.id in shipment_statuses_ids).mapped('assign_to_agent')
                if len(drivers):
                    changed_users= changed_users+drivers.read(fields_to_return)
                drivers_to_hide = self.sudo().env['rb_delivery.user'].search([['id','not in',drivers.ids],['role_code','=','rb_delivery.role_driver']]).ids
            self.env.cr.commit()
            agents = orders_with_agent_changed.mapped('assign_to_agent').read(fields_to_return)
            businesses = orders_with_agent_changed.mapped('assign_to_business').read(fields_to_return)
            previous_agents = orders_with_agent_changed.mapped('previous_agent').read(fields_to_return)
            changed_users+=agents+previous_agents+businesses
        if len(changed_users or drivers_to_hide)>0:
            new_cr.close()
            yield "data:"+json.dumps({'changed_users':changed_users,'drivers_to_hide':drivers_to_hide})+"\n\n"

    def get_session_id(self):
        return self.env['ir.http'].session_info().get('session_id')
        

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
