# -*- coding: utf-8 -*-
import json
import logging
from openerp import models, api, _, fields
from datetime import datetime, date,timedelta
_logger = logging.getLogger(__name__)

class olivery_dispatcher_map_filter(models.Model):
    _name = 'rb_delivery.dispatcher_date_rule'
    _inherit = "mail.thread"

    dispatcher_filter_date_filter_field = fields.Many2one(
        'ir.model.fields',
        domain="[('model_id.model', '=', 'rb_delivery.order'),('store', '=', True), '|',('ttype', '=', 'date'), ('ttype', '=', 'datetime')]",
        string="Date Filter Field",
        track_visibility="on_change"
    )
    dispatcher_filter_date_filter_selection = fields.Selection([
        ('l_none', 'None'),
        ('l_day', 'Today'),
        ('t_week', 'This Week'),
        ('t_month', 'This Month'),
        ('t_quarter', 'This Quarter'),
    ], string="Date Filter Selection", default="l_none", required=True,track_visibility="on_change")

    dispatcher_filter_id = fields.Many2one(
        'rb_delivery.dispatcher_filter',
        string="Dispatcher Filter",
        ondelete='cascade'
    )
