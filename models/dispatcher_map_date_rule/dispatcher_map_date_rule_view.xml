<odoo>
    <data>
        <record id="view_form_dispatcher_date_rule" model="ir.ui.view">
            <field name="name">view_form_dispatcher_date_rule</field>
            <field name="model">rb_delivery.dispatcher_date_rule</field>
      
            <field name="arch" type="xml">
              <form>
      
                <header>
                  <!-- Buttons and status widget -->
                </header>
      
                <sheet>
                  <group name="group_top">
                    <group name="group_left">
                        <field name="dispatcher_filter_date_filter_field"/>
                        <field name="dispatcher_filter_date_filter_selection" attrs="{'invisible': [('dispatcher_filter_date_filter_field', '=', False)]}"/>
                    </group>
                  </group>
                </sheet>
                <!-- History and communication: -->
                <div class="oe_chatter">
                  <field name="message_follower_ids" widget="mail_followers"/>
                  <field name="message_ids" widget="mail_thread"/>
                </div>
              </form>
      
            </field>
          </record>
    </data>
</odoo>
