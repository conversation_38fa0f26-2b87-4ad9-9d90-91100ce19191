# -*- coding: utf-8 -*-

import json
import logging

from openerp import models, api,_,fields
from openerp import sql_db as sql_db
from datetime import datetime, timedelta
import requests
import base64

_logger = logging.getLogger(__name__)


class olivery_dispatcher_map_routes(models.Model):

    _name = 'rb_delivery.routes'

    direction = fields.Char('Direction')

    name = fields.Char('name', compute='compute_name')

    locations = fields.Char('Locations')

    done_locations = fields.Char('Done Locations')

    order_ids = fields.Many2many('rb_delivery.order',string='Orders')

    status = fields.Selection(selection=[('in_progress','In Progress'),('done','Done')],string='Status',compute='_compute_route_status',store=True)

    driver_id = fields.Many2one('rb_delivery.user',string='Driver',compute="_get_driver")

    locations_write_date = fields.Datetime('Locations Write Date')

    route_collection_id = fields.Many2one('rb_delivery.route_collection',
        string="In route collection",
        relation='routes_route_collection_rel',
        copy=False,
        readonly=True
    )

    open_route = fields.Boolean('Open route', default=True)

    @api.depends('order_ids')
    def _compute_route_status(self):
        for rec in self:
            if len(rec.order_ids.filtered(lambda x:x.state=='in_progress')):
                rec.status = 'in_progress'
            else:
                rec.status = 'done'

    @api.depends('route_collection_id')
    @api.multi
    def compute_name(self):
        for rec in self:
            if rec.route_collection_id and len(rec.route_collection_id.delivered_orders) == len(rec.order_ids):
                rec.name = _('Closed_') + str(rec.create_date)
                if rec.open_route:
                    rec.open_route = False
            else:
                rec.name = _('Open_') + str(rec.create_date)
        

    @api.depends('create_uid')
    def _get_driver(self):
        for rec in self:
            rec.driver_id = rec.create_uid.rb_user
    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    @api.model
    def create(self,values):
        old_route = self.find_route(values=values)
        if not old_route:
            if values.get('locations'):
                self.update_orders(values['locations'])
                values['locations_write_date']=datetime.now()
            record = super(olivery_dispatcher_map_routes,self).create(values)
            record.add_to_route_collection(record.order_ids, values)
            return record
        old_route.write(values)
        return old_route
            
    def write(self,values):
        if values.get('locations'):
            self.update_orders(values['locations'])
            values['locations_write_date']=datetime.now()
        result = super(olivery_dispatcher_map_routes,self).write(values)
        if 'order_ids' in values and values['order_ids'] and not values.get('skip_adding_collection'):
            self.add_to_route_collection(self.order_ids, values)
        return result
            
    def update_orders(self,locations):
        locations = json.loads(locations) or []
        order_ids = [location['orderId'] for location in locations if location['orderId']]
        orders = self.env['rb_delivery.order'].browse(order_ids)
        for order in orders:
            message=_("Order has been updated while editing a route")
            location = next(filter(lambda x:x['orderId'] == order.id,locations),None)
            if location:
                order_vals = {'latitude':location['location']['lat'],'longitude':location['location']['lng'],'customer_area':order.customer_area.id}
                if order.longitude != order_vals['longitude'] or order.latitude != order_vals['latitude']:
                    data = {'uid':self._uid,'message':message,'records':order,'values':order_vals,'update':True}
                    self.env['rb_delivery.utility'].olivery_sudo(data)
                if order.route_collection_id:
                    order.route_collection_id.route = self.id


    def find_collection(self, agent):
        return self.env['rb_delivery.route_collection'].search([('assign_to_agent', '=', agent), ('state', '!=', 'done_route')], limit=1)
    
    def find_route(self, values):
        rb_user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        return self.env['rb_delivery.routes'].search([('driver_id', '=', rb_user.id), ('open_route', '=', True), ('order_ids', 'in', values['order_ids'][0][2])])
    
    def create_route_collection(self, agent, order_ids):
        route = self.env['rb_delivery.routes'].search(
            [('order_ids', 'in', order_ids)],
            limit=1,
            order='create_date desc'
        )
        vals = {
            'assign_to_agent': agent,
            'order_ids':  order_ids
        } 
        if route and route.id:
            vals['route'] = route.id
        route_collection = self.env['rb_delivery.route_collection'].create(vals)
        route.route_collection_id = route_collection.id


    def add_to_route_collection(self, orders_arr, values):
        orders = []
        agent_collection_map = {}
        for rec in orders_arr:
            values_copy = {}
            if rec.assign_to_agent and rec.longitude and rec.latitude and rec.state == 'in_progress':
                if rec.route_collection_id and rec.route_collection_id.state != 'done_route':
                    delivered_orders = self.order_ids.filtered(lambda o: o.state == 'delivered').ids
                    non_delivered_orders = self.order_ids.filtered(lambda o: o.state != 'delivered').ids
                    values_write_1 = {
                        'delivered_orders':  [(6, False, delivered_orders)],
                        'non_delivered_orders': [(6, False, non_delivered_orders)],
                        'skip_check_collection': True
                    }
                    self.route_collection_id.write(values_write_1)
                else:
                    if agent_collection_map.get(rec.assign_to_agent.id):
                        agent_collection_map.get(rec.assign_to_agent.id).append(rec.id)
                    else:
                        agent_collection_map[rec.assign_to_agent.id] = [rec.id]

            if values_copy:
                orders.append(rec)
        if agent_collection_map:
            for agent, orders in agent_collection_map.items():
                old_collection = self.find_collection(agent)
                if old_collection:
                    combined_order_ids = old_collection.order_ids.ids + orders
                    old_collection.write({'order_ids': [(6, 0, combined_order_ids)]})
                else:
                    self.create_route_collection(agent=agent, order_ids=orders)
        if values.get('delete_from_collection', 0):
            for rec in self:
                if rec.route_collection_id:
                    delete_ids = values.get('delete_from_collection') or []
                    if isinstance(delete_ids, int):
                        delete_ids = [delete_ids]
                    elif isinstance(delete_ids, list):
                        delete_ids = [id for id in delete_ids if isinstance(id, int)]
                    else:
                        delete_ids = []

                    new_orders = list(set(rec.route_collection_id.order_ids.ids) - set(delete_ids))
                    new_delivered_orders = list(set(rec.route_collection_id.delivered_orders.ids) - set(delete_ids))
                    new_non_delivered_orders = list(set(rec.route_collection_id.non_delivered_orders.ids) - set(delete_ids))
                    rec.route_collection_id.write({
                        'order_ids': [(6, 0, new_orders)],
                        'delivered_orders':  [(6, 0, new_delivered_orders)],
                        'non_delivered_orders': [(6, 0, new_non_delivered_orders)],
                        'skip_check_collection': True
                    })

                    orders = self.env['rb_delivery.order'].browse(delete_ids)
                    orders.write({'route_collection_id': False})
    
    @api.model                    
    def event_stream(self):
        
        with api.Environment.manage():
            new_cr = sql_db.db_connect(self.env.cr.dbname).cursor()
            uid, context = self.env.uid, self.env.context
            self.env = api.Environment(new_cr, uid, context)
            minus_four_secounds=datetime.now()- timedelta(hours=0, minutes=0,seconds=4)
            fields_to_return=[
                "create_date",
                "write_date",
                "create_uid",
                "driver_id",
                "locations",
                "direction",
                "order_ids",
                "done_locations",
                "status"
            ]
            changed_routes=self.env['rb_delivery.routes'].search_read([['locations_write_date','>',str(minus_four_secounds)]],fields_to_return)
            
            serialized_routes = []
            for route in changed_routes:
                for key, value in route.items():
                    if isinstance(value, datetime):
                        route[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                serialized_routes.append(route)
            self.env.cr.commit()
        if len(serialized_routes)>0:
            
            yield "data:"+json.dumps(changed_routes)+"\n\n"

    @api.model
    def calculate_optimized_delivery_eta(self, curr_longitude, curr_latitude, order_ids):
        rb_user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
        route_collection = self.env['rb_delivery.route_collection'].search([('assign_to_agent', '=', rb_user.id), ('state', '!=', 'done_route'), ('order_ids', 'in', order_ids)])
        orders = self.env['rb_delivery.order'].search([('id', 'in', order_ids), ('state', '=', 'in_progress')])
        try:
            if route_collection and route_collection.route and route_collection.route.direction:
                data = json.loads(route_collection.route.direction)
                optimized_order_ids = data['routes'][0]['waypoint_order']
                legs = data['routes'][0]['legs']
            else:
                optimized_order_ids, legs, data = self.get_google_locations(orders, curr_longitude, curr_latitude)

            if optimized_order_ids and legs:
                delivery_schedule = []
                cumulative_eta = 0

                for i, order_index in enumerate(optimized_order_ids):
                    order = orders[order_index]
                    leg = legs[i]
                    
                    travel_time = leg["duration"]["value"] / 60  # Convert to minutes
                    cumulative_eta += travel_time

                    delivery_schedule.append({
                        "order_id": order.id,
                        "distance_km": leg["distance"]["value"] / 1000,
                        "expected_delivery_time_min": cumulative_eta
                    })

                return delivery_schedule
            else:
                return {"error": "Unable to fetch data from Google Maps API"}
        except:
            return {"error": "COULD_NOT_CALCULATE_ETA_FOR_THE_ORDERS_PLEASE_TRY_AGAIN"}
        


    def get_google_locations(self, orders, curr_longitude, curr_latitude):
        api_key = self.env['rb_delivery.client_configuration'].get_param('google_map_key_for_server')
        base_url = "https://maps.googleapis.com/maps/api/directions/json"
        waypoints = []
        for order in orders:
            if order.latitude and order.longitude:
                waypoints.append(f"{order.latitude},{order.longitude}")

        waypoints = "|".join(waypoints)
        if waypoints:
            params = {
                "origin": f"{curr_latitude},{curr_longitude}",
                "destination": f"{curr_latitude},{curr_longitude}",
                "waypoints": f"optimize:true|{waypoints}",
                "key": api_key
            }
            response = requests.get(base_url, params=params)
            if response.status_code == 200:
                data = response.json()
                optimized_order_ids = data['routes'][0]['waypoint_order']
                legs = data['routes'][0]['legs']

                return optimized_order_ids, legs, data
        else:
            return False, False, False

    @api.model
    def create_route(self,collection_id, order_ids, curr_long, curr_lat):
        try:
            optimized_order_ids, legs, data = self.get_google_locations(order_ids, curr_long, curr_lat)
            locations = json.dumps(self.generate_locations(order_ids, curr_long, curr_lat))
        except:
            data = ''
            locations = ''
        return self.env['rb_delivery.routes'].create({
            'locations': locations,
            'route_collection_id':collection_id,
            'direction': data,
            'order_ids': [[6,0, order_ids.ids]],
            'done_locations': '[]'
        })


    def generate_locations(self, order_ids, curr_long, curr_lat):
        locations = [{"orderId": 0, "location": {"lat": curr_lat, "lng": curr_long}}]

        for order in order_ids:
            locations.append({
                "orderId": order.id,
                "location": {
                    "lat": order.latitude,
                    "lng": order.longitude
                }
            })

        return locations


    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
