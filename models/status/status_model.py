# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError

class olivery_dispatcher_status(models.Model):

    _inherit = 'rb_delivery.status'

    @api.model
    def get_collection_type(self):
        collection_types = super(olivery_dispatcher_status, self).get_collection_type()
        collection_types.append(('route_collection','Route Collection'))
        return collection_types