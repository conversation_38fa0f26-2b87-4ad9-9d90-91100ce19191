# -*- coding: utf-8 -*-

import json
import logging

from openerp import models,fields, api,_
from openerp import sql_db as sql_db
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from odoo.exceptions import ValidationError


class olivery_dispatcher_map_order(models.Model):

    _inherit = 'rb_delivery.order'

    agent_mobile_number = fields.Char(related='assign_to_agent.mobile_number', readonly=True)

    agent_whatsapp_mobile = fields.Char(related='assign_to_agent.whatsapp_mobile', readonly=True)

    route_collection_id = fields.Many2one('rb_delivery.route_collection',
        string="In route collection",
        relation='order_route_collection_rel',
        copy=False,
        readonly=True
    )

    route_collection_state = fields.Char(string='Route collection state')



    @api.multi
    def write(self, values):
        if 'assign_to_agent' in values and values['assign_to_agent']:
            for rec in self:
                if rec.route_collection_id and rec.route_collection_id and rec in rec.route_collection_id.order_ids:
                    rec.route_collection_id.with_context(update_route=True).write({
                        'order_ids': [(6, 0, list(set(rec.route_collection_id.order_ids.ids) - {rec.id}))],
                        'delivered_orders': [(6, 0, list(set(rec.route_collection_id.delivered_orders.ids) - {rec.id}))],
                        'non_delivered_orders': [(6, 0, list(set(rec.route_collection_id.non_delivered_orders.ids) - {rec.id}))],
                    })
            values['route_collection_id'] = False
        write = super(olivery_dispatcher_map_order, self).write(values)
        if 'state' in values and values['state'] == 'delivered' or values.get('longitude') or values.get('latitude'):
            self.with_delay(channel="root.basic",max_retries=2).update_route_collection(values)
        return write
    
    @api.multi
    def update_route_collection(self, values):
        collection_field = 'route_collection_id'

        filtered_recs = self.filtered(
            lambda rec: getattr(rec, collection_field)
        )

        collection_recs_map = {}
        for rec in filtered_recs:
            collection = getattr(rec, collection_field)
            if collection:
                collection_recs_map.setdefault(collection, self.env['rb_delivery.order'])
                collection_recs_map[collection] |= rec
        for collection, recs in collection_recs_map.items():
            if 'non_delivered_orders' in collection._fields:
                collection.non_delivered_orders = collection.non_delivered_orders - recs
            if 'delivered_orders' in collection._fields:
                collection.delivered_orders = collection.delivered_orders | recs
        if (values.get('longitude') or values.get('latitude')):
            self.update_routes(values)

    def update_routes(self, values):
        for rec in self:
            if rec.route_collection_id:
                rec.route_collection_id.check_collections({'force_write': True, 'direction': rec.route_collection_id.route.direction})

    def find_collection(self, agent):
        return self.env['rb_delivery.route_collection'].search([('assign_to_agent', '=', agent), ('state', '!=', 'done_route')], limit=1)
    
    def create_route_collection(self, agent, order_ids):
        route = self.env['rb_delivery.routes'].search_read(
            [('order_ids', 'in', order_ids)],
            ['id'],
            limit=1,
            order='create_date desc'
        )
        vals = {
            'assign_to_agent': agent,
            'order_ids':  order_ids
        } 
        if route and route['id']:
            vals['route'] = route['id']
        self.env['rb_delivery.route_collection'].create(vals)


    def add_to_route_collection(self):
        orders = []
        agent_collection_map = {}
        for rec in self:
            values_copy = {}
            if rec.assign_to_agent and rec.longitude and rec.latitude and rec.state == 'in_progress' and (not rec.route_collection_id or  rec.route_collection_id.state == 'done'):
                if agent_collection_map.get(rec.assign_to_agent.id):
                    agent_collection_map.get(rec.assign_to_agent.id).append(rec.id)
                else:
                    agent_collection_map[rec.assign_to_agent.id] = [rec.id]

            if values_copy:
                orders.append(rec)
        if agent_collection_map:
            for agent, orders in agent_collection_map.items():
                old_collection = self.find_collection(agent)
                if old_collection:
                    combined_order_ids = old_collection.order_ids.ids + orders
                    old_collection.write({'order_ids': [(6, 0, combined_order_ids)]})
                else:
                    self.create_route_collection(agent=agent, order_ids=orders)


        


    # ----------------------------------------------------------------------
    # Computed
    # ----------------------------------------------------------------------                 


    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------
        
    # ----------------------------------------------------------
    # Function
    # ----------------------------------------------------------    
    @api.model                    
    def event_stream(self,fields):
        with api.Environment.manage():
            new_cr = sql_db.db_connect(self.env.cr.dbname).cursor()
            uid, context = self.env.uid, self.env.context
            self.env = api.Environment(new_cr, uid, context)
            minus_four_secounds=str(datetime.now()- timedelta(hours=0, minutes=0,seconds=4))
            changed_orders=self.env['rb_delivery.order'].search_read([['write_date','>',minus_four_secounds]],fields)
            serialized_orders = []
            for order in changed_orders:
                for key, value in order.items():
                    if isinstance(value, datetime):
                        order[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                serialized_orders.append(order)
            self.env.cr.commit()
        if len(serialized_orders)>0:
            yield "data:"+json.dumps(serialized_orders)+"\n\n"

    def get_session_id(self):
        return self.env['ir.http'].session_info().get('session_id')
    
    def get_tracking_link(self):
        import uuid
        error_message = ""
        company = self.env['res.company'].sudo().search([],limit=1)
        if not self.assign_to_agent:
            error_message += _('Please set agent to the order!\n')
        if self.assign_to_agent and (not self.assign_to_agent.longitude or not self.assign_to_agent.latitude):
            error_message += _('Make sure agent is sharing location!\n')
        
        if not company or not company.base_url:
            error_message += _('Please set your company base url\n')  
        elif not error_message:
            expiry_time = self.env['rb_delivery.client_configuration'].get_param('tracking_link_expiry_time')
            tracking_uid = str(uuid.uuid4())
            url = company.base_url
            self.sudo().write({
                'tracking_uid' : tracking_uid,
                'tracking_url' : url+'/track_driver/' + str(tracking_uid),
                'tracking_url_expires_at' : datetime.now()+relativedelta(hours=int(expiry_time)),
                'tracking_url_generated_by':self._uid
            })
            
        else:
            raise ValidationError(error_message)
    

    # ----------------------------------------------------------
    # Create, Update, Delete, Copy
    # ----------------------------------------------------------

    # ----------------------------------------------------------
    # Notes
    # ----------------------------------------------------------
