# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import AccessError, UserError, RedirectWarning, ValidationError, Warning
from odoo.exceptions import UserError
from openerp import SUPERUSER_ID
class order_select_route_state_wizard(models.TransientModel):
    _name = 'rb_delivery.select_route_state'
    _description = "Select route State Model"

    state = fields.Selection(selection='get_status',track_visibility="on_change",string="Status",default="")
    agent = fields.Many2one( 'rb_delivery.user', 'Agent', track_visibility="on_change", domain=[('role_code', '=', 'rb_delivery.role_driver')])
    required_agent = fields.<PERSON><PERSON>an('Show Agent Required', default=False)
    show_agent = fields.<PERSON><PERSON>an('Show Agent', default=False)

    def get_status(self):
        group_id = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','route_collection')])
        status_list =[]
        for status in next_statuses:
            if group_id:
                if status.role_action_status_ids and len(status.role_action_status_ids)>0:
                    for role in status.role_action_status_ids:
                        if role.id == group_id.id:
                            status_list.append((status.name,status.title))
            else:
                status_list.append((status.name,status.title))
        return status_list

    @api.onchange('state')
    def change_state(self):
        state = self.env['rb_delivery.status'].sudo().search([('name','=',self.state),('status_type','=','olivery_collection'),('collection_type','=','route_collection')])
        optional_status_actions = state.status_action_optional_related_fields
        required_status_actions = state.status_action_required_aditional_fields
        self.show_agent = False
        self.required_agent = False
        self.agent=False
        if state and optional_status_actions:
            for status_action in optional_status_actions:
                if status_action.name == 'collection_show_agent':
                    self.show_agent = True
        if state and required_status_actions:
            for status_action in required_status_actions:
                if status_action.name == 'collection_show_agent':
                    self.show_agent = True
                    self.required_agent = True

    @api.multi
    def select_state(self):
        for rec in self:
            state = self.env['rb_delivery.status'].sudo().search([('name','=',rec.state),('status_type','=','olivery_collection'),('collection_type','=','route_collection')])
            route_ids = self.env['rb_delivery.route_collection'].browse(
                self._context.get('active_ids'))
            route_collection_vals = {}
            order_vals = {}
            if rec.state:
                route_collection_vals['state'] = rec.state
                if state.related_order_status:
                    order_state = self.env['rb_delivery.status'].sudo().search([('name','=',state.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')])
                    if order_state:
                        order_vals['state'] = order_state.name
                        order_vals['is_from_collection'] = True
            if rec.agent:
                route_collection_vals['assign_to_agent'] = rec.agent.id
                order_vals['assign_to_agent'] = rec.agent.id
            if route_collection_vals:
                for route_colelction_id in route_ids:
                    route_colelction_id.write(route_collection_vals)
                    if order_vals and route_colelction_id.order_ids:
                        route_colelction_id.order_ids.write(order_vals)
        return True