<odoo>
  <data>

    <record id="view_form_rb_delivery_route_collection" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_route_collection</field>
      <field name="model">rb_delivery.route_collection</field>

      <field name="arch" type="xml">
        <form create="false">

          <header>
            <button string="Change status" name="wkf_action_change_status" type="object"
              groups="rb_delivery.role_manager,rb_delivery.role_super_manager,rb_delivery.role_business,base.group_system" />
              <field name="state" widget="statusbar" statusbar_visible=" "/>
          </header>

          <sheet>
            <div class="oe_button_box o_full" name="button_box" style="margin-top:1vh"
            attrs="{'invisible':[('write_date', '=', False)]}">
            
            <button type="object" name="get_orders"
              class="btn btn-sm oe_stat_button o_form_invisible">
              <div class="fa fa-fw fa-list o_button_icon" />
              <div class="o_form_field o_stat_info" data-original-title="" title="">
                <span>All orders</span>
              </div>
            </button>
            
            <button type="object" name="get_done_orders"
              class="btn btn-sm oe_stat_button o_form_invisible">
              <div class="fa fa-fw fa-check-square-o o_button_icon" />
              <div class="o_form_field o_stat_info" data-original-title="" title="">
                <span>Done orders</span>
              </div>
            </button>
            
            <button type="object" name="get_not_done_orders"
              class="btn btn-sm oe_stat_button o_form_invisible">
              <div class="fa fa-fw fa-times-circle o_button_icon" />
              <div class="o_form_field o_stat_info" data-original-title="" title="">
                <span>Not done orders</span>
              </div>
            </button>
          
          </div>          
            <group name="group_top">
              <group name="group-right">
                <field name="route"/>
                <field name="create_uid" />
                <field name="write_date" invisible='1' />
                <field name="create_date" />
                <field name="assign_to_agent" readonly="1" />
                <field name="previous_agent" readonly="1"
                  groups="rb_delivery.role_manager,rb_delivery.role_super_manager,base.group_system" />
                <field name="previous_status" readonly="1" />
                <field name="route"/>
              </group>
              <group name="group-left">
                <div class="oe_right">
                  <field name="use_qr_code" readonly="1" invisible="1" />
                  <field name="barcode" style="display:block;text-align:center;width:200px"
                    height="100" width="200" widget="image" class="oe_center" nolabel="1"
                    attrs="{'invisible':[('use_qr_code','=',True)]}" />
                  <field name="qr_code_image"
                    style="display:block;text-align:center;max-width:150px;max-height:150px!important"
                    height="150" width="150" widget="image" class="oe_center" nolabel="1"
                    attrs="{'invisible':[('use_qr_code','=',False)]}" />
                  <field name="sequence" string="Sequence Number"
                    style="display:block;text-align:center;width:200px" />

                </div>
              </group>
            </group>
            <group name="group_top">
              <field name="is_collection_manager" invisible="1" />
              <field name="allow_edit_collection_orders" invisible="1" />
              <field name="order_ids"
                attrs="{'readonly':[('allow_edit_collection_orders','=',False),('is_collection_manager','=',False)]}"
                context="{'order': 'route_sequence'}">
                <tree delete="1">
                  <field name="sequence_related" />
                  <field name="reference_id" />
                  <field name="assign_to_business" />
                  <field name="customer_name" />
                  <field name="customer_mobile" />
                  <field name="customer_area" />
                  <field name="customer_address" />
                  <field name="previous_agent" />
                  <field name="customer_sub_area"
                    attrs="{'invisible': [('customer_sub_area','=',False)]}" />
                  <field name="money_collection_cost" sum="Money collection"
                    groups="rb_delivery.role_super_manager,base.group_system" />
                  <field name="state" />
                </tree>
              </field>
            </group>
          </sheet>
          <!-- History and communication: -->
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers" />
            <field name="message_ids" widget="mail_thread" />
          </div>
        </form>

      </field>
    </record>

    <record id="view_tree_rb_delivery_route_collection" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_route_collection</field>
      <field name="model">rb_delivery.route_collection</field>
      <field name="arch" type="xml">
        <tree create="false">
          <field name="sequence" />
          <field name="state" />
          <field name="assign_to_agent" />
          <field name="create_uid" />
          <field name="create_date" />
          <field name="order_count" sum="Orders" />
          <field name="route" />
          <field name="delivered_orders" />
          <field name="non_delivered_orders" />
          <field name="previous_status" />
          <field name="previous_agent" readonly="1"
            groups="rb_delivery.role_manager,rb_delivery.role_super_manager,base.group_system" />
        </tree>
      </field>
    </record>

    <record id="view_search_rb_delivery_route_collection" model="ir.ui.view">
      <field name="name">view_search_rb_delivery_route_collection</field>
      <field name="model">rb_delivery.route_collection</field>
      <field name="arch" type="xml">
        <search>
          <group>
            <field name="sequence" />
            <field name="assign_to_agent" />
            <field name="state" />
            <field name="previous_agent" />
            <field name="previous_status" />
          </group>
          <group string="Filters">
            <filter name="completed_collections" string="Completed Collections"
              domain="[('state','=','completed_returned')]" />
          </group>
          <group string="Groups">
            <filter name="group_by_previous_agent" string="By Previous Agent" icon="terp-partner"
              context="{'group_by':'previous_agent'}" />
            <filter name="group_by_agent" string="By Agent" icon="terp-partner"
              context="{'group_by':'assign_to_agent'}" />
            <filter name="group_by_previous_status" string="By Previous Status" icon="terp-partner"
              context="{'group_by':'previous_status'}" />
          </group>
        </search>
      </field>
    </record>


    <record id="view_form_rb_delivery_order_select_route_collection_state" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_order_select_route_collection_state</field>
      <field name="model">rb_delivery.select_route_state</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
          </header>

          <sheet>

            <group name="group_top">
              <field name="state" />
            </group>

          </sheet>
          <footer>
            <button name="select_state" type="object" string="Save" />
            <button name="cancel" string="Cancel" special="cancel" class="oe_link" />
          </footer>

        </form>

      </field>
    </record>
    
  </data>
</odoo>