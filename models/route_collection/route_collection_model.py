# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
import base64
import qrcode
from odoo.exceptions import ValidationError, UserError
from io import BytesIO
from collections import defaultdict
import barcode
from barcode.writer import ImageWriter
import io
from datetime import timedelta
import json

class rb_delivery_route_collection(models.Model):
    _name = 'rb_delivery.route_collection'
    _inherit = 'mail.thread'
    _order = "create_date DESC"
    _description = "Route collection Model"

    @api.multi
    def check_user(self):
        user = self.env['res.users'].browse([self._uid])
        for rec in self:
            rec.is_collection_manager = user.has_group('rb_delivery.role_collection_manager')

    def default_is_collection_manager(self):
        user = self.env['res.users'].browse([self._uid])
        is_collection_manager = user.has_group('rb_delivery.role_collection_manager')
        return is_collection_manager

    @api.model
    def get_default_status(self):
        status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','route_collection'),('status_type','=','olivery_collection')],limit=1)
        return status.name if status else None

    @api.one
    def compute_allow_edit_collection_orders(self):
        if self._uid == 1 or self._uid == 2:
            self.allow_edit_collection_orders = True
        else:
            user = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)])
            self.allow_edit_collection_orders = user.allow_edit_collection_orders

    def compute_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        self.use_qr_code = use_qr_code

    def default_use_qr_code(self):
        use_qr_code = self.env['rb_delivery.client_configuration'].get_param('use_qr_code')
        return use_qr_code

    sequence = fields.Char('Sequence', readonly=True,track_visibility=False,copy=False)

    barcode = fields.Binary('Barcode', compute="create_barcode")

    assign_to_agent = fields.Many2one('rb_delivery.user', 'Agent', domain=[('role_code', '=', 'rb_delivery.role_driver')])

    order_ids = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        string = 'Orders')

    active = fields.Boolean('Active', default=True , track_visibility="on_change")

    previous_agent = fields.Many2one('rb_delivery.user', 'Previous Agent', track_visibility="on_change",copy=False)

    state = fields.Selection(selection='get_status', track_visibility="on_change",string="Status",default=get_default_status)

    previous_status = fields.Char('Previous Status', track_visibility=False)

    previous_status_title = fields.Char('Previous Status Name',  track_visibility="on_change", readonly=True)

    order_count = fields.Integer(string="Order count")

    is_collection_manager = fields.Boolean('Is Collection Manager', compute="check_user", default=default_is_collection_manager)


    state_id = fields.Many2one('rb_delivery.status', 'Status ID',compute="compute_status_id",store=True)

    status_color = fields.Char(related='state_id.colour_code', track_visibility="on_change")

    secondary_status_color = fields.Char(related='state_id.secondary_colour_code', track_visibility="on_change")

    allow_edit_collection_orders = fields.Boolean('Allow edit collections orders', compute="compute_allow_edit_collection_orders")

    qr_code_image = fields.Binary('QR Code', compute="create_qr_code")

    use_qr_code = fields.Boolean('Use QR code', compute="compute_use_qr_code", default=default_use_qr_code)

    route = fields.Many2one('rb_delivery.routes', 'Route', readonly=True)

    delivered_orders = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        relation='route_collection_order_delivered_rel',
        string = 'Delivered orders')
    
    non_delivered_orders = fields.Many2many(
        comodel_name = 'rb_delivery.order',
        relation='route_collection_order_non_delivered_rel',
        string = 'Not delivered orders')
    
    name = fields.Char('name', compute='compute_name_display')

    route_date = fields.Datetime('Date', readonly=True)


    @api.depends('route_date')
    @api.multi
    def compute_name_display(self):
        for rec in self:
            if rec.route_date:
                rec.name = str(rec.route_date)
    
    @api.multi
    @api.depends('state')
    def compute_status_id(self):
        for rec in self:
            if rec.state:
                state_id = self.env['rb_delivery.status'].search([('name','=',rec.state),('status_type','=','olivery_collection'),('collection_type','=','route_collection')],limit=1)
                rec.state_id = state_id.id

    def get_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        ids = self.order_ids.ids
        domain = [('id', 'in', ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.display_name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}
    
    def get_done_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        ids = self.delivered_orders.ids
        domain = [('id', 'in', ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.display_name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}
    
    def get_not_done_orders(self):
        address_form_id = self.env.ref('rb_delivery.view_tree_rb_delivery_order').id
        ids = self.non_delivered_orders.ids
        domain = [('id', 'in', ids)]
        return {
            'type': 'ir.actions.act_window',
            'name': self.display_name,
            'res_model': 'rb_delivery.order',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'tree'), (False, 'form')],
            'target': 'current',
            'domain': domain}
    

    @api.model
    def get_status(self):
        status_list=[]
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','route_collection')])

        for status in next_statuses:
            status_list.append((status.name,status.title))
        return status_list
    
    @api.model
    def create_route(self,collection_id, curr_lat, curr_long):
        collection = self.env['rb_delivery.route_collection'].browse(collection_id)
        if collection.order_ids:
            route = self.env['rb_delivery.routes'].create_route(collection.id,collection.order_ids, curr_lat, curr_long)
            collection.route = route.id
        return collection.route.read()

    @api.model
    def create(self, values):
        if 'order_ids' in values and values['order_ids']:
            order_ids_create = values['order_ids']
            if len(order_ids_create) == 0:
                orders_list = self.env['rb_delivery.order'].search([('assign_to_agent', '=', values['assign_to_agent']), ('state', '=', 'in_progress')])
            else:
                orders_list = self.env['rb_delivery.order'].browse(values['order_ids'])
            del values['order_ids']
        else:
            orders_list = self.env['rb_delivery.order'].search([('assign_to_agent', '=', values['assign_to_agent']), ('state', '=', 'in_progress'),'|' ,('route_collection_id', '=', False), ('route_collection_state', '=', 'done_route')])

        if not orders_list: 
            raise ValidationError(_('Please make sure to add orders to be able to create route collection'))
        route_date = values.get('route_date', False)
        route_date = route_date.replace('T', ' ')
        if route_date:
            route_date_obj = fields.Datetime.from_string(route_date).date()

            existing_routes = self.search([
                ('route_date', '>=', fields.Datetime.to_string(route_date_obj)),
                ('route_date', '<', fields.Datetime.to_string(route_date_obj + timedelta(days=1))),
                ('assign_to_agent', '=', values.get('assign_to_agent'))
            ])

            if existing_routes:
               for route in existing_routes:
                    non_delivered = orders_list + route.order_ids
                    non_delivered = non_delivered.filtered(lambda o: o.state != 'delivered').ids
                    non_delivered = [(6,0,non_delivered)]
                    order_ids = [(6,0,orders_list.ids + route.order_ids.ids)]
                    delivered_orders_ids = route.order_ids + orders_list
                    delivered_orders_ids = delivered_orders_ids.filtered(lambda o: o.state == 'delivered').ids
                    delivered = [(6,0,delivered_orders_ids)]
                    route.write({
                        'state': 'open_route',
                        'order_ids': order_ids,
                        'non_delivered_orders': non_delivered,
                        'override_update_non_delivered': True,
                        'delivered_orders':delivered,
                        'skip_check_collection':True
                    })
                    route.route.sudo().unlink()

                

               return existing_routes

        order_sequences = set()
        route_collection_sequences = set()

        for order in orders_list:
            if order.route_collection_id and order.route_collection_id.state != 'done_route':
                order_sequences.add(order.sequence)
                route_collection_sequences.update(order.route_collection_id.mapped('sequence'))

        order_sequences_list = sorted(order_sequences)
        route_collection_sequences_list = sorted(route_collection_sequences)

        existing_records = []
        if route_collection_sequences_list:
            existing_records.append(
                _('Orders %s are in route collections of sequences %s') % (
                    ', '.join(order_sequences_list),
                    ', '.join(route_collection_sequences_list)
                )
            )

        exist_message = "\n".join(existing_records)

        if existing_records:
            self.env['rb_delivery.error_log'].raise_olivery_error(
                1700, self.id, {'exist_message': _(exist_message)}
            )


        if 'state' in values and values['state']:
            self.authorize_change_status(values['state'])
            self.env['rb_delivery.action'].notify_for_action_type('for_collection',action_name='route_collection',state_name=values['state'])

        status=self.env['rb_delivery.status'].search([('default','=',True),('collection_type','=','route_collection'),('status_type','=','olivery_collection')],limit=1)
        if status and status.default_related_order and status.related_order_status:
            order_status = self.env['rb_delivery.status'].search([('name','=',status.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')],limit=1)
            if order_status:
                data = {'uid':self._uid,'message':_("Orders status updated from create route collection."),'records':orders_list,'values':{'state':order_status.name},'update':True}
                self.env['rb_delivery.utility'].olivery_sudo(data)

        values['order_ids'] = [(6,0,orders_list.ids)]
        values['order_count'] = len(orders_list.ids)
        new_sequence = self.env['ir.sequence'].next_by_code(
            'rb_delivery.route_collection')
        values['sequence'] = new_sequence

        values['non_delivered_orders'] = [(6,0,orders_list.ids)]


        route = super(rb_delivery_route_collection, self).create(values)

        route.order_ids.write({'route_collection_id': route.id, 'route_collection_state': 'open_route'})
        message = _('Route collection generated')
        self.env['rb_delivery.utility'].send_toast('for_user', ['short_time',message], str(self._uid))
        return route
    

    @api.multi
    def write(self,values):
        user = self.env['res.users'].sudo().browse([self._uid])
        for rec in self:
            if 'order_ids' in values and values['order_ids']:
                ids = []
                to_seqs = []
                for order in rec.order_ids:
                    ids.append(order.sequence)
                if len(values['order_ids'][0][2])==0:
                    rec.active =False
                    rec.order_ids.write({'route_collection_id': False})
                else:
                    write_orders = [r for r in values['order_ids'][0][2] if r not in ids]
                    orders = self.env['rb_delivery.order'].browse(write_orders)
                    to_seqs = orders.mapped('sequence')
                    orders.write({'route_collection_id': rec.id, 'route_collection_state': values.get('state') if values.get('state') else rec.state})
                    values['order_count'] = len(orders)
                    values_to_be_reflected = self.env['rb_delivery.utility'].reflect_changes_to_collections(values['order_ids'][0][2],'route_collection')
                    if values_to_be_reflected:
                        values.update(values_to_be_reflected)
                    if not values.get('override_update_non_delivered'):
                        values['non_delivered_orders'] = [(4, order) for order in write_orders]
                
                rec.message_post(body=_("Orders were changed from %s to %s by %s") % (ids,to_seqs,user.name))
            if values.get('state'):
                rec.order_ids.with_context(original_uid=self.env.user.partner_id.id).sudo().write({'route_collection_state': values.get('state')})
        if not values.get('skip_check_collection'):
            self.update_collections_one_by_one(values=values)

        super(rb_delivery_route_collection, self).write(values)
        if not values.get('skip_check_collection'):
            self.check_collections(values=values)

        if values.get('state', '') != 'done_route':
            recs_to_close = self.env['rb_delivery.route_collection']
            for rec in self:
                if len(rec.non_delivered_orders)==0 and rec.state != 'done_route':
                    recs_to_close += rec
                if self._context.get('update_route'):
                    rec.route.sudo().unlink()
            if recs_to_close:
                recs_to_close.write({
                    'state': 'done_route'
                })


    def check_collections(self, values):
        for rec in self:
            if not values.get('skip_check_collection'):
                rec.update_non_delivered()
            if len(rec.order_ids) == 0:
                rec.unlink()
            elif ((values.get('order_ids') or values.get('force_write') or values.get('non_delivered_orders')) and len(rec.non_delivered_orders) > 0 and rec.route and len(rec.non_delivered_orders) != len(rec.route.order_ids)) or (values.get('delivered_orders') and len(rec.non_delivered_orders) > 0 and rec.route and len(rec.non_delivered_orders) != len(rec.route.order_ids)):
                rec.route.write({'direction': values.get('direction'), 'order_ids': [(6, 0, rec.non_delivered_orders.ids)], 'skip_adding_collection': True})
            elif rec.route and len(rec.non_delivered_orders) != len(rec.route.order_ids):
                rec.route.write({'direction': values.get('direction'), 'order_ids': [(6, 0, rec.non_delivered_orders.ids)], 'skip_adding_collection': True})
            all_delivered = all(order.state == 'delivered' for order in self.order_ids)
            if all_delivered and rec.state=='open_route':
                rec.state='done_route'
                rec.route.write({'skip_adding_collection': True, 'status': 'done', 'direction': values.get('direction'), 'order_ids': [(6, 0, rec.non_delivered_orders.ids)]})
            elif all_delivered and rec.state=='done_route':
                rec.route.write({'skip_adding_collection': True, 'status': 'done', 'direction': values.get('direction'), 'order_ids': [(6, 0, rec.non_delivered_orders.ids)]})

    def update_non_delivered(self):
        non_delivered = self.order_ids.filtered(lambda o: o.state != 'delivered').ids
        delivered = self.order_ids.filtered(lambda o: o.state == 'delivered').ids
        values = {
            'non_delivered_orders': [(6,0,non_delivered)],
            'delivered_orders': [(6,0,delivered)],
            'skip_check_collection': True
        }
        if len(non_delivered) > 0 and self.state != 'open_route':
            values['state'] = 'open_route'
        if len(non_delivered) == 0 and self.state == 'open_route':
            values['state'] == 'done_route'
        self.write(values)

    def update_collections_one_by_one(self,values):
        values_arr = []
        collections = []
        for rec in self:
            values_copy = {}

            if ('assign_to_agent' in values and values['assign_to_agent']):
                if rec.assign_to_agent:
                    values['previous_agent'] = rec.assign_to_agent.id

            if 'state' in values and values['state']:

                values_copy['previous_status'] = rec.state
                money_collection_status = self.env['rb_delivery.status'].sudo().search([('name','=',values_copy['previous_status']),('status_type','=','olivery_collection'),('collection_type','=','collection')])
                if money_collection_status and money_collection_status.title:
                    values_copy['previous_status_title'] = money_collection_status.title
            
            values['skip_check_collection'] = True

            if 'delivered_orders' in values and values['delivered_orders']:
                if len(values['delivered_orders'][0])==3:
                    if (len(rec.delivered_orders)) >= rec.order_count and rec.state =='open_route':
                        values_copy['state'] = 'done_route'
                        if rec.route:
                            rec.route.active = False

            if values_copy:
                values_arr.append(values_copy)
                collections.append(rec)
        if len(values_arr)>0 and len(collections)>0:
            self.with_delay(channel="root.basic",max_retries=2).write_jq(values_arr,collections)

    def write_jq(self,values_arr,collections,context=False):
        if context:
            merged_context = self._context.copy()
            merged_context.update(context)
            self = self.with_context(**merged_context)
        grouped_orders = defaultdict(list)
        for value, order in zip(values_arr, collections):
            key = frozenset(value.items())  
            grouped_orders[key].append(order.id) 
        for key, collection_ids in grouped_orders.items():
            update_values = dict(key)

            username = self.env.user.name
            message = _("Collection has been updated by %s through function write_jq.")%(username)
            grouped_order_set = self.browse(collection_ids)
            data = {'uid':self._uid,'message':message,'records':grouped_order_set,'values':update_values,'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)

    def wkf_action_change_status(self):
        address_form_id = self.env.ref('olivery_dispatcher_map.view_form_rb_delivery_order_select_route_collection_state').id
        context = {"parent_obj":self.id}

        return {
            'type': 'ir.actions.act_window',
            'name': 'Select State',
            'res_model': 'rb_delivery.select_route_state',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'views': [(address_form_id, 'form'), (False, 'tree')],
            'target': 'new',
            'context': context,
            'domain': []}

    @api.multi
    def name_get(self):
        result = []
        for route in self:
            if route.assign_to_agent:
                name = route.sudo().assign_to_agent.username
            else:
                name = "route"

            result.append((route.id, name))
        return result

    @api.model
    def authorize_change_status(self,status):

        if self.state and (self._uid!=1 and self._uid!=2): self.check_lock_status(self.state,status)
        #for super (used sudo) admin and super manager skip
        user = self.env['res.users'].browse([self._uid])
        if user.has_group('rb_delivery.role_super_manager') or self._uid==1 or self._uid==2:
            return
        #get the current role and the current status
        user_group = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id

        #get fields that has teh ability to edit for that specific role | status
        record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','route_collection')],limit=1)

        exist = False
        if record.role_action_status_ids and len(record.role_action_status_ids)>0:
            for role in record.role_action_status_ids:
                if role.id == user_group.id:
                    exist=True
                    break
        if not exist:
            self.env['rb_delivery.error_log'].raise_olivery_error(293,self.id,{'group': user_group, 'status': status,'collection_type':_('route collection')})
            #raise Warning(_("You are not allowed to change to this state"))

        return

    @api.model
    def check_lock_status(self,status,next_status):
        current_status_record=self.env['rb_delivery.status'].search([('name','=',status),('status_type','=','olivery_collection'),('collection_type','=','route_collection')],limit=1)
        next_status_record=self.env['rb_delivery.status'].search([('name','=',next_status),('status_type','=','olivery_collection'),('collection_type','=','route_collection')],limit=1)

        if not current_status_record.lock_status:
            # then there is no lock you can move to next status
            return
        else :
            # check if there is exception
            allowed_group=current_status_record.pass_lock_allowed_group_ids
            user_groups = self.env['res.users'].browse([self._uid]).groups_id
            if user_groups and allowed_group and set(allowed_group).intersection(set(user_groups)): return
            elif next_status_record.id in current_status_record.next_state_ids.ids :return
            else :
                # check if the next status is in the next status of the record
                self.env['rb_delivery.error_log'].raise_olivery_error(292,self.id,{'first': current_status_record.title, 'second': next_status_record.title,'collection_type':_('route collection')})
                #raise Warning(_("You are not allowed to change from this status {} to this status {}").format(current_status_record.title,next_status_record.title))

    @api.one
    @api.depends('sequence')
    def create_barcode(self):
        if (self.sequence):
            barcode.base.Barcode.default_writer_options['write_text'] = False
            EAN = barcode.get_barcode_class('code39')
            ean = EAN(self.sequence, writer=ImageWriter(), add_checksum=False)
            # ean = EAN(self.name, writer=ImageWriter())
            image_output = io.BytesIO()
            ean.write(image_output)
            encoded = base64.b64encode(image_output.getvalue())
            self.barcode = encoded
            # self.write({'barcode':encoded})

    @api.model
    def print_multi_orders_route_report(self,collection_id):
        pdf, _ = self.env.ref('rb_delivery.report_rb_delevery_route_action').sudo().render_qweb_pdf(collection_id)
        pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
        data = base64.encodestring(pdf)
        return data

    @api.multi
    def change_route_state(self,state_name):
        collection_state = self.env['rb_delivery.status'].sudo().search([('name','=',state_name),('collection_type','=','route_collection'),('status_type','=','olivery_collection')])
        order_state = self.env['rb_delivery.status'].sudo().search([('name','=',collection_state.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')])

        self.write({'state': collection_state.name})
        if order_state and len(order_state) >0:
            data = {'uid':self._uid,'message':_("Orders values updated from update route collection."),'records':self.order_ids,'values':{'state': order_state.name,'is_from_collection':True,'is_from_route_collection':True},'update':True}
            self.env['rb_delivery.utility'].olivery_sudo(data)
        return True

    @api.multi
    @api.depends('sequence')
    def create_qr_code(self):
        for rec in self:
            if (rec.sequence):
                qr = qrcode.QRCode(version=1,error_correction=qrcode.constants.ERROR_CORRECT_L,box_size=10,border=4,)
                qr.add_data(rec.sequence)
                qr.make(fit=True)
                img = qr.make_image()
                temp = BytesIO()
                img.save(temp, format="PNG")
                qr_image = base64.b64encode(temp.getvalue())
                rec.qr_code_image=qr_image
