# -*- coding: utf-8 -*-
import logging
from openerp import models, api, _, fields
_logger = logging.getLogger(__name__)

from odoo.exceptions import ValidationError

class olivery_dispatcher_map_order_card_button(models.Model):
    _name = 'rb_delivery.dispatcher_order_card_button'
    _inherit = "mail.thread"

    name = fields.Char("Button Name", required=True , track_visibility="on_change")
    
    action = fields.Selection([('statusButton', 'Status Button'),('assignAgent', 'Assign Agent')], string="Action Key", required=True,track_visibility="on_change")
