"use strict";(self.webpackChunkdispatcher=self.webpackChunkdispatcher||[]).push([[179],{404:()=>{function se(t){return"function"==typeof t}function ls(t){const e=t(r=>{Error.call(r),r.stack=(new Error).stack});return e.prototype=Object.create(Error.prototype),e.prototype.constructor=e,e}const cs=ls(t=>function(e){t(this),this.message=e?`${e.length} errors occurred during unsubscription:\n${e.map((r,i)=>`${i+1}) ${r.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=e});function Sr(t,n){if(t){const e=t.indexOf(n);0<=e&&t.splice(e,1)}}class $e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;const{_parentage:e}=this;if(e)if(this._parentage=null,Array.isArray(e))for(const o of e)o.remove(this);else e.remove(this);const{initialTeardown:r}=this;if(se(r))try{r()}catch(o){n=o instanceof cs?o.errors:[o]}const{_finalizers:i}=this;if(i){this._finalizers=null;for(const o of i)try{gf(o)}catch(s){n=n??[],s instanceof cs?n=[...n,...s.errors]:n.push(s)}}if(n)throw new cs(n)}}add(n){var e;if(n&&n!==this)if(this.closed)gf(n);else{if(n instanceof $e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=null!==(e=this._finalizers)&&void 0!==e?e:[]).push(n)}}_hasParent(n){const{_parentage:e}=this;return e===n||Array.isArray(e)&&e.includes(n)}_addParent(n){const{_parentage:e}=this;this._parentage=Array.isArray(e)?(e.push(n),e):e?[e,n]:n}_removeParent(n){const{_parentage:e}=this;e===n?this._parentage=null:Array.isArray(e)&&Sr(e,n)}remove(n){const{_finalizers:e}=this;e&&Sr(e,n),n instanceof $e&&n._removeParent(this)}}$e.EMPTY=(()=>{const t=new $e;return t.closed=!0,t})();const ff=$e.EMPTY;function pf(t){return t instanceof $e||t&&"closed"in t&&se(t.remove)&&se(t.add)&&se(t.unsubscribe)}function gf(t){se(t)?t():t.unsubscribe()}const nr={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},us={setTimeout(t,n,...e){const{delegate:r}=us;return r?.setTimeout?r.setTimeout(t,n,...e):setTimeout(t,n,...e)},clearTimeout(t){const{delegate:n}=us;return(n?.clearTimeout||clearTimeout)(t)},delegate:void 0};function mf(t){us.setTimeout(()=>{const{onUnhandledError:n}=nr;if(!n)throw t;n(t)})}function Al(){}const Rw=kl("C",void 0,void 0);function kl(t,n,e){return{kind:t,value:n,error:e}}let rr=null;function ds(t){if(nr.useDeprecatedSynchronousErrorHandling){const n=!rr;if(n&&(rr={errorThrown:!1,error:null}),t(),n){const{errorThrown:e,error:r}=rr;if(rr=null,e)throw r}}else t()}class Rl extends $e{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,pf(n)&&n.add(this)):this.destination=jw}static create(n,e,r){return new Ri(n,e,r)}next(n){this.isStopped?Nl(function Nw(t){return kl("N",t,void 0)}(n),this):this._next(n)}error(n){this.isStopped?Nl(function Pw(t){return kl("E",void 0,t)}(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?Nl(Rw,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const Lw=Function.prototype.bind;function Pl(t,n){return Lw.call(t,n)}class Vw{constructor(n){this.partialObserver=n}next(n){const{partialObserver:e}=this;if(e.next)try{e.next(n)}catch(r){hs(r)}}error(n){const{partialObserver:e}=this;if(e.error)try{e.error(n)}catch(r){hs(r)}else hs(n)}complete(){const{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(e){hs(e)}}}class Ri extends Rl{constructor(n,e,r){let i;if(super(),se(n)||!n)i={next:n??void 0,error:e??void 0,complete:r??void 0};else{let o;this&&nr.useDeprecatedNextContext?(o=Object.create(n),o.unsubscribe=()=>this.unsubscribe(),i={next:n.next&&Pl(n.next,o),error:n.error&&Pl(n.error,o),complete:n.complete&&Pl(n.complete,o)}):i=n}this.destination=new Vw(i)}}function hs(t){nr.useDeprecatedSynchronousErrorHandling?function Fw(t){nr.useDeprecatedSynchronousErrorHandling&&rr&&(rr.errorThrown=!0,rr.error=t)}(t):mf(t)}function Nl(t,n){const{onStoppedNotification:e}=nr;e&&us.setTimeout(()=>e(t,n))}const jw={closed:!0,next:Al,error:function Bw(t){throw t},complete:Al},Fl="function"==typeof Symbol&&Symbol.observable||"@@observable";function Or(t){return t}let ve=(()=>{class t{constructor(e){e&&(this._subscribe=e)}lift(e){const r=new t;return r.source=this,r.operator=e,r}subscribe(e,r,i){const o=function zw(t){return t&&t instanceof Rl||function Hw(t){return t&&se(t.next)&&se(t.error)&&se(t.complete)}(t)&&pf(t)}(e)?e:new Ri(e,r,i);return ds(()=>{const{operator:s,source:a}=this;o.add(s?s.call(o,a):a?this._subscribe(o):this._trySubscribe(o))}),o}_trySubscribe(e){try{return this._subscribe(e)}catch(r){e.error(r)}}forEach(e,r){return new(r=vf(r))((i,o)=>{const s=new Ri({next:a=>{try{e(a)}catch(l){o(l),s.unsubscribe()}},error:o,complete:i});this.subscribe(s)})}_subscribe(e){var r;return null===(r=this.source)||void 0===r?void 0:r.subscribe(e)}[Fl](){return this}pipe(...e){return function _f(t){return 0===t.length?Or:1===t.length?t[0]:function(e){return t.reduce((r,i)=>i(r),e)}}(e)(this)}toPromise(e){return new(e=vf(e))((r,i)=>{let o;this.subscribe(s=>o=s,s=>i(s),()=>r(o))})}}return t.create=n=>new t(n),t})();function vf(t){var n;return null!==(n=t??nr.Promise)&&void 0!==n?n:Promise}const Uw=ls(t=>function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});let te=(()=>{class t extends ve{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(e){const r=new yf(this,this);return r.operator=e,r}_throwIfClosed(){if(this.closed)throw new Uw}next(e){ds(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const r of this.currentObservers)r.next(e)}})}error(e){ds(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=e;const{observers:r}=this;for(;r.length;)r.shift().error(e)}})}complete(){ds(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:e}=this;for(;e.length;)e.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0}_trySubscribe(e){return this._throwIfClosed(),super._trySubscribe(e)}_subscribe(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)}_innerSubscribe(e){const{hasError:r,isStopped:i,observers:o}=this;return r||i?ff:(this.currentObservers=null,o.push(e),new $e(()=>{this.currentObservers=null,Sr(o,e)}))}_checkFinalizedStatuses(e){const{hasError:r,thrownError:i,isStopped:o}=this;r?e.error(i):o&&e.complete()}asObservable(){const e=new ve;return e.source=this,e}}return t.create=(n,e)=>new yf(n,e),t})();class yf extends te{constructor(n,e){super(),this.destination=n,this.source=e}next(n){var e,r;null===(r=null===(e=this.destination)||void 0===e?void 0:e.next)||void 0===r||r.call(e,n)}error(n){var e,r;null===(r=null===(e=this.destination)||void 0===e?void 0:e.error)||void 0===r||r.call(e,n)}complete(){var n,e;null===(e=null===(n=this.destination)||void 0===n?void 0:n.complete)||void 0===e||e.call(n)}_subscribe(n){var e,r;return null!==(r=null===(e=this.source)||void 0===e?void 0:e.subscribe(n))&&void 0!==r?r:ff}}function Je(t){return n=>{if(function $w(t){return se(t?.lift)}(n))return n.lift(function(e){try{return t(e,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function Ge(t,n,e,r,i){return new Gw(t,n,e,r,i)}class Gw extends Rl{constructor(n,e,r,i,o,s){super(n),this.onFinalize=o,this.shouldUnsubscribe=s,this._next=e?function(a){try{e(a)}catch(l){n.error(l)}}:super._next,this._error=i?function(a){try{i(a)}catch(l){n.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:e}=this;super.unsubscribe(),!e&&(null===(n=this.onFinalize)||void 0===n||n.call(this))}}}function ze(t,n){return Je((e,r)=>{let i=0;e.subscribe(Ge(r,o=>{r.next(t.call(n,o,i++))}))})}function Pn(t){return this instanceof Pn?(this.v=t,this):new Pn(t)}function wf(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=function jl(t){var n="function"==typeof Symbol&&Symbol.iterator,e=n&&t[n],r=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(o){e[o]=t[o]&&function(s){return new Promise(function(a,l){!function i(o,s,a,l){Promise.resolve(l).then(function(c){o({value:c,done:a})},s)}(a,l,(s=t[o](s)).done,s.value)})}}}"function"==typeof SuppressedError&&SuppressedError;const Hl=t=>t&&"number"==typeof t.length&&"function"!=typeof t;function Ef(t){return se(t?.then)}function Mf(t){return se(t[Fl])}function If(t){return Symbol.asyncIterator&&se(t?.[Symbol.asyncIterator])}function Sf(t){return new TypeError(`You provided ${null!==t&&"object"==typeof t?"an invalid object":`'${t}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}const Of=function fE(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}();function Tf(t){return se(t?.[Of])}function xf(t){return function bf(t,n,e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i,r=e.apply(t,n||[]),o=[];return i={},s("next"),s("throw"),s("return"),i[Symbol.asyncIterator]=function(){return this},i;function s(h){r[h]&&(i[h]=function(f){return new Promise(function(g,v){o.push([h,f,g,v])>1||a(h,f)})})}function a(h,f){try{!function l(h){h.value instanceof Pn?Promise.resolve(h.value.v).then(c,u):d(o[0][2],h)}(r[h](f))}catch(g){d(o[0][3],g)}}function c(h){a("next",h)}function u(h){a("throw",h)}function d(h,f){h(f),o.shift(),o.length&&a(o[0][0],o[0][1])}}(this,arguments,function*(){const e=t.getReader();try{for(;;){const{value:r,done:i}=yield Pn(e.read());if(i)return yield Pn(void 0);yield yield Pn(r)}}finally{e.releaseLock()}})}function Af(t){return se(t?.getReader)}function st(t){if(t instanceof ve)return t;if(null!=t){if(Mf(t))return function pE(t){return new ve(n=>{const e=t[Fl]();if(se(e.subscribe))return e.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(t);if(Hl(t))return function gE(t){return new ve(n=>{for(let e=0;e<t.length&&!n.closed;e++)n.next(t[e]);n.complete()})}(t);if(Ef(t))return function mE(t){return new ve(n=>{t.then(e=>{n.closed||(n.next(e),n.complete())},e=>n.error(e)).then(null,mf)})}(t);if(If(t))return kf(t);if(Tf(t))return function _E(t){return new ve(n=>{for(const e of t)if(n.next(e),n.closed)return;n.complete()})}(t);if(Af(t))return function vE(t){return kf(xf(t))}(t)}throw Sf(t)}function kf(t){return new ve(n=>{(function yE(t,n){var e,r,i,o;return function Cf(t,n,e,r){return new(e||(e=Promise))(function(o,s){function a(u){try{c(r.next(u))}catch(d){s(d)}}function l(u){try{c(r.throw(u))}catch(d){s(d)}}function c(u){u.done?o(u.value):function i(o){return o instanceof e?o:new e(function(s){s(o)})}(u.value).then(a,l)}c((r=r.apply(t,n||[])).next())})}(this,void 0,void 0,function*(){try{for(e=wf(t);!(r=yield e.next()).done;)if(n.next(r.value),n.closed)return}catch(s){i={error:s}}finally{try{r&&!r.done&&(o=e.return)&&(yield o.call(e))}finally{if(i)throw i.error}}n.complete()})})(t,n).catch(e=>n.error(e))})}function vn(t,n,e,r=0,i=!1){const o=n.schedule(function(){e(),i?t.add(this.schedule(null,r)):this.unsubscribe()},r);if(t.add(o),!i)return o}function Pi(t,n,e=1/0){return se(n)?Pi((r,i)=>ze((o,s)=>n(r,o,i,s))(st(t(r,i))),e):("number"==typeof n&&(e=n),Je((r,i)=>function CE(t,n,e,r,i,o,s,a){const l=[];let c=0,u=0,d=!1;const h=()=>{d&&!l.length&&!c&&n.complete()},f=v=>c<r?g(v):l.push(v),g=v=>{o&&n.next(v),c++;let D=!1;st(e(v,u++)).subscribe(Ge(n,I=>{i?.(I),o?f(I):n.next(I)},()=>{D=!0},void 0,()=>{if(D)try{for(c--;l.length&&c<r;){const I=l.shift();s?vn(n,s,()=>g(I)):g(I)}h()}catch(I){n.error(I)}}))};return t.subscribe(Ge(n,f,()=>{d=!0,h()})),()=>{a?.()}}(r,i,t,e)))}function Rf(t=1/0){return Pi(Or,t)}const zl=new ve(t=>t.complete());function Pf(t){return t&&se(t.schedule)}function Ul(t){return t[t.length-1]}function Ni(t){return Pf(Ul(t))?t.pop():void 0}function Ff(t,n=0){return Je((e,r)=>{e.subscribe(Ge(r,i=>vn(r,t,()=>r.next(i),n),()=>vn(r,t,()=>r.complete(),n),i=>vn(r,t,()=>r.error(i),n)))})}function Lf(t,n=0){return Je((e,r)=>{r.add(t.schedule(()=>e.subscribe(r),n))})}function Vf(t,n){if(!t)throw new Error("Iterable cannot be null");return new ve(e=>{vn(e,n,()=>{const r=t[Symbol.asyncIterator]();vn(e,n,()=>{r.next().then(i=>{i.done?e.complete():e.next(i.value)})},0,!0)})})}function ir(t,n){return n?function OE(t,n){if(null!=t){if(Mf(t))return function wE(t,n){return st(t).pipe(Lf(n),Ff(n))}(t,n);if(Hl(t))return function ME(t,n){return new ve(e=>{let r=0;return n.schedule(function(){r===t.length?e.complete():(e.next(t[r++]),e.closed||this.schedule())})})}(t,n);if(Ef(t))return function EE(t,n){return st(t).pipe(Lf(n),Ff(n))}(t,n);if(If(t))return Vf(t,n);if(Tf(t))return function IE(t,n){return new ve(e=>{let r;return vn(e,n,()=>{r=t[Of](),vn(e,n,()=>{let i,o;try{({value:i,done:o}=r.next())}catch(s){return void e.error(s)}o?e.complete():e.next(i)},0,!0)}),()=>se(r?.return)&&r.return()})}(t,n);if(Af(t))return function SE(t,n){return Vf(xf(t),n)}(t,n)}throw Sf(t)}(t,n):st(t)}function fs(...t){const n=Ni(t),e=function bE(t,n){return"number"==typeof Ul(t)?t.pop():n}(t,1/0),r=t;return r.length?1===r.length?st(r[0]):Rf(e)(ir(r,n)):zl}class Pe extends te{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){const e=super._subscribe(n);return!e.closed&&n.next(this._value),e}getValue(){const{hasError:n,thrownError:e,_value:r}=this;if(n)throw e;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}}function Pt(...t){return ir(t,Ni(t))}function $l(t={}){const{connector:n=(()=>new te),resetOnError:e=!0,resetOnComplete:r=!0,resetOnRefCountZero:i=!0}=t;return o=>{let s,a,l,c=0,u=!1,d=!1;const h=()=>{a?.unsubscribe(),a=void 0},f=()=>{h(),s=l=void 0,u=d=!1},g=()=>{const v=s;f(),v?.unsubscribe()};return Je((v,D)=>{c++,!d&&!u&&h();const I=l=l??n();D.add(()=>{c--,0===c&&!d&&!u&&(a=Gl(g,i))}),I.subscribe(D),!s&&c>0&&(s=new Ri({next:w=>I.next(w),error:w=>{d=!0,h(),a=Gl(f,e,w),I.error(w)},complete:()=>{u=!0,h(),a=Gl(f,r),I.complete()}}),st(v).subscribe(s))})(o)}}function Gl(t,n,...e){if(!0===n)return void t();if(!1===n)return;const r=new Ri({next:()=>{r.unsubscribe(),t()}});return st(n(...e)).subscribe(r)}function Tr(t,n){return Je((e,r)=>{let i=null,o=0,s=!1;const a=()=>s&&!i&&r.complete();e.subscribe(Ge(r,l=>{i?.unsubscribe();let c=0;const u=o++;st(t(l,u)).subscribe(i=Ge(r,d=>r.next(n?n(l,d,u,c++):d),()=>{i=null,a()}))},()=>{s=!0,a()}))})}function xE(t,n){return t===n}function me(t){for(let n in t)if(t[n]===me)return n;throw Error("Could not find renamed property on target object.")}function ps(t,n){for(const e in n)n.hasOwnProperty(e)&&!t.hasOwnProperty(e)&&(t[e]=n[e])}function Le(t){if("string"==typeof t)return t;if(Array.isArray(t))return"["+t.map(Le).join(", ")+"]";if(null==t)return""+t;if(t.overriddenName)return`${t.overriddenName}`;if(t.name)return`${t.name}`;const n=t.toString();if(null==n)return""+n;const e=n.indexOf("\n");return-1===e?n:n.substring(0,e)}function Wl(t,n){return null==t||""===t?null===n?"":n:null==n||""===n?t:t+" "+n}const AE=me({__forward_ref__:me});function ye(t){return t.__forward_ref__=ye,t.toString=function(){return Le(this())},t}function q(t){return ql(t)?t():t}function ql(t){return"function"==typeof t&&t.hasOwnProperty(AE)&&t.__forward_ref__===ye}function Zl(t){return t&&!!t.\u0275providers}const Bf="https://g.co/ng/security#xss";class O extends Error{constructor(n,e){super(function gs(t,n){return`NG0${Math.abs(t)}${n?": "+n:""}`}(n,e)),this.code=n}}function K(t){return"string"==typeof t?t:null==t?"":String(t)}function Kl(t,n){throw new O(-201,!1)}function Nt(t,n){null==t&&function $(t,n,e,r){throw new Error(`ASSERTION ERROR: ${t}`+(null==r?"":` [Expected=> ${e} ${r} ${n} <=Actual]`))}(n,t,null,"!=")}function Q(t){return{token:t.token,providedIn:t.providedIn||null,factory:t.factory,value:void 0}}function We(t){return{providers:t.providers||[],imports:t.imports||[]}}function ms(t){return jf(t,vs)||jf(t,Hf)}function jf(t,n){return t.hasOwnProperty(n)?t[n]:null}function _s(t){return t&&(t.hasOwnProperty(Ql)||t.hasOwnProperty(VE))?t[Ql]:null}const vs=me({\u0275prov:me}),Ql=me({\u0275inj:me}),Hf=me({ngInjectableDef:me}),VE=me({ngInjectorDef:me});var ae=function(t){return t[t.Default=0]="Default",t[t.Host=1]="Host",t[t.Self=2]="Self",t[t.SkipSelf=4]="SkipSelf",t[t.Optional=8]="Optional",t}(ae||{});let Yl;function _t(t){const n=Yl;return Yl=t,n}function Uf(t,n,e){const r=ms(t);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:e&ae.Optional?null:void 0!==n?n:void Kl(Le(t))}const Ce=globalThis;class P{constructor(n,e){this._desc=n,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof e?this.__NG_ELEMENT_ID__=e:void 0!==e&&(this.\u0275prov=Q({token:this,providedIn:e.providedIn||"root",factory:e.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}const Fi={},nc="__NG_DI_FLAG__",ys="ngTempTokenPath",HE=/\n/gm,Gf="__source";let xr;function Nn(t){const n=xr;return xr=t,n}function $E(t,n=ae.Default){if(void 0===xr)throw new O(-203,!1);return null===xr?Uf(t,void 0,n):xr.get(t,n&ae.Optional?null:void 0,n)}function j(t,n=ae.Default){return(function zf(){return Yl}()||$E)(q(t),n)}function Y(t,n=ae.Default){return j(t,Cs(n))}function Cs(t){return typeof t>"u"||"number"==typeof t?t:0|(t.optional&&8)|(t.host&&1)|(t.self&&2)|(t.skipSelf&&4)}function rc(t){const n=[];for(let e=0;e<t.length;e++){const r=q(t[e]);if(Array.isArray(r)){if(0===r.length)throw new O(900,!1);let i,o=ae.Default;for(let s=0;s<r.length;s++){const a=r[s],l=GE(a);"number"==typeof l?-1===l?i=a.token:o|=l:i=a}n.push(j(i,o))}else n.push(j(r))}return n}function Li(t,n){return t[nc]=n,t.prototype[nc]=n,t}function GE(t){return t[nc]}function yn(t){return{toString:t}.toString()}var Ds=function(t){return t[t.OnPush=0]="OnPush",t[t.Default=1]="Default",t}(Ds||{}),zt=function(t){return t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom",t}(zt||{});const rn={},ue=[],bs=me({\u0275cmp:me}),ic=me({\u0275dir:me}),oc=me({\u0275pipe:me}),qf=me({\u0275mod:me}),Cn=me({\u0275fac:me}),Vi=me({__NG_ELEMENT_ID__:me}),Zf=me({__NG_ENV_ID__:me});function Kf(t,n,e){let r=t.length;for(;;){const i=t.indexOf(n,e);if(-1===i)return i;if(0===i||t.charCodeAt(i-1)<=32){const o=n.length;if(i+o===r||t.charCodeAt(i+o)<=32)return i}e=i+1}}function sc(t,n,e){let r=0;for(;r<e.length;){const i=e[r];if("number"==typeof i){if(0!==i)break;r++;const o=e[r++],s=e[r++],a=e[r++];t.setAttribute(n,s,a,o)}else{const o=i,s=e[++r];Yf(o)?t.setProperty(n,o,s):t.setAttribute(n,o,s),r++}}return r}function Qf(t){return 3===t||4===t||6===t}function Yf(t){return 64===t.charCodeAt(0)}function Bi(t,n){if(null!==n&&0!==n.length)if(null===t||0===t.length)t=n.slice();else{let e=-1;for(let r=0;r<n.length;r++){const i=n[r];"number"==typeof i?e=i:0===e||Xf(t,e,i,null,-1===e||2===e?n[++r]:null)}}return t}function Xf(t,n,e,r,i){let o=0,s=t.length;if(-1===n)s=-1;else for(;o<t.length;){const a=t[o++];if("number"==typeof a){if(a===n){s=-1;break}if(a>n){s=o-1;break}}}for(;o<t.length;){const a=t[o];if("number"==typeof a)break;if(a===e){if(null===r)return void(null!==i&&(t[o+1]=i));if(r===t[o+1])return void(t[o+2]=i)}o++,null!==r&&o++,null!==i&&o++}-1!==s&&(t.splice(s,0,n),o=s+1),t.splice(o++,0,e),null!==r&&t.splice(o++,0,r),null!==i&&t.splice(o++,0,i)}const Jf="ng-template";function ZE(t,n,e){let r=0,i=!0;for(;r<t.length;){let o=t[r++];if("string"==typeof o&&i){const s=t[r++];if(e&&"class"===o&&-1!==Kf(s.toLowerCase(),n,0))return!0}else{if(1===o){for(;r<t.length&&"string"==typeof(o=t[r++]);)if(o.toLowerCase()===n)return!0;return!1}"number"==typeof o&&(i=!1)}}return!1}function ep(t){return 4===t.type&&t.value!==Jf}function KE(t,n,e){return n===(4!==t.type||e?t.value:Jf)}function QE(t,n,e){let r=4;const i=t.attrs||[],o=function JE(t){for(let n=0;n<t.length;n++)if(Qf(t[n]))return n;return t.length}(i);let s=!1;for(let a=0;a<n.length;a++){const l=n[a];if("number"!=typeof l){if(!s)if(4&r){if(r=2|1&r,""!==l&&!KE(t,l,e)||""===l&&1===n.length){if(Ut(r))return!1;s=!0}}else{const c=8&r?l:n[++a];if(8&r&&null!==t.attrs){if(!ZE(t.attrs,c,e)){if(Ut(r))return!1;s=!0}continue}const d=YE(8&r?"class":l,i,ep(t),e);if(-1===d){if(Ut(r))return!1;s=!0;continue}if(""!==c){let h;h=d>o?"":i[d+1].toLowerCase();const f=8&r?h:null;if(f&&-1!==Kf(f,c,0)||2&r&&c!==h){if(Ut(r))return!1;s=!0}}}}else{if(!s&&!Ut(r)&&!Ut(l))return!1;if(s&&Ut(l))continue;s=!1,r=l|1&r}}return Ut(r)||s}function Ut(t){return 0==(1&t)}function YE(t,n,e,r){if(null===n)return-1;let i=0;if(r||!e){let o=!1;for(;i<n.length;){const s=n[i];if(s===t)return i;if(3===s||6===s)o=!0;else{if(1===s||2===s){let a=n[++i];for(;"string"==typeof a;)a=n[++i];continue}if(4===s)break;if(0===s){i+=4;continue}}i+=o?1:2}return-1}return function e0(t,n){let e=t.indexOf(4);if(e>-1)for(e++;e<t.length;){const r=t[e];if("number"==typeof r)return-1;if(r===n)return e;e++}return-1}(n,t)}function tp(t,n,e=!1){for(let r=0;r<n.length;r++)if(QE(t,n[r],e))return!0;return!1}function t0(t,n){e:for(let e=0;e<n.length;e++){const r=n[e];if(t.length===r.length){for(let i=0;i<t.length;i++)if(t[i]!==r[i])continue e;return!0}}return!1}function np(t,n){return t?":not("+n.trim()+")":n}function n0(t){let n=t[0],e=1,r=2,i="",o=!1;for(;e<t.length;){let s=t[e];if("string"==typeof s)if(2&r){const a=t[++e];i+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else 8&r?i+="."+s:4&r&&(i+=" "+s);else""!==i&&!Ut(s)&&(n+=np(o,i),i=""),r=s,o=o||!Ut(r);e++}return""!==i&&(n+=np(o,i)),n}function $t(t){return yn(()=>{const n=ip(t),e={...n,decls:t.decls,vars:t.vars,template:t.template,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,onPush:t.changeDetection===Ds.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&t.dependencies||null,getStandaloneInjector:null,signals:t.signals??!1,data:t.data||{},encapsulation:t.encapsulation||zt.Emulated,styles:t.styles||ue,_:null,schemas:t.schemas||null,tView:null,id:""};op(e);const r=t.dependencies;return e.directiveDefs=ws(r,!1),e.pipeDefs=ws(r,!0),e.id=function d0(t){let n=0;const e=[t.selectors,t.ngContentSelectors,t.hostVars,t.hostAttrs,t.consts,t.vars,t.decls,t.encapsulation,t.standalone,t.signals,t.exportAs,JSON.stringify(t.inputs),JSON.stringify(t.outputs),Object.getOwnPropertyNames(t.type.prototype),!!t.contentQueries,!!t.viewQuery].join("|");for(const i of e)n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}(e),e})}function a0(t){return le(t)||qe(t)}function l0(t){return null!==t}function et(t){return yn(()=>({type:t.type,bootstrap:t.bootstrap||ue,declarations:t.declarations||ue,imports:t.imports||ue,exports:t.exports||ue,transitiveCompileScopes:null,schemas:t.schemas||null,id:t.id||null}))}function rp(t,n){if(null==t)return rn;const e={};for(const r in t)if(t.hasOwnProperty(r)){let i=t[r],o=i;Array.isArray(i)&&(o=i[1],i=i[0]),e[i]=r,n&&(n[i]=o)}return e}function H(t){return yn(()=>{const n=ip(t);return op(n),n})}function at(t){return{type:t.type,name:t.name,factory:null,pure:!1!==t.pure,standalone:!0===t.standalone,onDestroy:t.type.prototype.ngOnDestroy||null}}function le(t){return t[bs]||null}function qe(t){return t[ic]||null}function lt(t){return t[oc]||null}function ip(t){const n={};return{type:t.type,providersResolver:null,factory:null,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:n,inputTransforms:null,inputConfig:t.inputs||rn,exportAs:t.exportAs||null,standalone:!0===t.standalone,signals:!0===t.signals,selectors:t.selectors||ue,viewQuery:t.viewQuery||null,features:t.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:rp(t.inputs,n),outputs:rp(t.outputs)}}function op(t){t.features?.forEach(n=>n(t))}function ws(t,n){if(!t)return null;const e=n?lt:a0;return()=>("function"==typeof t?t():t).map(r=>e(r)).filter(l0)}const Oe=0,F=1,ee=2,Me=3,Gt=4,Hi=5,tt=6,Ar=7,xe=8,Fn=9,kr=10,X=11,zi=12,sp=13,Rr=14,Ae=15,Ui=16,Pr=17,on=18,$i=19,ap=20,Ln=21,Dn=22,Gi=23,Wi=24,ie=25,ac=1,lp=2,sn=7,Nr=9,Ze=11;function vt(t){return Array.isArray(t)&&"object"==typeof t[ac]}function ct(t){return Array.isArray(t)&&!0===t[ac]}function lc(t){return 0!=(4&t.flags)}function sr(t){return t.componentOffset>-1}function Ms(t){return 1==(1&t.flags)}function Wt(t){return!!t.template}function cc(t){return 0!=(512&t[ee])}function ar(t,n){return t.hasOwnProperty(Cn)?t[Cn]:null}let Ke=null,Is=!1;function Ft(t){const n=Ke;return Ke=t,n}const dp={version:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{}};function fp(t){if(!Zi(t)||t.dirty){if(!t.producerMustRecompute(t)&&!mp(t))return void(t.dirty=!1);t.producerRecomputeValue(t),t.dirty=!1}}function gp(t){t.dirty=!0,function pp(t){if(void 0===t.liveConsumerNode)return;const n=Is;Is=!0;try{for(const e of t.liveConsumerNode)e.dirty||gp(e)}finally{Is=n}}(t),t.consumerMarkedDirty?.(t)}function dc(t){return t&&(t.nextProducerIndex=0),Ft(t)}function hc(t,n){if(Ft(n),t&&void 0!==t.producerNode&&void 0!==t.producerIndexOfThis&&void 0!==t.producerLastReadVersion){if(Zi(t))for(let e=t.nextProducerIndex;e<t.producerNode.length;e++)Ss(t.producerNode[e],t.producerIndexOfThis[e]);for(;t.producerNode.length>t.nextProducerIndex;)t.producerNode.pop(),t.producerLastReadVersion.pop(),t.producerIndexOfThis.pop()}}function mp(t){Fr(t);for(let n=0;n<t.producerNode.length;n++){const e=t.producerNode[n],r=t.producerLastReadVersion[n];if(r!==e.version||(fp(e),r!==e.version))return!0}return!1}function _p(t){if(Fr(t),Zi(t))for(let n=0;n<t.producerNode.length;n++)Ss(t.producerNode[n],t.producerIndexOfThis[n]);t.producerNode.length=t.producerLastReadVersion.length=t.producerIndexOfThis.length=0,t.liveConsumerNode&&(t.liveConsumerNode.length=t.liveConsumerIndexOfThis.length=0)}function Ss(t,n){if(function yp(t){t.liveConsumerNode??=[],t.liveConsumerIndexOfThis??=[]}(t),Fr(t),1===t.liveConsumerNode.length)for(let r=0;r<t.producerNode.length;r++)Ss(t.producerNode[r],t.producerIndexOfThis[r]);const e=t.liveConsumerNode.length-1;if(t.liveConsumerNode[n]=t.liveConsumerNode[e],t.liveConsumerIndexOfThis[n]=t.liveConsumerIndexOfThis[e],t.liveConsumerNode.length--,t.liveConsumerIndexOfThis.length--,n<t.liveConsumerNode.length){const r=t.liveConsumerIndexOfThis[n],i=t.liveConsumerNode[n];Fr(i),i.producerIndexOfThis[r]=n}}function Zi(t){return t.consumerIsAlwaysLive||(t?.liveConsumerNode?.length??0)>0}function Fr(t){t.producerNode??=[],t.producerIndexOfThis??=[],t.producerLastReadVersion??=[]}let Cp=null;function wp(t){const n=Ft(null);try{return t()}finally{Ft(n)}}const Ep=()=>{},E0=(()=>({...dp,consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:t=>{t.schedule(t.ref)},hasRun:!1,cleanupFn:Ep}))();class M0{constructor(n,e,r){this.previousValue=n,this.currentValue=e,this.firstChange=r}isFirstChange(){return this.firstChange}}function Qe(){return Mp}function Mp(t){return t.type.prototype.ngOnChanges&&(t.setInput=S0),I0}function I0(){const t=Sp(this),n=t?.current;if(n){const e=t.previous;if(e===rn)t.previous=n;else for(let r in n)e[r]=n[r];t.current=null,this.ngOnChanges(n)}}function S0(t,n,e,r){const i=this.declaredInputs[e],o=Sp(t)||function O0(t,n){return t[Ip]=n}(t,{previous:rn,current:null}),s=o.current||(o.current={}),a=o.previous,l=a[i];s[i]=new M0(l&&l.currentValue,n,a===rn),t[r]=n}Qe.ngInherit=!0;const Ip="__ngSimpleChanges__";function Sp(t){return t[Ip]||null}const an=function(t,n,e){};function De(t){for(;Array.isArray(t);)t=t[Oe];return t}function Os(t,n){return De(n[t])}function yt(t,n){return De(n[t.index])}function xp(t,n){return t.data[n]}function Ot(t,n){const e=n[t];return vt(e)?e:e[Oe]}function Bn(t,n){return null==n?null:t[n]}function Ap(t){t[Pr]=0}function P0(t){1024&t[ee]||(t[ee]|=1024,Rp(t,1))}function kp(t){1024&t[ee]&&(t[ee]&=-1025,Rp(t,-1))}function Rp(t,n){let e=t[Me];if(null===e)return;e[Hi]+=n;let r=e;for(e=e[Me];null!==e&&(1===n&&1===r[Hi]||-1===n&&0===r[Hi]);)e[Hi]+=n,r=e,e=e[Me]}const G={lFrame:$p(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function Fp(){return G.bindingsEnabled}function Vr(){return null!==G.skipHydrationRootTNode}function E(){return G.lFrame.lView}function ce(){return G.lFrame.tView}function T(t){return G.lFrame.contextLView=t,t[xe]}function x(t){return G.lFrame.contextLView=null,t}function Ye(){let t=Lp();for(;null!==t&&64===t.type;)t=t.parent;return t}function Lp(){return G.lFrame.currentTNode}function ln(t,n){const e=G.lFrame;e.currentTNode=t,e.isParent=n}function _c(){return G.lFrame.isParent}function vc(){G.lFrame.isParent=!1}function ut(){const t=G.lFrame;let n=t.bindingRootIndex;return-1===n&&(n=t.bindingRootIndex=t.tView.bindingStartIndex),n}function Br(){return G.lFrame.bindingIndex++}function wn(t){const n=G.lFrame,e=n.bindingIndex;return n.bindingIndex=n.bindingIndex+t,e}function W0(t,n){const e=G.lFrame;e.bindingIndex=e.bindingRootIndex=t,yc(n)}function yc(t){G.lFrame.currentDirectiveIndex=t}function Hp(){return G.lFrame.currentQueryIndex}function Dc(t){G.lFrame.currentQueryIndex=t}function Z0(t){const n=t[F];return 2===n.type?n.declTNode:1===n.type?t[tt]:null}function zp(t,n,e){if(e&ae.SkipSelf){let i=n,o=t;for(;!(i=i.parent,null!==i||e&ae.Host||(i=Z0(o),null===i||(o=o[Rr],10&i.type))););if(null===i)return!1;n=i,t=o}const r=G.lFrame=Up();return r.currentTNode=n,r.lView=t,!0}function bc(t){const n=Up(),e=t[F];G.lFrame=n,n.currentTNode=e.firstChild,n.lView=t,n.tView=e,n.contextLView=t,n.bindingIndex=e.bindingStartIndex,n.inI18n=!1}function Up(){const t=G.lFrame,n=null===t?null:t.child;return null===n?$p(t):n}function $p(t){const n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:t,child:null,inI18n:!1};return null!==t&&(t.child=n),n}function Gp(){const t=G.lFrame;return G.lFrame=t.parent,t.currentTNode=null,t.lView=null,t}const Wp=Gp;function wc(){const t=Gp();t.isParent=!0,t.tView=null,t.selectedIndex=-1,t.contextLView=null,t.elementDepthCount=0,t.currentDirectiveIndex=-1,t.currentNamespace=null,t.bindingRootIndex=-1,t.bindingIndex=-1,t.currentQueryIndex=0}function dt(){return G.lFrame.selectedIndex}function lr(t){G.lFrame.selectedIndex=t}function Ie(){const t=G.lFrame;return xp(t.tView,t.selectedIndex)}let Zp=!0;function Ts(){return Zp}function jn(t){Zp=t}function xs(t,n){for(let e=n.directiveStart,r=n.directiveEnd;e<r;e++){const o=t.data[e].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=o;s&&(t.contentHooks??=[]).push(-e,s),a&&((t.contentHooks??=[]).push(e,a),(t.contentCheckHooks??=[]).push(e,a)),l&&(t.viewHooks??=[]).push(-e,l),c&&((t.viewHooks??=[]).push(e,c),(t.viewCheckHooks??=[]).push(e,c)),null!=u&&(t.destroyHooks??=[]).push(e,u)}}function As(t,n,e){Kp(t,n,3,e)}function ks(t,n,e,r){(3&t[ee])===e&&Kp(t,n,e,r)}function Ec(t,n){let e=t[ee];(3&e)===n&&(e&=8191,e+=1,t[ee]=e)}function Kp(t,n,e,r){const o=r??-1,s=n.length-1;let a=0;for(let l=void 0!==r?65535&t[Pr]:0;l<s;l++)if("number"==typeof n[l+1]){if(a=n[l],null!=r&&a>=r)break}else n[l]<0&&(t[Pr]+=65536),(a<o||-1==o)&&(nM(t,e,n,l),t[Pr]=(**********&t[Pr])+l+2),l++}function Qp(t,n){an(4,t,n);const e=Ft(null);try{n.call(t)}finally{Ft(e),an(5,t,n)}}function nM(t,n,e,r){const i=e[r]<0,o=e[r+1],a=t[i?-e[r]:e[r]];i?t[ee]>>13<t[Pr]>>16&&(3&t[ee])===n&&(t[ee]+=8192,Qp(a,o)):Qp(a,o)}const jr=-1;class Qi{constructor(n,e,r){this.factory=n,this.resolving=!1,this.canSeeViewProviders=e,this.injectImpl=r}}function Ic(t){return t!==jr}function Yi(t){return 32767&t}function Xi(t,n){let e=function sM(t){return t>>16}(t),r=n;for(;e>0;)r=r[Rr],e--;return r}let Sc=!0;function Rs(t){const n=Sc;return Sc=t,n}const Yp=255,Xp=5;let aM=0;const cn={};function Ps(t,n){const e=Jp(t,n);if(-1!==e)return e;const r=n[F];r.firstCreatePass&&(t.injectorIndex=n.length,Oc(r.data,t),Oc(n,null),Oc(r.blueprint,null));const i=Ns(t,n),o=t.injectorIndex;if(Ic(i)){const s=Yi(i),a=Xi(i,n),l=a[F].data;for(let c=0;c<8;c++)n[o+c]=a[s+c]|l[s+c]}return n[o+8]=i,o}function Oc(t,n){t.push(0,0,0,0,0,0,0,0,n)}function Jp(t,n){return-1===t.injectorIndex||t.parent&&t.parent.injectorIndex===t.injectorIndex||null===n[t.injectorIndex+8]?-1:t.injectorIndex}function Ns(t,n){if(t.parent&&-1!==t.parent.injectorIndex)return t.parent.injectorIndex;let e=0,r=null,i=n;for(;null!==i;){if(r=sg(i),null===r)return jr;if(e++,i=i[Rr],-1!==r.injectorIndex)return r.injectorIndex|e<<16}return jr}function Tc(t,n,e){!function lM(t,n,e){let r;"string"==typeof e?r=e.charCodeAt(0)||0:e.hasOwnProperty(Vi)&&(r=e[Vi]),null==r&&(r=e[Vi]=aM++);const i=r&Yp;n.data[t+(i>>Xp)]|=1<<i}(t,n,e)}function eg(t,n,e){if(e&ae.Optional||void 0!==t)return t;Kl()}function tg(t,n,e,r){if(e&ae.Optional&&void 0===r&&(r=null),!(e&(ae.Self|ae.Host))){const i=t[Fn],o=_t(void 0);try{return i?i.get(n,r,e&ae.Optional):Uf(n,r,e&ae.Optional)}finally{_t(o)}}return eg(r,0,e)}function ng(t,n,e,r=ae.Default,i){if(null!==t){if(2048&n[ee]&&!(r&ae.Self)){const s=function pM(t,n,e,r,i){let o=t,s=n;for(;null!==o&&null!==s&&2048&s[ee]&&!(512&s[ee]);){const a=rg(o,s,e,r|ae.Self,cn);if(a!==cn)return a;let l=o.parent;if(!l){const c=s[ap];if(c){const u=c.get(e,cn,r);if(u!==cn)return u}l=sg(s),s=s[Rr]}o=l}return i}(t,n,e,r,cn);if(s!==cn)return s}const o=rg(t,n,e,r,cn);if(o!==cn)return o}return tg(n,e,r,i)}function rg(t,n,e,r,i){const o=function dM(t){if("string"==typeof t)return t.charCodeAt(0)||0;const n=t.hasOwnProperty(Vi)?t[Vi]:void 0;return"number"==typeof n?n>=0?n&Yp:fM:n}(e);if("function"==typeof o){if(!zp(n,t,r))return r&ae.Host?eg(i,0,r):tg(n,e,r,i);try{let s;if(s=o(r),null!=s||r&ae.Optional)return s;Kl()}finally{Wp()}}else if("number"==typeof o){let s=null,a=Jp(t,n),l=jr,c=r&ae.Host?n[Ae][tt]:null;for((-1===a||r&ae.SkipSelf)&&(l=-1===a?Ns(t,n):n[a+8],l!==jr&&og(r,!1)?(s=n[F],a=Yi(l),n=Xi(l,n)):a=-1);-1!==a;){const u=n[F];if(ig(o,a,u.data)){const d=uM(a,n,e,s,r,c);if(d!==cn)return d}l=n[a+8],l!==jr&&og(r,n[F].data[a+8]===c)&&ig(o,a,n)?(s=u,a=Yi(l),n=Xi(l,n)):a=-1}}return i}function uM(t,n,e,r,i,o){const s=n[F],a=s.data[t+8],u=Fs(a,s,e,null==r?sr(a)&&Sc:r!=s&&0!=(3&a.type),i&ae.Host&&o===a);return null!==u?cr(n,s,u,a):cn}function Fs(t,n,e,r,i){const o=t.providerIndexes,s=n.data,a=1048575&o,l=t.directiveStart,u=o>>20,h=i?a+u:t.directiveEnd;for(let f=r?a:a+u;f<h;f++){const g=s[f];if(f<l&&e===g||f>=l&&g.type===e)return f}if(i){const f=s[l];if(f&&Wt(f)&&f.type===e)return l}return null}function cr(t,n,e,r){let i=t[e];const o=n.data;if(function rM(t){return t instanceof Qi}(i)){const s=i;s.resolving&&function kE(t,n){const e=n?`. Dependency path: ${n.join(" > ")} > ${t}`:"";throw new O(-200,`Circular dependency in DI detected for ${t}${e}`)}(function pe(t){return"function"==typeof t?t.name||t.toString():"object"==typeof t&&null!=t&&"function"==typeof t.type?t.type.name||t.type.toString():K(t)}(o[e]));const a=Rs(s.canSeeViewProviders);s.resolving=!0;const c=s.injectImpl?_t(s.injectImpl):null;zp(t,r,ae.Default);try{i=t[e]=s.factory(void 0,o,t,r),n.firstCreatePass&&e>=r.directiveStart&&function tM(t,n,e){const{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=n.type.prototype;if(r){const s=Mp(n);(e.preOrderHooks??=[]).push(t,s),(e.preOrderCheckHooks??=[]).push(t,s)}i&&(e.preOrderHooks??=[]).push(0-t,i),o&&((e.preOrderHooks??=[]).push(t,o),(e.preOrderCheckHooks??=[]).push(t,o))}(e,o[e],n)}finally{null!==c&&_t(c),Rs(a),s.resolving=!1,Wp()}}return i}function ig(t,n,e){return!!(e[n+(t>>Xp)]&1<<t)}function og(t,n){return!(t&ae.Self||t&ae.Host&&n)}class ht{constructor(n,e){this._tNode=n,this._lView=e}get(n,e,r){return ng(this._tNode,this._lView,n,Cs(r),e)}}function fM(){return new ht(Ye(),E())}function ke(t){return yn(()=>{const n=t.prototype.constructor,e=n[Cn]||xc(n),r=Object.prototype;let i=Object.getPrototypeOf(t.prototype).constructor;for(;i&&i!==r;){const o=i[Cn]||xc(i);if(o&&o!==e)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function xc(t){return ql(t)?()=>{const n=xc(q(t));return n&&n()}:ar(t)}function sg(t){const n=t[F],e=n.type;return 2===e?n.declTNode:1===e?t[tt]:null}const zr="__parameters__";function $r(t,n,e){return yn(()=>{const r=function kc(t){return function(...e){if(t){const r=t(...e);for(const i in r)this[i]=r[i]}}}(n);function i(...o){if(this instanceof i)return r.apply(this,o),this;const s=new i(...o);return a.annotation=s,a;function a(l,c,u){const d=l.hasOwnProperty(zr)?l[zr]:Object.defineProperty(l,zr,{value:[]})[zr];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return e&&(i.prototype=Object.create(e.prototype)),i.prototype.ngMetadataName=t,i.annotationCls=i,i})}function Wr(t,n){t.forEach(e=>Array.isArray(e)?Wr(e,n):n(e))}function lg(t,n,e){n>=t.length?t.push(e):t.splice(n,0,e)}function Ls(t,n){return n>=t.length-1?t.pop():t.splice(n,1)[0]}function to(t,n){const e=[];for(let r=0;r<t;r++)e.push(n);return e}function Tt(t,n,e){let r=qr(t,n);return r>=0?t[1|r]=e:(r=~r,function DM(t,n,e,r){let i=t.length;if(i==n)t.push(e,r);else if(1===i)t.push(r,t[0]),t[0]=e;else{for(i--,t.push(t[i-1],t[i]);i>n;)t[i]=t[i-2],i--;t[n]=e,t[n+1]=r}}(t,r,n,e)),r}function Rc(t,n){const e=qr(t,n);if(e>=0)return t[1|e]}function qr(t,n){return function cg(t,n,e){let r=0,i=t.length>>e;for(;i!==r;){const o=r+(i-r>>1),s=t[o<<e];if(n===s)return o<<e;s>n?i=o:r=o+1}return~(i<<e)}(t,n,1)}const Nc=Li($r("Optional"),8),Fc=Li($r("SkipSelf"),4);function Us(t){return 128==(128&t.flags)}var Hn=function(t){return t[t.Important=1]="Important",t[t.DashCase=2]="DashCase",t}(Hn||{});const zM=/^>|^->|<!--|-->|--!>|<!-$/g,UM=/(<|>)/g,$M="\u200b$1\u200b";const jc=new Map;let GM=0;const zc="__ngContext__";function nt(t,n){vt(n)?(t[zc]=n[$i],function qM(t){jc.set(t[$i],t)}(n)):t[zc]=n}let Uc;function $c(t,n){return Uc(t,n)}function io(t){const n=t[Me];return ct(n)?n[Me]:n}function Tg(t){return Ag(t[zi])}function xg(t){return Ag(t[Gt])}function Ag(t){for(;null!==t&&!ct(t);)t=t[Gt];return t}function Qr(t,n,e,r,i){if(null!=r){let o,s=!1;ct(r)?o=r:vt(r)&&(s=!0,r=r[Oe]);const a=De(r);0===t&&null!==e?null==i?Ng(n,e,a):ur(n,e,a,i||null,!0):1===t&&null!==e?ur(n,e,a,i||null,!0):2===t?function Qs(t,n,e){const r=Zs(t,n);r&&function hI(t,n,e,r){t.removeChild(n,e,r)}(t,r,n,e)}(n,a,s):3===t&&n.destroyNode(a),null!=o&&function gI(t,n,e,r,i){const o=e[sn];o!==De(e)&&Qr(n,t,r,o,i);for(let a=Ze;a<e.length;a++){const l=e[a];so(l[F],l,t,n,r,o)}}(n,t,o,e,i)}}function Gc(t,n){return t.createComment(function Dg(t){return t.replace(zM,n=>n.replace(UM,$M))}(n))}function Ws(t,n,e){return t.createElement(n,e)}function Rg(t,n){const e=t[Nr],r=e.indexOf(n);kp(n),e.splice(r,1)}function qs(t,n){if(t.length<=Ze)return;const e=Ze+n,r=t[e];if(r){const i=r[Ui];null!==i&&i!==t&&Rg(i,r),n>0&&(t[e-1][Gt]=r[Gt]);const o=Ls(t,Ze+n);!function iI(t,n){so(t,n,n[X],2,null,null),n[Oe]=null,n[tt]=null}(r[F],r);const s=o[on];null!==s&&s.detachView(o[F]),r[Me]=null,r[Gt]=null,r[ee]&=-129}return r}function Wc(t,n){if(!(256&n[ee])){const e=n[X];n[Gi]&&_p(n[Gi]),n[Wi]&&_p(n[Wi]),e.destroyNode&&so(t,n,e,3,null,null),function aI(t){let n=t[zi];if(!n)return qc(t[F],t);for(;n;){let e=null;if(vt(n))e=n[zi];else{const r=n[Ze];r&&(e=r)}if(!e){for(;n&&!n[Gt]&&n!==t;)vt(n)&&qc(n[F],n),n=n[Me];null===n&&(n=t),vt(n)&&qc(n[F],n),e=n&&n[Gt]}n=e}}(n)}}function qc(t,n){if(!(256&n[ee])){n[ee]&=-129,n[ee]|=256,function dI(t,n){let e;if(null!=t&&null!=(e=t.destroyHooks))for(let r=0;r<e.length;r+=2){const i=n[e[r]];if(!(i instanceof Qi)){const o=e[r+1];if(Array.isArray(o))for(let s=0;s<o.length;s+=2){const a=i[o[s]],l=o[s+1];an(4,a,l);try{l.call(a)}finally{an(5,a,l)}}else{an(4,i,o);try{o.call(i)}finally{an(5,i,o)}}}}}(t,n),function uI(t,n){const e=t.cleanup,r=n[Ar];if(null!==e)for(let o=0;o<e.length-1;o+=2)if("string"==typeof e[o]){const s=e[o+3];s>=0?r[s]():r[-s].unsubscribe(),o+=2}else e[o].call(r[e[o+1]]);null!==r&&(n[Ar]=null);const i=n[Ln];if(null!==i){n[Ln]=null;for(let o=0;o<i.length;o++)(0,i[o])()}}(t,n),1===n[F].type&&n[X].destroy();const e=n[Ui];if(null!==e&&ct(n[Me])){e!==n[Me]&&Rg(e,n);const r=n[on];null!==r&&r.detachView(t)}!function ZM(t){jc.delete(t[$i])}(n)}}function Zc(t,n,e){return function Pg(t,n,e){let r=n;for(;null!==r&&40&r.type;)r=(n=r).parent;if(null===r)return e[Oe];{const{componentOffset:i}=r;if(i>-1){const{encapsulation:o}=t.data[r.directiveStart+i];if(o===zt.None||o===zt.Emulated)return null}return yt(r,e)}}(t,n.parent,e)}function ur(t,n,e,r,i){t.insertBefore(n,e,r,i)}function Ng(t,n,e){t.appendChild(n,e)}function Fg(t,n,e,r,i){null!==r?ur(t,n,e,r,i):Ng(t,n,e)}function Zs(t,n){return t.parentNode(n)}function Lg(t,n,e){return Bg(t,n,e)}let Kc,Jc,Bg=function Vg(t,n,e){return 40&t.type?yt(t,e):null};function Ks(t,n,e,r){const i=Zc(t,r,n),o=n[X],a=Lg(r.parent||n[tt],r,n);if(null!=i)if(Array.isArray(e))for(let l=0;l<e.length;l++)Fg(o,i,e[l],a,!1);else Fg(o,i,e,a,!1);void 0!==Kc&&Kc(o,r,n,e,i)}function oo(t,n){if(null!==n){const e=n.type;if(3&e)return yt(n,t);if(4&e)return Qc(-1,t[n.index]);if(8&e){const r=n.child;if(null!==r)return oo(t,r);{const i=t[n.index];return ct(i)?Qc(-1,i):De(i)}}if(32&e)return $c(n,t)()||De(t[n.index]);{const r=Hg(t,n);return null!==r?Array.isArray(r)?r[0]:oo(io(t[Ae]),r):oo(t,n.next)}}return null}function Hg(t,n){return null!==n?t[Ae][tt].projection[n.projection]:null}function Qc(t,n){const e=Ze+t+1;if(e<n.length){const r=n[e],i=r[F].firstChild;if(null!==i)return oo(r,i)}return n[sn]}function Yc(t,n,e,r,i,o,s){for(;null!=e;){const a=r[e.index],l=e.type;if(s&&0===n&&(a&&nt(De(a),r),e.flags|=2),32!=(32&e.flags))if(8&l)Yc(t,n,e.child,r,i,o,!1),Qr(n,t,i,a,o);else if(32&l){const c=$c(e,r);let u;for(;u=c();)Qr(n,t,i,u,o);Qr(n,t,i,a,o)}else 16&l?Ug(t,n,r,e,i,o):Qr(n,t,i,a,o);e=s?e.projectionNext:e.next}}function so(t,n,e,r,i,o){Yc(e,r,t.firstChild,n,i,o,!1)}function Ug(t,n,e,r,i,o){const s=e[Ae],l=s[tt].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++)Qr(n,t,i,l[c],o);else{let c=l;const u=s[Me];Us(r)&&(c.flags|=128),Yc(t,n,c,u,i,o,!0)}}function $g(t,n,e){""===e?t.removeAttribute(n,"class"):t.setAttribute(n,"class",e)}function Gg(t,n,e){const{mergedAttrs:r,classes:i,styles:o}=e;null!==r&&sc(t,n,r),null!==i&&$g(t,n,i),null!==o&&function _I(t,n,e){t.setAttribute(n,"style",e)}(t,n,o)}class Kg{constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Bf})`}}function zn(t){return t instanceof Kg?t.changingThisBreaksApplicationSecurity:t}const AI=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;var Jr=function(t){return t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL",t}(Jr||{});function xt(t){const n=function co(){const t=E();return t&&t[kr].sanitizer}();return n?n.sanitize(Jr.URL,t)||"":function ao(t,n){const e=function SI(t){return t instanceof Kg&&t.getTypeName()||null}(t);if(null!=e&&e!==n){if("ResourceURL"===e&&"URL"===n)return!0;throw new Error(`Required a safe ${n}, got a ${e} (see ${Bf})`)}return e===n}(t,"URL")?zn(t):function tu(t){return(t=String(t)).match(AI)?t:"unsafe:"+t}(K(t))}const ea=new P("ENVIRONMENT_INITIALIZER"),rm=new P("INJECTOR",-1),im=new P("INJECTOR_DEF_TYPES");class ou{get(n,e=Fi){if(e===Fi){const r=new Error(`NullInjectorError: No provider for ${Le(n)}!`);throw r.name="NullInjectorError",r}return e}}function GI(...t){return{\u0275providers:om(0,t),\u0275fromNgModule:!0}}function om(t,...n){const e=[],r=new Set;let i;const o=s=>{e.push(s)};return Wr(n,s=>{const a=s;ta(a,o,[],r)&&(i||=[],i.push(a))}),void 0!==i&&sm(i,o),e}function sm(t,n){for(let e=0;e<t.length;e++){const{ngModule:r,providers:i}=t[e];au(i,o=>{n(o,r)})}}function ta(t,n,e,r){if(!(t=q(t)))return!1;let i=null,o=_s(t);const s=!o&&le(t);if(o||s){if(s&&!s.standalone)return!1;i=t}else{const l=t.ngModule;if(o=_s(l),!o)return!1;i=l}const a=r.has(i);if(s){if(a)return!1;if(r.add(i),s.dependencies){const l="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const c of l)ta(c,n,e,r)}}else{if(!o)return!1;{if(null!=o.imports&&!a){let c;r.add(i);try{Wr(o.imports,u=>{ta(u,n,e,r)&&(c||=[],c.push(u))})}finally{}void 0!==c&&sm(c,n)}if(!a){const c=ar(i)||(()=>new i);n({provide:i,useFactory:c,deps:ue},i),n({provide:im,useValue:i,multi:!0},i),n({provide:ea,useValue:()=>j(i),multi:!0},i)}const l=o.providers;if(null!=l&&!a){const c=t;au(l,u=>{n(u,c)})}}}return i!==t&&void 0!==t.providers}function au(t,n){for(let e of t)Zl(e)&&(e=e.\u0275providers),Array.isArray(e)?au(e,n):n(e)}const WI=me({provide:String,useValue:me});function lu(t){return null!==t&&"object"==typeof t&&WI in t}function dr(t){return"function"==typeof t}const cu=new P("Set Injector scope."),na={},ZI={};let uu;function ra(){return void 0===uu&&(uu=new ou),uu}class un{}class ei extends un{get destroyed(){return this._destroyed}constructor(n,e,r,i){super(),this.parent=e,this.source=r,this.scopes=i,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,hu(n,s=>this.processProvider(s)),this.records.set(rm,ti(void 0,this)),i.has("environment")&&this.records.set(un,ti(void 0,this));const o=this.records.get(cu);null!=o&&"string"==typeof o.value&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(im.multi,ue,ae.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{for(const e of this._ngOnDestroyHooks)e.ngOnDestroy();const n=this._onDestroyHooks;this._onDestroyHooks=[];for(const e of n)e()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear()}}onDestroy(n){return this.assertNotDestroyed(),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){this.assertNotDestroyed();const e=Nn(this),r=_t(void 0);try{return n()}finally{Nn(e),_t(r)}}get(n,e=Fi,r=ae.Default){if(this.assertNotDestroyed(),n.hasOwnProperty(Zf))return n[Zf](this);r=Cs(r);const o=Nn(this),s=_t(void 0);try{if(!(r&ae.SkipSelf)){let l=this.records.get(n);if(void 0===l){const c=function JI(t){return"function"==typeof t||"object"==typeof t&&t instanceof P}(n)&&ms(n);l=c&&this.injectableDefInScope(c)?ti(du(n),na):null,this.records.set(n,l)}if(null!=l)return this.hydrate(n,l)}return(r&ae.Self?ra():this.parent).get(n,e=r&ae.Optional&&e===Fi?null:e)}catch(a){if("NullInjectorError"===a.name){if((a[ys]=a[ys]||[]).unshift(Le(n)),o)throw a;return function WE(t,n,e,r){const i=t[ys];throw n[Gf]&&i.unshift(n[Gf]),t.message=function qE(t,n,e,r=null){t=t&&"\n"===t.charAt(0)&&"\u0275"==t.charAt(1)?t.slice(2):t;let i=Le(n);if(Array.isArray(n))i=n.map(Le).join(" -> ");else if("object"==typeof n){let o=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];o.push(s+":"+("string"==typeof a?JSON.stringify(a):Le(a)))}i=`{${o.join(", ")}}`}return`${e}${r?"("+r+")":""}[${i}]: ${t.replace(HE,"\n  ")}`}("\n"+t.message,i,e,r),t.ngTokenPath=i,t[ys]=null,t}(a,n,"R3InjectorError",this.source)}throw a}finally{_t(s),Nn(o)}}resolveInjectorInitializers(){const n=Nn(this),e=_t(void 0);try{const i=this.get(ea.multi,ue,ae.Self);for(const o of i)o()}finally{Nn(n),_t(e)}}toString(){const n=[],e=this.records;for(const r of e.keys())n.push(Le(r));return`R3Injector[${n.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new O(205,!1)}processProvider(n){let e=dr(n=q(n))?n:q(n&&n.provide);const r=function QI(t){return lu(t)?ti(void 0,t.useValue):ti(cm(t),na)}(n);if(dr(n)||!0!==n.multi)this.records.get(e);else{let i=this.records.get(e);i||(i=ti(void 0,na,!0),i.factory=()=>rc(i.multi),this.records.set(e,i)),e=n,i.multi.push(n)}this.records.set(e,r)}hydrate(n,e){return e.value===na&&(e.value=ZI,e.value=e.factory()),"object"==typeof e.value&&e.value&&function XI(t){return null!==t&&"object"==typeof t&&"function"==typeof t.ngOnDestroy}(e.value)&&this._ngOnDestroyHooks.add(e.value),e.value}injectableDefInScope(n){if(!n.providedIn)return!1;const e=q(n.providedIn);return"string"==typeof e?"any"===e||this.scopes.has(e):this.injectorDefTypes.has(e)}removeOnDestroy(n){const e=this._onDestroyHooks.indexOf(n);-1!==e&&this._onDestroyHooks.splice(e,1)}}function du(t){const n=ms(t),e=null!==n?n.factory:ar(t);if(null!==e)return e;if(t instanceof P)throw new O(204,!1);if(t instanceof Function)return function KI(t){const n=t.length;if(n>0)throw to(n,"?"),new O(204,!1);const e=function LE(t){return t&&(t[vs]||t[Hf])||null}(t);return null!==e?()=>e.factory(t):()=>new t}(t);throw new O(204,!1)}function cm(t,n,e){let r;if(dr(t)){const i=q(t);return ar(i)||du(i)}if(lu(t))r=()=>q(t.useValue);else if(function lm(t){return!(!t||!t.useFactory)}(t))r=()=>t.useFactory(...rc(t.deps||[]));else if(function am(t){return!(!t||!t.useExisting)}(t))r=()=>j(q(t.useExisting));else{const i=q(t&&(t.useClass||t.provide));if(!function YI(t){return!!t.deps}(t))return ar(i)||du(i);r=()=>new i(...rc(t.deps))}return r}function ti(t,n,e=!1){return{factory:t,value:n,multi:e?[]:void 0}}function hu(t,n){for(const e of t)Array.isArray(e)?hu(e,n):e&&Zl(e)?hu(e.\u0275providers,n):n(e)}const ia=new P("AppId",{providedIn:"root",factory:()=>eS}),eS="ng",um=new P("Platform Initializer"),Mn=new P("Platform ID",{providedIn:"platform",factory:()=>"unknown"}),dm=new P("CSP nonce",{providedIn:"root",factory:()=>function Xr(){if(void 0!==Jc)return Jc;if(typeof document<"u")return document;throw new O(210,!1)}().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});let hm=(t,n,e)=>null;function Cu(t,n,e=!1){return hm(t,n,e)}class uS{}class gm{}class hS{resolveComponentFactory(n){throw function dS(t){const n=Error(`No component factory found for ${Le(t)}.`);return n.ngComponent=t,n}(n)}}let ua=(()=>{class t{static#e=this.NULL=new hS}return t})();function fS(){return ii(Ye(),E())}function ii(t,n){return new Xe(yt(t,n))}let Xe=(()=>{class t{constructor(e){this.nativeElement=e}static#e=this.__NG_ELEMENT_ID__=fS}return t})();function pS(t){return t instanceof Xe?t.nativeElement:t}class _m{}let Un=(()=>{class t{constructor(){this.destroyNode=null}static#e=this.__NG_ELEMENT_ID__=()=>function gS(){const t=E(),e=Ot(Ye().index,t);return(vt(e)?e:t)[X]}()}return t})(),mS=(()=>{class t{static#e=this.\u0275prov=Q({token:t,providedIn:"root",factory:()=>null})}return t})();class da{constructor(n){this.full=n,this.major=n.split(".")[0],this.minor=n.split(".")[1],this.patch=n.split(".").slice(2).join(".")}}const _S=new da("16.2.12"),wu={};function Dm(t,n=null,e=null,r){const i=bm(t,n,e,r);return i.resolveInjectorInitializers(),i}function bm(t,n=null,e=null,r,i=new Set){const o=[e||ue,GI(t)];return r=r||("object"==typeof t?void 0:Le(t)),new ei(o,n||ra(),r||null,i)}let qt=(()=>{class t{static#e=this.THROW_IF_NOT_FOUND=Fi;static#t=this.NULL=new ou;static create(e,r){if(Array.isArray(e))return Dm({name:""},r,e,"");{const i=e.name??"";return Dm({name:i},e.parent,e.providers,i)}}static#n=this.\u0275prov=Q({token:t,providedIn:"any",factory:()=>j(rm)});static#r=this.__NG_ELEMENT_ID__=-1}return t})();function Mu(t){return t.ngOriginalError}class In{constructor(){this._console=console}handleError(n){const e=this._findOriginalError(n);this._console.error("ERROR",n),e&&this._console.error("ORIGINAL ERROR",e)}_findOriginalError(n){let e=n&&Mu(n);for(;e&&Mu(e);)e=Mu(e);return e||null}}function Su(t){return n=>{setTimeout(t,void 0,n)}}const B=class ES extends te{constructor(n=!1){super(),this.__isAsync=n}emit(n){super.next(n)}subscribe(n,e,r){let i=n,o=e||(()=>null),s=r;if(n&&"object"==typeof n){const l=n;i=l.next?.bind(l),o=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(o=Su(o),i&&(i=Su(i)),s&&(s=Su(s)));const a=super.subscribe({next:i,error:o,complete:s});return n instanceof $e&&n.add(a),a}};function Em(...t){}class U{constructor({enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:e=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new B(!1),this.onMicrotaskEmpty=new B(!1),this.onStable=new B(!1),this.onError=new B(!1),typeof Zone>"u")throw new O(908,!1);Zone.assertZonePatched();const i=this;i._nesting=0,i._outer=i._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(i._inner=i._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(i._inner=i._inner.fork(Zone.longStackTraceZoneSpec)),i.shouldCoalesceEventChangeDetection=!r&&e,i.shouldCoalesceRunChangeDetection=r,i.lastRequestAnimationFrameId=-1,i.nativeRequestAnimationFrame=function MS(){const t="function"==typeof Ce.requestAnimationFrame;let n=Ce[t?"requestAnimationFrame":"setTimeout"],e=Ce[t?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&n&&e){const r=n[Zone.__symbol__("OriginalDelegate")];r&&(n=r);const i=e[Zone.__symbol__("OriginalDelegate")];i&&(e=i)}return{nativeRequestAnimationFrame:n,nativeCancelAnimationFrame:e}}().nativeRequestAnimationFrame,function OS(t){const n=()=>{!function SS(t){t.isCheckStableRunning||-1!==t.lastRequestAnimationFrameId||(t.lastRequestAnimationFrameId=t.nativeRequestAnimationFrame.call(Ce,()=>{t.fakeTopEventTask||(t.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{t.lastRequestAnimationFrameId=-1,Tu(t),t.isCheckStableRunning=!0,Ou(t),t.isCheckStableRunning=!1},void 0,()=>{},()=>{})),t.fakeTopEventTask.invoke()}),Tu(t))}(t)};t._inner=t._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(e,r,i,o,s,a)=>{if(function xS(t){return!(!Array.isArray(t)||1!==t.length)&&!0===t[0].data?.__ignore_ng_zone__}(a))return e.invokeTask(i,o,s,a);try{return Mm(t),e.invokeTask(i,o,s,a)}finally{(t.shouldCoalesceEventChangeDetection&&"eventTask"===o.type||t.shouldCoalesceRunChangeDetection)&&n(),Im(t)}},onInvoke:(e,r,i,o,s,a,l)=>{try{return Mm(t),e.invoke(i,o,s,a,l)}finally{t.shouldCoalesceRunChangeDetection&&n(),Im(t)}},onHasTask:(e,r,i,o)=>{e.hasTask(i,o),r===i&&("microTask"==o.change?(t._hasPendingMicrotasks=o.microTask,Tu(t),Ou(t)):"macroTask"==o.change&&(t.hasPendingMacrotasks=o.macroTask))},onHandleError:(e,r,i,o)=>(e.handleError(i,o),t.runOutsideAngular(()=>t.onError.emit(o)),!1)})}(i)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!U.isInAngularZone())throw new O(909,!1)}static assertNotInAngularZone(){if(U.isInAngularZone())throw new O(909,!1)}run(n,e,r){return this._inner.run(n,e,r)}runTask(n,e,r,i){const o=this._inner,s=o.scheduleEventTask("NgZoneEvent: "+i,n,IS,Em,Em);try{return o.runTask(s,e,r)}finally{o.cancelTask(s)}}runGuarded(n,e,r){return this._inner.runGuarded(n,e,r)}runOutsideAngular(n){return this._outer.run(n)}}const IS={};function Ou(t){if(0==t._nesting&&!t.hasPendingMicrotasks&&!t.isStable)try{t._nesting++,t.onMicrotaskEmpty.emit(null)}finally{if(t._nesting--,!t.hasPendingMicrotasks)try{t.runOutsideAngular(()=>t.onStable.emit(null))}finally{t.isStable=!0}}}function Tu(t){t.hasPendingMicrotasks=!!(t._hasPendingMicrotasks||(t.shouldCoalesceEventChangeDetection||t.shouldCoalesceRunChangeDetection)&&-1!==t.lastRequestAnimationFrameId)}function Mm(t){t._nesting++,t.isStable&&(t.isStable=!1,t.onUnstable.emit(null))}function Im(t){t._nesting--,Ou(t)}class TS{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new B,this.onMicrotaskEmpty=new B,this.onStable=new B,this.onError=new B}run(n,e,r){return n.apply(e,r)}runGuarded(n,e,r){return n.apply(e,r)}runOutsideAngular(n){return n()}runTask(n,e,r,i){return n.apply(e,r)}}const Sm=new P("",{providedIn:"root",factory:Om});function Om(){const t=Y(U);let n=!0;return fs(new ve(i=>{n=t.isStable&&!t.hasPendingMacrotasks&&!t.hasPendingMicrotasks,t.runOutsideAngular(()=>{i.next(n),i.complete()})}),new ve(i=>{let o;t.runOutsideAngular(()=>{o=t.onStable.subscribe(()=>{U.assertNotInAngularZone(),queueMicrotask(()=>{!n&&!t.hasPendingMacrotasks&&!t.hasPendingMicrotasks&&(n=!0,i.next(!0))})})});const s=t.onUnstable.subscribe(()=>{U.assertInAngularZone(),n&&(n=!1,t.runOutsideAngular(()=>{i.next(!1)}))});return()=>{o.unsubscribe(),s.unsubscribe()}}).pipe($l()))}let xu=(()=>{class t{constructor(){this.renderDepth=0,this.handler=null}begin(){this.handler?.validateBegin(),this.renderDepth++}end(){this.renderDepth--,0===this.renderDepth&&this.handler?.execute()}ngOnDestroy(){this.handler?.destroy(),this.handler=null}static#e=this.\u0275prov=Q({token:t,providedIn:"root",factory:()=>new t})}return t})();function fo(t){for(;t;){t[ee]|=64;const n=io(t);if(cc(t)&&!n)return t;t=n}return null}const Rm=new P("",{providedIn:"root",factory:()=>!1});let fa=null;function Lm(t,n){return t[n]??jm()}function Vm(t,n){const e=jm();e.producerNode?.length&&(t[n]=fa,e.lView=t,fa=Bm())}const jS={...dp,consumerIsAlwaysLive:!0,consumerMarkedDirty:t=>{fo(t.lView)},lView:null};function Bm(){return Object.create(jS)}function jm(){return fa??=Bm(),fa}const J={};function p(t){Hm(ce(),E(),dt()+t,!1)}function Hm(t,n,e,r){if(!r)if(3==(3&n[ee])){const o=t.preOrderCheckHooks;null!==o&&As(n,o,e)}else{const o=t.preOrderHooks;null!==o&&ks(n,o,0,e)}lr(e)}function C(t,n=ae.Default){const e=E();return null===e?j(t,n):ng(Ye(),e,q(t),n)}function pa(t,n,e,r,i,o,s,a,l,c,u){const d=n.blueprint.slice();return d[Oe]=i,d[ee]=140|r,(null!==c||t&&2048&t[ee])&&(d[ee]|=2048),Ap(d),d[Me]=d[Rr]=t,d[xe]=e,d[kr]=s||t&&t[kr],d[X]=a||t&&t[X],d[Fn]=l||t&&t[Fn]||null,d[tt]=o,d[$i]=function WM(){return GM++}(),d[Dn]=u,d[ap]=c,d[Ae]=2==n.type?t[Ae]:d,d}function ai(t,n,e,r,i){let o=t.data[n];if(null===o)o=function Au(t,n,e,r,i){const o=Lp(),s=_c(),l=t.data[n]=function KS(t,n,e,r,i,o){let s=n?n.injectorIndex:-1,a=0;return Vr()&&(a|=128),{type:e,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?o:o&&o.parent,e,n,r,i);return null===t.firstChild&&(t.firstChild=l),null!==o&&(s?null==o.child&&null!==l.parent&&(o.child=l):null===o.next&&(o.next=l,l.prev=o)),l}(t,n,e,r,i),function G0(){return G.lFrame.inI18n}()&&(o.flags|=32);else if(64&o.type){o.type=e,o.value=r,o.attrs=i;const s=function Ki(){const t=G.lFrame,n=t.currentTNode;return t.isParent?n:n.parent}();o.injectorIndex=null===s?-1:s.injectorIndex}return ln(o,!0),o}function po(t,n,e,r){if(0===e)return-1;const i=n.length;for(let o=0;o<e;o++)n.push(r),t.blueprint.push(r),t.data.push(null);return i}function zm(t,n,e,r,i){const o=Lm(n,Gi),s=dt(),a=2&r;try{lr(-1),a&&n.length>ie&&Hm(t,n,ie,!1),an(a?2:0,i);const c=a?o:null,u=dc(c);try{null!==c&&(c.dirty=!1),e(r,i)}finally{hc(c,u)}}finally{a&&null===n[Gi]&&Vm(n,Gi),lr(s),an(a?3:1,i)}}function ku(t,n,e){if(lc(n)){const r=Ft(null);try{const o=n.directiveEnd;for(let s=n.directiveStart;s<o;s++){const a=t.data[s];a.contentQueries&&a.contentQueries(1,e[s],s)}}finally{Ft(r)}}}function Ru(t,n,e){Fp()&&(function nO(t,n,e,r){const i=e.directiveStart,o=e.directiveEnd;sr(e)&&function cO(t,n,e){const r=yt(n,t),i=Um(e);let s=16;e.signals?s=4096:e.onPush&&(s=64);const a=ga(t,pa(t,i,null,s,r,n,null,t[kr].rendererFactory.createRenderer(r,e),null,null,null));t[n.index]=a}(n,e,t.data[i+e.componentOffset]),t.firstCreatePass||Ps(e,n),nt(r,n);const s=e.initialInputs;for(let a=i;a<o;a++){const l=t.data[a],c=cr(n,t,a,e);nt(c,n),null!==s&&uO(0,a-i,c,l,0,s),Wt(l)&&(Ot(e.index,n)[xe]=cr(n,t,a,e))}}(t,n,e,yt(e,n)),64==(64&e.flags)&&Zm(t,n,e))}function Pu(t,n,e=yt){const r=n.localNames;if(null!==r){let i=n.index+1;for(let o=0;o<r.length;o+=2){const s=r[o+1],a=-1===s?e(n,t):t[s];t[i++]=a}}}function Um(t){const n=t.tView;return null===n||n.incompleteFirstPass?t.tView=Nu(1,null,t.template,t.decls,t.vars,t.directiveDefs,t.pipeDefs,t.viewQuery,t.schemas,t.consts,t.id):n}function Nu(t,n,e,r,i,o,s,a,l,c,u){const d=ie+r,h=d+i,f=function US(t,n){const e=[];for(let r=0;r<n;r++)e.push(r<t?null:J);return e}(d,h),g="function"==typeof c?c():c;return f[F]={type:t,blueprint:f,template:e,queries:null,viewQuery:a,declTNode:n,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof o?o():o,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:l,consts:g,incompleteFirstPass:!1,ssrId:u}}let $m=t=>null;function Gm(t,n,e,r){for(let i in t)if(t.hasOwnProperty(i)){e=null===e?{}:e;const o=t[i];null===r?Wm(e,n,i,o):r.hasOwnProperty(i)&&Wm(e,n,r[i],o)}return e}function Wm(t,n,e,r){t.hasOwnProperty(e)?t[e].push(n,r):t[e]=[n,r]}function At(t,n,e,r,i,o,s,a){const l=yt(n,e);let u,c=n.inputs;!a&&null!=c&&(u=c[r])?(ju(t,e,u,r,i),sr(n)&&function XS(t,n){const e=Ot(n,t);16&e[ee]||(e[ee]|=64)}(e,n.index)):3&n.type&&(r=function YS(t){return"class"===t?"className":"for"===t?"htmlFor":"formaction"===t?"formAction":"innerHtml"===t?"innerHTML":"readonly"===t?"readOnly":"tabindex"===t?"tabIndex":t}(r),i=null!=s?s(i,n.value||"",r):i,o.setProperty(l,r,i))}function Fu(t,n,e,r){if(Fp()){const i=null===r?null:{"":-1},o=function iO(t,n){const e=t.directiveRegistry;let r=null,i=null;if(e)for(let o=0;o<e.length;o++){const s=e[o];if(tp(n,s.selectors,!1))if(r||(r=[]),Wt(s))if(null!==s.findHostDirectiveDefs){const a=[];i=i||new Map,s.findHostDirectiveDefs(s,a,i),r.unshift(...a,s),Lu(t,n,a.length)}else r.unshift(s),Lu(t,n,0);else i=i||new Map,s.findHostDirectiveDefs?.(s,r,i),r.push(s)}return null===r?null:[r,i]}(t,e);let s,a;null===o?s=a=null:[s,a]=o,null!==s&&qm(t,n,e,s,i,a),i&&function oO(t,n,e){if(n){const r=t.localNames=[];for(let i=0;i<n.length;i+=2){const o=e[n[i+1]];if(null==o)throw new O(-301,!1);r.push(n[i],o)}}}(e,r,i)}e.mergedAttrs=Bi(e.mergedAttrs,e.attrs)}function qm(t,n,e,r,i,o){for(let c=0;c<r.length;c++)Tc(Ps(e,n),t,r[c].type);!function aO(t,n,e){t.flags|=1,t.directiveStart=n,t.directiveEnd=n+e,t.providerIndexes=n}(e,t.data.length,r.length);for(let c=0;c<r.length;c++){const u=r[c];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,l=po(t,n,r.length,null);for(let c=0;c<r.length;c++){const u=r[c];e.mergedAttrs=Bi(e.mergedAttrs,u.hostAttrs),lO(t,e,n,l,u),sO(l,u,i),null!==u.contentQueries&&(e.flags|=4),(null!==u.hostBindings||null!==u.hostAttrs||0!==u.hostVars)&&(e.flags|=64);const d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((t.preOrderHooks??=[]).push(e.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((t.preOrderCheckHooks??=[]).push(e.index),a=!0),l++}!function QS(t,n,e){const i=n.directiveEnd,o=t.data,s=n.attrs,a=[];let l=null,c=null;for(let u=n.directiveStart;u<i;u++){const d=o[u],h=e?e.get(d):null,g=h?h.outputs:null;l=Gm(d.inputs,u,l,h?h.inputs:null),c=Gm(d.outputs,u,c,g);const v=null===l||null===s||ep(n)?null:dO(l,u,s);a.push(v)}null!==l&&(l.hasOwnProperty("class")&&(n.flags|=8),l.hasOwnProperty("style")&&(n.flags|=16)),n.initialInputs=a,n.inputs=l,n.outputs=c}(t,e,o)}function Zm(t,n,e){const r=e.directiveStart,i=e.directiveEnd,o=e.index,s=function q0(){return G.lFrame.currentDirectiveIndex}();try{lr(o);for(let a=r;a<i;a++){const l=t.data[a],c=n[a];yc(a),(null!==l.hostBindings||0!==l.hostVars||null!==l.hostAttrs)&&rO(l,c)}}finally{lr(-1),yc(s)}}function rO(t,n){null!==t.hostBindings&&t.hostBindings(1,n)}function Lu(t,n,e){n.componentOffset=e,(t.components??=[]).push(n.index)}function sO(t,n,e){if(e){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)e[n.exportAs[r]]=t;Wt(n)&&(e[""]=t)}}function lO(t,n,e,r,i){t.data[r]=i;const o=i.factory||(i.factory=ar(i.type)),s=new Qi(o,Wt(i),C);t.blueprint[r]=s,e[r]=s,function eO(t,n,e,r,i){const o=i.hostBindings;if(o){let s=t.hostBindingOpCodes;null===s&&(s=t.hostBindingOpCodes=[]);const a=~n.index;(function tO(t){let n=t.length;for(;n>0;){const e=t[--n];if("number"==typeof e&&e<0)return e}return 0})(s)!=a&&s.push(a),s.push(e,r,o)}}(t,n,r,po(t,e,i.hostVars,J),i)}function dn(t,n,e,r,i,o){const s=yt(t,n);!function Vu(t,n,e,r,i,o,s){if(null==o)t.removeAttribute(n,i,e);else{const a=null==s?K(o):s(o,r||"",i);t.setAttribute(n,i,a,e)}}(n[X],s,o,t.value,e,r,i)}function uO(t,n,e,r,i,o){const s=o[n];if(null!==s)for(let a=0;a<s.length;)Km(r,e,s[a++],s[a++],s[a++])}function Km(t,n,e,r,i){const o=Ft(null);try{const s=t.inputTransforms;null!==s&&s.hasOwnProperty(r)&&(i=s[r].call(n,i)),null!==t.setInput?t.setInput(n,i,e,r):n[r]=i}finally{Ft(o)}}function dO(t,n,e){let r=null,i=0;for(;i<e.length;){const o=e[i];if(0!==o)if(5!==o){if("number"==typeof o)break;if(t.hasOwnProperty(o)){null===r&&(r=[]);const s=t[o];for(let a=0;a<s.length;a+=2)if(s[a]===n){r.push(o,s[a+1],e[i+1]);break}}i+=2}else i+=2;else i+=4}return r}function Qm(t,n,e,r){return[t,!0,!1,n,null,0,r,e,null,null,null]}function Ym(t,n){const e=t.contentQueries;if(null!==e)for(let r=0;r<e.length;r+=2){const o=e[r+1];if(-1!==o){const s=t.data[o];Dc(e[r]),s.contentQueries(2,n[o],o)}}}function ga(t,n){return t[zi]?t[sp][Gt]=n:t[zi]=n,t[sp]=n,n}function Bu(t,n,e){Dc(0);const r=Ft(null);try{n(t,e)}finally{Ft(r)}}function Xm(t){return t[Ar]||(t[Ar]=[])}function Jm(t){return t.cleanup||(t.cleanup=[])}function t_(t,n){const e=t[Fn],r=e?e.get(In,null):null;r&&r.handleError(n)}function ju(t,n,e,r,i){for(let o=0;o<e.length;){const s=e[o++],a=e[o++];Km(t.data[s],n[s],r,a,i)}}function On(t,n,e){const r=Os(n,t);!function kg(t,n,e){t.setValue(n,e)}(t[X],r,e)}function hO(t,n){const e=Ot(n,t),r=e[F];!function fO(t,n){for(let e=n.length;e<t.blueprint.length;e++)n.push(t.blueprint[e])}(r,e);const i=e[Oe];null!==i&&null===e[Dn]&&(e[Dn]=Cu(i,e[Fn])),Hu(r,e,e[xe])}function Hu(t,n,e){bc(n);try{const r=t.viewQuery;null!==r&&Bu(1,r,e);const i=t.template;null!==i&&zm(t,n,i,1,e),t.firstCreatePass&&(t.firstCreatePass=!1),t.staticContentQueries&&Ym(t,n),t.staticViewQueries&&Bu(2,t.viewQuery,e);const o=t.components;null!==o&&function pO(t,n){for(let e=0;e<n.length;e++)hO(t,n[e])}(n,o)}catch(r){throw t.firstCreatePass&&(t.incompleteFirstPass=!0,t.firstCreatePass=!1),r}finally{n[ee]&=-5,wc()}}let n_=(()=>{class t{constructor(){this.all=new Set,this.queue=new Map}create(e,r,i){const o=typeof Zone>"u"?null:Zone.current,s=function w0(t,n,e){const r=Object.create(E0);e&&(r.consumerAllowSignalWrites=!0),r.fn=t,r.schedule=n;const i=s=>{r.cleanupFn=s};return r.ref={notify:()=>gp(r),run:()=>{if(r.dirty=!1,r.hasRun&&!mp(r))return;r.hasRun=!0;const s=dc(r);try{r.cleanupFn(),r.cleanupFn=Ep,r.fn(i)}finally{hc(r,s)}},cleanup:()=>r.cleanupFn()},r.ref}(e,c=>{this.all.has(c)&&this.queue.set(c,o)},i);let a;this.all.add(s),s.notify();const l=()=>{s.cleanup(),a?.(),this.all.delete(s),this.queue.delete(s)};return a=r?.onDestroy(l),{destroy:l}}flush(){if(0!==this.queue.size)for(const[e,r]of this.queue)this.queue.delete(e),r?r.run(()=>e.run()):e.run()}get isQueueEmpty(){return 0===this.queue.size}static#e=this.\u0275prov=Q({token:t,providedIn:"root",factory:()=>new t})}return t})();function ma(t,n,e){let r=e?t.styles:null,i=e?t.classes:null,o=0;if(null!==n)for(let s=0;s<n.length;s++){const a=n[s];"number"==typeof a?o=a:1==o?i=Wl(i,a):2==o&&(r=Wl(r,a+": "+n[++s]+";"))}e?t.styles=r:t.stylesWithoutHost=r,e?t.classes=i:t.classesWithoutHost=i}function go(t,n,e,r,i=!1){for(;null!==e;){const o=n[e.index];null!==o&&r.push(De(o)),ct(o)&&r_(o,r);const s=e.type;if(8&s)go(t,n,e.child,r);else if(32&s){const a=$c(e,n);let l;for(;l=a();)r.push(l)}else if(16&s){const a=Hg(n,e);if(Array.isArray(a))r.push(...a);else{const l=io(n[Ae]);go(l[F],l,a,r,!0)}}e=i?e.projectionNext:e.next}return r}function r_(t,n){for(let e=Ze;e<t.length;e++){const r=t[e],i=r[F].firstChild;null!==i&&go(r[F],r,i,n)}t[sn]!==t[Oe]&&n.push(t[sn])}function _a(t,n,e,r=!0){const i=n[kr],o=i.rendererFactory,s=i.afterRenderEventManager;o.begin?.(),s?.begin();try{i_(t,n,t.template,e)}catch(l){throw r&&t_(n,l),l}finally{o.end?.(),i.effectManager?.flush(),s?.end()}}function i_(t,n,e,r){const i=n[ee];if(256!=(256&i)){n[kr].effectManager?.flush(),bc(n);try{Ap(n),function Bp(t){return G.lFrame.bindingIndex=t}(t.bindingStartIndex),null!==e&&zm(t,n,e,2,r);const s=3==(3&i);if(s){const c=t.preOrderCheckHooks;null!==c&&As(n,c,null)}else{const c=t.preOrderHooks;null!==c&&ks(n,c,0,null),Ec(n,0)}if(function _O(t){for(let n=Tg(t);null!==n;n=xg(n)){if(!n[lp])continue;const e=n[Nr];for(let r=0;r<e.length;r++){P0(e[r])}}}(n),o_(n,2),null!==t.contentQueries&&Ym(t,n),s){const c=t.contentCheckHooks;null!==c&&As(n,c)}else{const c=t.contentHooks;null!==c&&ks(n,c,1),Ec(n,1)}!function zS(t,n){const e=t.hostBindingOpCodes;if(null===e)return;const r=Lm(n,Wi);try{for(let i=0;i<e.length;i++){const o=e[i];if(o<0)lr(~o);else{const s=o,a=e[++i],l=e[++i];W0(a,s),r.dirty=!1;const c=dc(r);try{l(2,n[s])}finally{hc(r,c)}}}}finally{null===n[Wi]&&Vm(n,Wi),lr(-1)}}(t,n);const a=t.components;null!==a&&a_(n,a,0);const l=t.viewQuery;if(null!==l&&Bu(2,l,r),s){const c=t.viewCheckHooks;null!==c&&As(n,c)}else{const c=t.viewHooks;null!==c&&ks(n,c,2),Ec(n,2)}!0===t.firstUpdatePass&&(t.firstUpdatePass=!1),n[ee]&=-73,kp(n)}finally{wc()}}}function o_(t,n){for(let e=Tg(t);null!==e;e=xg(e))for(let r=Ze;r<e.length;r++)s_(e[r],n)}function vO(t,n,e){s_(Ot(n,t),e)}function s_(t,n){if(!function k0(t){return 128==(128&t[ee])}(t))return;const e=t[F],r=t[ee];if(80&r&&0===n||1024&r||2===n)i_(e,t,e.template,t[xe]);else if(t[Hi]>0){o_(t,1);const i=e.components;null!==i&&a_(t,i,1)}}function a_(t,n,e){for(let r=0;r<n.length;r++)vO(t,n[r],e)}class mo{get rootNodes(){const n=this._lView,e=n[F];return go(e,n,e.firstChild,[])}constructor(n,e){this._lView=n,this._cdRefInjectingView=e,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[xe]}set context(n){this._lView[xe]=n}get destroyed(){return 256==(256&this._lView[ee])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const n=this._lView[Me];if(ct(n)){const e=n[8],r=e?e.indexOf(this):-1;r>-1&&(qs(n,r),Ls(e,r))}this._attachedToViewContainer=!1}Wc(this._lView[F],this._lView)}onDestroy(n){!function Pp(t,n){if(256==(256&t[ee]))throw new O(911,!1);null===t[Ln]&&(t[Ln]=[]),t[Ln].push(n)}(this._lView,n)}markForCheck(){fo(this._cdRefInjectingView||this._lView)}detach(){this._lView[ee]&=-129}reattach(){this._lView[ee]|=128}detectChanges(){_a(this._lView[F],this._lView,this.context)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new O(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,function sI(t,n){so(t,n,n[X],2,null,null)}(this._lView[F],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new O(902,!1);this._appRef=n}}class yO extends mo{constructor(n){super(n),this._view=n}detectChanges(){const n=this._view;_a(n[F],n,n[xe],!1)}checkNoChanges(){}get context(){return null}}class l_ extends ua{constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){const e=le(n);return new _o(e,this.ngModule)}}function c_(t){const n=[];for(let e in t)t.hasOwnProperty(e)&&n.push({propName:t[e],templateName:e});return n}class DO{constructor(n,e){this.injector=n,this.parentInjector=e}get(n,e,r){r=Cs(r);const i=this.injector.get(n,wu,r);return i!==wu||e===wu?i:this.parentInjector.get(n,e,r)}}class _o extends gm{get inputs(){const n=this.componentDef,e=n.inputTransforms,r=c_(n.inputs);if(null!==e)for(const i of r)e.hasOwnProperty(i.propName)&&(i.transform=e[i.propName]);return r}get outputs(){return c_(this.componentDef.outputs)}constructor(n,e){super(),this.componentDef=n,this.ngModule=e,this.componentType=n.type,this.selector=function r0(t){return t.map(n0).join(",")}(n.selectors),this.ngContentSelectors=n.ngContentSelectors?n.ngContentSelectors:[],this.isBoundToModule=!!e}create(n,e,r,i){let o=(i=i||this.ngModule)instanceof un?i:i?.injector;o&&null!==this.componentDef.getStandaloneInjector&&(o=this.componentDef.getStandaloneInjector(o)||o);const s=o?new DO(n,o):n,a=s.get(_m,null);if(null===a)throw new O(407,!1);const d={rendererFactory:a,sanitizer:s.get(mS,null),effectManager:s.get(n_,null),afterRenderEventManager:s.get(xu,null)},h=a.createRenderer(null,this.componentDef),f=this.componentDef.selectors[0][0]||"div",g=r?function $S(t,n,e,r){const o=r.get(Rm,!1)||e===zt.ShadowDom,s=t.selectRootElement(n,o);return function GS(t){$m(t)}(s),s}(h,r,this.componentDef.encapsulation,s):Ws(h,f,function CO(t){const n=t.toLowerCase();return"svg"===n?"svg":"math"===n?"math":null}(f)),I=this.componentDef.signals?4608:this.componentDef.onPush?576:528;let w=null;null!==g&&(w=Cu(g,s,!0));const N=Nu(0,null,null,1,0,null,null,null,null,null,null),z=pa(null,N,null,I,null,null,d,h,s,null,w);let ne,Ue;bc(z);try{const _n=this.componentDef;let Ir,hf=null;_n.findHostDirectiveDefs?(Ir=[],hf=new Map,_n.findHostDirectiveDefs(_n,Ir,hf),Ir.push(_n)):Ir=[_n];const wB=function wO(t,n){const e=t[F],r=ie;return t[r]=n,ai(e,r,2,"#host",null)}(z,g),EB=function EO(t,n,e,r,i,o,s){const a=i[F];!function MO(t,n,e,r){for(const i of t)n.mergedAttrs=Bi(n.mergedAttrs,i.hostAttrs);null!==n.mergedAttrs&&(ma(n,n.mergedAttrs,!0),null!==e&&Gg(r,e,n))}(r,t,n,s);let l=null;null!==n&&(l=Cu(n,i[Fn]));const c=o.rendererFactory.createRenderer(n,e);let u=16;e.signals?u=4096:e.onPush&&(u=64);const d=pa(i,Um(e),null,u,i[t.index],t,o,c,null,null,l);return a.firstCreatePass&&Lu(a,t,r.length-1),ga(i,d),i[t.index]=d}(wB,g,_n,Ir,z,d,h);Ue=xp(N,ie),g&&function SO(t,n,e,r){if(r)sc(t,e,["ng-version",_S.full]);else{const{attrs:i,classes:o}=function o0(t){const n=[],e=[];let r=1,i=2;for(;r<t.length;){let o=t[r];if("string"==typeof o)2===i?""!==o&&n.push(o,t[++r]):8===i&&e.push(o);else{if(!Ut(i))break;i=o}r++}return{attrs:n,classes:e}}(n.selectors[0]);i&&sc(t,e,i),o&&o.length>0&&$g(t,e,o.join(" "))}}(h,_n,g,r),void 0!==e&&function OO(t,n,e){const r=t.projection=[];for(let i=0;i<n.length;i++){const o=e[i];r.push(null!=o?Array.from(o):null)}}(Ue,this.ngContentSelectors,e),ne=function IO(t,n,e,r,i,o){const s=Ye(),a=i[F],l=yt(s,i);qm(a,i,s,e,null,r);for(let u=0;u<e.length;u++)nt(cr(i,a,s.directiveStart+u,s),i);Zm(a,i,s),l&&nt(l,i);const c=cr(i,a,s.directiveStart+s.componentOffset,s);if(t[xe]=i[xe]=c,null!==o)for(const u of o)u(c,n);return ku(a,s,t),c}(EB,_n,Ir,hf,z,[TO]),Hu(N,z,null)}finally{wc()}return new bO(this.componentType,ne,ii(Ue,z),z,Ue)}}class bO extends uS{constructor(n,e,r,i,o){super(),this.location=r,this._rootLView=i,this._tNode=o,this.previousInputValues=null,this.instance=e,this.hostView=this.changeDetectorRef=new yO(i),this.componentType=n}setInput(n,e){const r=this._tNode.inputs;let i;if(null!==r&&(i=r[n])){if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),e))return;const o=this._rootLView;ju(o[F],o,i,n,e),this.previousInputValues.set(n,e),fo(Ot(this._tNode.index,o))}}get injector(){return new ht(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}}function TO(){const t=Ye();xs(E()[F],t)}function de(t){let n=function u_(t){return Object.getPrototypeOf(t.prototype).constructor}(t.type),e=!0;const r=[t];for(;n;){let i;if(Wt(t))i=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new O(903,!1);i=n.\u0275dir}if(i){if(e){r.push(i);const s=t;s.inputs=va(t.inputs),s.inputTransforms=va(t.inputTransforms),s.declaredInputs=va(t.declaredInputs),s.outputs=va(t.outputs);const a=i.hostBindings;a&&RO(t,a);const l=i.viewQuery,c=i.contentQueries;if(l&&AO(t,l),c&&kO(t,c),ps(t.inputs,i.inputs),ps(t.declaredInputs,i.declaredInputs),ps(t.outputs,i.outputs),null!==i.inputTransforms&&(null===s.inputTransforms&&(s.inputTransforms={}),ps(s.inputTransforms,i.inputTransforms)),Wt(i)&&i.data.animation){const u=t.data;u.animation=(u.animation||[]).concat(i.data.animation)}}const o=i.features;if(o)for(let s=0;s<o.length;s++){const a=o[s];a&&a.ngInherit&&a(t),a===de&&(e=!1)}}n=Object.getPrototypeOf(n)}!function xO(t){let n=0,e=null;for(let r=t.length-1;r>=0;r--){const i=t[r];i.hostVars=n+=i.hostVars,i.hostAttrs=Bi(i.hostAttrs,e=Bi(e,i.hostAttrs))}}(r)}function va(t){return t===rn?{}:t===ue?[]:t}function AO(t,n){const e=t.viewQuery;t.viewQuery=e?(r,i)=>{n(r,i),e(r,i)}:n}function kO(t,n){const e=t.contentQueries;t.contentQueries=e?(r,i,o)=>{n(r,i,o),e(r,i,o)}:n}function RO(t,n){const e=t.hostBindings;t.hostBindings=e?(r,i)=>{n(r,i),e(r,i)}:n}function ya(t){return!!zu(t)&&(Array.isArray(t)||!(t instanceof Map)&&Symbol.iterator in t)}function zu(t){return null!==t&&("function"==typeof t||"object"==typeof t)}function hn(t,n,e){return t[n]=e}function rt(t,n,e){return!Object.is(t[n],e)&&(t[n]=e,!0)}function hr(t,n,e,r){const i=rt(t,n,e);return rt(t,n+1,r)||i}function fn(t,n,e,r){const i=E();return rt(i,Br(),n)&&(ce(),dn(Ie(),i,t,n,e,r)),fn}function ci(t,n,e,r){return rt(t,Br(),e)?n+K(e)+r:J}function ui(t,n,e,r,i,o){const a=hr(t,function bn(){return G.lFrame.bindingIndex}(),e,i);return wn(2),a?n+K(e)+r+K(i)+o:J}function S(t,n,e,r,i,o,s,a){const l=E(),c=ce(),u=t+ie,d=c.firstCreatePass?function oT(t,n,e,r,i,o,s,a,l){const c=n.consts,u=ai(n,t,4,s||null,Bn(c,a));Fu(n,e,u,Bn(c,l)),xs(n,u);const d=u.tView=Nu(2,u,r,i,o,n.directiveRegistry,n.pipeRegistry,null,n.schemas,c,null);return null!==n.queries&&(n.queries.template(n,u),d.queries=n.queries.embeddedTView(u)),u}(u,c,l,n,e,r,i,o,s):c.data[u];ln(d,!1);const h=I_(c,l,d,t);Ts()&&Ks(c,l,h,d),nt(h,l),ga(l,l[u]=Qm(h,l,h,d)),Ms(d)&&Ru(c,l,d),null!=s&&Pu(l,d,a)}let I_=function S_(t,n,e,r){return jn(!0),n[X].createComment("")};function b(t,n,e){const r=E();return rt(r,Br(),n)&&At(ce(),Ie(),r,t,n,r[X],e,!1),b}function Zu(t,n,e,r,i){const s=i?"class":"style";ju(t,e,n.inputs[s],s,r)}function m(t,n,e,r){const i=E(),o=ce(),s=ie+t,a=i[X],l=o.firstCreatePass?function uT(t,n,e,r,i,o){const s=n.consts,l=ai(n,t,2,r,Bn(s,i));return Fu(n,e,l,Bn(s,o)),null!==l.attrs&&ma(l,l.attrs,!1),null!==l.mergedAttrs&&ma(l,l.mergedAttrs,!0),null!==n.queries&&n.queries.elementStart(n,l),l}(s,o,i,n,e,r):o.data[s],c=O_(o,i,l,a,n,t);i[s]=c;const u=Ms(l);return ln(l,!0),Gg(a,c,l),32!=(32&l.flags)&&Ts()&&Ks(o,i,c,l),0===function F0(){return G.lFrame.elementDepthCount}()&&nt(c,i),function L0(){G.lFrame.elementDepthCount++}(),u&&(Ru(o,i,l),ku(o,l,i)),null!==r&&Pu(i,l),m}function _(){let t=Ye();_c()?vc():(t=t.parent,ln(t,!1));const n=t;(function B0(t){return G.skipHydrationRootTNode===t})(n)&&function U0(){G.skipHydrationRootTNode=null}(),function V0(){G.lFrame.elementDepthCount--}();const e=ce();return e.firstCreatePass&&(xs(e,t),lc(t)&&e.queries.elementEnd(t)),null!=n.classesWithoutHost&&function iM(t){return 0!=(8&t.flags)}(n)&&Zu(e,n,E(),n.classesWithoutHost,!0),null!=n.stylesWithoutHost&&function oM(t){return 0!=(16&t.flags)}(n)&&Zu(e,n,E(),n.stylesWithoutHost,!1),_}function W(t,n,e,r){return m(t,n,e,r),_(),W}let O_=(t,n,e,r,i,o)=>(jn(!0),Ws(r,i,function qp(){return G.lFrame.currentNamespace}()));function Be(t,n,e){const r=E(),i=ce(),o=t+ie,s=i.firstCreatePass?function fT(t,n,e,r,i){const o=n.consts,s=Bn(o,r),a=ai(n,t,8,"ng-container",s);return null!==s&&ma(a,s,!0),Fu(n,e,a,Bn(o,i)),null!==n.queries&&n.queries.elementStart(n,a),a}(o,i,r,n,e):i.data[o];ln(s,!0);const a=x_(i,r,s,t);return r[o]=a,Ts()&&Ks(i,r,a,s),nt(a,r),Ms(s)&&(Ru(i,r,s),ku(i,s,r)),null!=e&&Pu(r,s),Be}function je(){let t=Ye();const n=ce();return _c()?vc():(t=t.parent,ln(t,!1)),n.firstCreatePass&&(xs(n,t),lc(t)&&n.queries.elementEnd(t)),je}let x_=(t,n,e,r)=>(jn(!0),Gc(n[X],""));function oe(){return E()}function Ea(t){return!!t&&"function"==typeof t.then}function A_(t){return!!t&&"function"==typeof t.subscribe}function V(t,n,e,r){const i=E(),o=ce(),s=Ye();return function R_(t,n,e,r,i,o,s){const a=Ms(r),c=t.firstCreatePass&&Jm(t),u=n[xe],d=Xm(n);let h=!0;if(3&r.type||s){const v=yt(r,n),D=s?s(v):v,I=d.length,w=s?z=>s(De(z[r.index])):r.index;let N=null;if(!s&&a&&(N=function mT(t,n,e,r){const i=t.cleanup;if(null!=i)for(let o=0;o<i.length-1;o+=2){const s=i[o];if(s===e&&i[o+1]===r){const a=n[Ar],l=i[o+2];return a.length>l?a[l]:null}"string"==typeof s&&(o+=2)}return null}(t,n,i,r.index)),null!==N)(N.__ngLastListenerFn__||N).__ngNextListenerFn__=o,N.__ngLastListenerFn__=o,h=!1;else{o=N_(r,n,u,o,!1);const z=e.listen(D,i,o);d.push(o,z),c&&c.push(i,w,I,I+1)}}else o=N_(r,n,u,o,!1);const f=r.outputs;let g;if(h&&null!==f&&(g=f[i])){const v=g.length;if(v)for(let D=0;D<v;D+=2){const ne=n[g[D]][g[D+1]].subscribe(o),Ue=d.length;d.push(o,ne),c&&c.push(i,r.index,Ue,-(Ue+1))}}}(o,i,i[X],s,t,n,r),V}function P_(t,n,e,r){try{return an(6,n,e),!1!==e(r)}catch(i){return t_(t,i),!1}finally{an(7,n,e)}}function N_(t,n,e,r,i){return function o(s){if(s===Function)return r;fo(t.componentOffset>-1?Ot(t.index,n):n);let l=P_(n,e,r,s),c=o.__ngNextListenerFn__;for(;c;)l=P_(n,e,c,s)&&l,c=c.__ngNextListenerFn__;return i&&!1===l&&s.preventDefault(),l}}function y(t=1){return function K0(t){return(G.lFrame.contextLView=function Q0(t,n){for(;t>0;)n=n[Rr],t--;return n}(t,G.lFrame.contextLView))[xe]}(t)}function _T(t,n){let e=null;const r=function XE(t){const n=t.attrs;if(null!=n){const e=n.indexOf(5);if(!(1&e))return n[e+1]}return null}(t);for(let i=0;i<n.length;i++){const o=n[i];if("*"!==o){if(null===r?tp(t,o,!0):t0(r,o))return i}else e=i}return e}function Bt(t,n,e){return Tn(t,"",n,"",e),Bt}function Tn(t,n,e,r,i){const o=E(),s=ci(o,n,e,r);return s!==J&&At(ce(),Ie(),o,t,s,o[X],i,!1),Tn}function Ma(t,n){return t<<17|n<<2}function $n(t){return t>>17&32767}function Yu(t){return 2|t}function fr(t){return(131068&t)>>2}function Xu(t,n){return-131069&t|n<<2}function Ju(t){return 1|t}function $_(t,n,e,r,i){const o=t[e+1],s=null===n;let a=r?$n(o):fr(o),l=!1;for(;0!==a&&(!1===l||s);){const u=t[a+1];wT(t[a],n)&&(l=!0,t[a+1]=r?Ju(u):Yu(u)),a=r?$n(u):fr(u)}l&&(t[e+1]=r?Yu(o):Ju(o))}function wT(t,n){return null===t||null==n||(Array.isArray(t)?t[1]:t)===n||!(!Array.isArray(t)||"string"!=typeof n)&&qr(t,n)>=0}const He={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function G_(t){return t.substring(He.key,He.keyEnd)}function W_(t,n){const e=He.textEnd;return e===n?-1:(n=He.keyEnd=function ST(t,n,e){for(;n<e&&t.charCodeAt(n)>32;)n++;return n}(t,He.key=n,e),_i(t,n,e))}function _i(t,n,e){for(;n<e&&t.charCodeAt(n)<=32;)n++;return n}function Gn(t,n){return function Zt(t,n,e,r){const i=E(),o=ce(),s=wn(2);o.firstUpdatePass&&J_(o,t,s,r),n!==J&&rt(i,s,n)&&tv(o,o.data[dt()],i,i[X],t,i[s+1]=function VT(t,n){return null==t||""===t||("string"==typeof n?t+=n:"object"==typeof t&&(t=Le(zn(t)))),t}(n,e),r,s)}(t,n,null,!0),Gn}function ed(t){!function Kt(t,n,e,r){const i=ce(),o=wn(2);i.firstUpdatePass&&J_(i,null,o,r);const s=E();if(e!==J&&rt(s,o,e)){const a=i.data[dt()];if(rv(a,r)&&!X_(i,o)){let l=r?a.classesWithoutHost:a.stylesWithoutHost;null!==l&&(e=Wl(l,e||"")),Zu(i,a,s,e,r)}else!function LT(t,n,e,r,i,o,s,a){i===J&&(i=ue);let l=0,c=0,u=0<i.length?i[0]:null,d=0<o.length?o[0]:null;for(;null!==u||null!==d;){const h=l<i.length?i[l+1]:void 0,f=c<o.length?o[c+1]:void 0;let v,g=null;u===d?(l+=2,c+=2,h!==f&&(g=d,v=f)):null===d||null!==u&&u<d?(l+=2,g=u):(c+=2,g=d,v=f),null!==g&&tv(t,n,e,r,g,v,s,a),u=l<i.length?i[l]:null,d=c<o.length?o[c]:null}}(i,a,s,s[X],s[o+1],s[o+1]=function NT(t,n,e){if(null==e||""===e)return ue;const r=[],i=zn(e);if(Array.isArray(i))for(let o=0;o<i.length;o++)t(r,i[o],!0);else if("object"==typeof i)for(const o in i)i.hasOwnProperty(o)&&t(r,o,i[o]);else"string"==typeof i&&n(r,i);return r}(t,n,e),r,o)}}(FT,gn,t,!0)}function gn(t,n){for(let e=function MT(t){return function Z_(t){He.key=0,He.keyEnd=0,He.value=0,He.valueEnd=0,He.textEnd=t.length}(t),W_(t,_i(t,0,He.textEnd))}(n);e>=0;e=W_(n,e))Tt(t,G_(n),!0)}function X_(t,n){return n>=t.expandoStartIndex}function J_(t,n,e,r){const i=t.data;if(null===i[e+1]){const o=i[dt()],s=X_(t,e);rv(o,r)&&null===n&&!s&&(n=!1),n=function AT(t,n,e,r){const i=function Cc(t){const n=G.lFrame.currentDirectiveIndex;return-1===n?null:t[n]}(t);let o=r?n.residualClasses:n.residualStyles;if(null===i)0===(r?n.classBindings:n.styleBindings)&&(e=bo(e=td(null,t,n,e,r),n.attrs,r),o=null);else{const s=n.directiveStylingLast;if(-1===s||t[s]!==i)if(e=td(i,t,n,e,r),null===o){let l=function kT(t,n,e){const r=e?n.classBindings:n.styleBindings;if(0!==fr(r))return t[$n(r)]}(t,n,r);void 0!==l&&Array.isArray(l)&&(l=td(null,t,n,l[1],r),l=bo(l,n.attrs,r),function RT(t,n,e,r){t[$n(e?n.classBindings:n.styleBindings)]=r}(t,n,r,l))}else o=function PT(t,n,e){let r;const i=n.directiveEnd;for(let o=1+n.directiveStylingLast;o<i;o++)r=bo(r,t[o].hostAttrs,e);return bo(r,n.attrs,e)}(t,n,r)}return void 0!==o&&(r?n.residualClasses=o:n.residualStyles=o),e}(i,o,n,r),function DT(t,n,e,r,i,o){let s=o?n.classBindings:n.styleBindings,a=$n(s),l=fr(s);t[r]=e;let u,c=!1;if(Array.isArray(e)?(u=e[1],(null===u||qr(e,u)>0)&&(c=!0)):u=e,i)if(0!==l){const h=$n(t[a+1]);t[r+1]=Ma(h,a),0!==h&&(t[h+1]=Xu(t[h+1],r)),t[a+1]=function yT(t,n){return 131071&t|n<<17}(t[a+1],r)}else t[r+1]=Ma(a,0),0!==a&&(t[a+1]=Xu(t[a+1],r)),a=r;else t[r+1]=Ma(l,0),0===a?a=r:t[l+1]=Xu(t[l+1],r),l=r;c&&(t[r+1]=Yu(t[r+1])),$_(t,u,r,!0),$_(t,u,r,!1),function bT(t,n,e,r,i){const o=i?t.residualClasses:t.residualStyles;null!=o&&"string"==typeof n&&qr(o,n)>=0&&(e[r+1]=Ju(e[r+1]))}(n,u,t,r,o),s=Ma(a,l),o?n.classBindings=s:n.styleBindings=s}(i,o,n,e,s,r)}}function td(t,n,e,r,i){let o=null;const s=e.directiveEnd;let a=e.directiveStylingLast;for(-1===a?a=e.directiveStart:a++;a<s&&(o=n[a],r=bo(r,o.hostAttrs,i),o!==t);)a++;return null!==t&&(e.directiveStylingLast=a),r}function bo(t,n,e){const r=e?1:2;let i=-1;if(null!==n)for(let o=0;o<n.length;o++){const s=n[o];"number"==typeof s?i=s:i===r&&(Array.isArray(t)||(t=void 0===t?[]:["",t]),Tt(t,s,!!e||n[++o]))}return void 0===t?null:t}function FT(t,n,e){const r=String(n);""!==r&&!r.includes(" ")&&Tt(t,r,e)}function tv(t,n,e,r,i,o,s,a){if(!(3&n.type))return;const l=t.data,c=l[a+1],u=function CT(t){return 1==(1&t)}(c)?nv(l,n,e,i,fr(c),s):void 0;Ia(u)||(Ia(o)||function vT(t){return 2==(2&t)}(c)&&(o=nv(l,null,e,i,a,s)),function mI(t,n,e,r,i){if(n)i?t.addClass(e,r):t.removeClass(e,r);else{let o=-1===r.indexOf("-")?void 0:Hn.DashCase;null==i?t.removeStyle(e,r,o):("string"==typeof i&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=Hn.Important),t.setStyle(e,r,i,o))}}(r,s,Os(dt(),e),i,o))}function nv(t,n,e,r,i,o){const s=null===n;let a;for(;i>0;){const l=t[i],c=Array.isArray(l),u=c?l[1]:l,d=null===u;let h=e[i+1];h===J&&(h=d?ue:void 0);let f=d?Rc(h,r):u===r?h:void 0;if(c&&!Ia(f)&&(f=Rc(l,r)),Ia(f)&&(a=f,s))return a;const g=t[i+1];i=s?$n(g):fr(g)}if(null!==n){let l=o?n.residualClasses:n.residualStyles;null!=l&&(a=Rc(l,r))}return a}function Ia(t){return void 0!==t}function rv(t,n){return 0!=(t.flags&(n?8:16))}function M(t,n=""){const e=E(),r=ce(),i=t+ie,o=r.firstCreatePass?ai(r,i,1,n,null):r.data[i],s=iv(r,e,o,n,t);e[i]=s,Ts()&&Ks(r,e,s,o),ln(o,!1)}let iv=(t,n,e,r,i)=>(jn(!0),function Gs(t,n){return t.createText(n)}(n[X],r));function L(t){return be("",t,""),L}function be(t,n,e){const r=E(),i=ci(r,t,n,e);return i!==J&&On(r,dt(),i),be}function Dt(t,n,e,r,i){const o=E(),s=ui(o,t,n,e,r,i);return s!==J&&On(o,dt(),s),Dt}const yi="en-US";let Mv=yi;function id(t,n,e,r,i){if(t=q(t),Array.isArray(t))for(let o=0;o<t.length;o++)id(t[o],n,e,r,i);else{const o=ce(),s=E(),a=Ye();let l=dr(t)?t:q(t.provide);const c=cm(t),u=1048575&a.providerIndexes,d=a.directiveStart,h=a.providerIndexes>>20;if(dr(t)||!t.multi){const f=new Qi(c,i,C),g=sd(l,n,i?u:u+h,d);-1===g?(Tc(Ps(a,s),o,l),od(o,t,n.length),n.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),e.push(f),s.push(f)):(e[g]=f,s[g]=f)}else{const f=sd(l,n,u+h,d),g=sd(l,n,u,u+h),D=g>=0&&e[g];if(i&&!D||!i&&!(f>=0&&e[f])){Tc(Ps(a,s),o,l);const I=function s1(t,n,e,r,i){const o=new Qi(t,e,C);return o.multi=[],o.index=n,o.componentProviders=0,Qv(o,i,r&&!e),o}(i?o1:r1,e.length,i,r,c);!i&&D&&(e[g].providerFactory=I),od(o,t,n.length,0),n.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),e.push(I),s.push(I)}else od(o,t,f>-1?f:g,Qv(e[i?g:f],c,!i&&r));!i&&r&&D&&e[g].componentProviders++}}}function od(t,n,e,r){const i=dr(n),o=function qI(t){return!!t.useClass}(n);if(i||o){const l=(o?q(n.useClass):n).prototype.ngOnDestroy;if(l){const c=t.destroyHooks||(t.destroyHooks=[]);if(!i&&n.multi){const u=c.indexOf(e);-1===u?c.push(e,[r,l]):c[u+1].push(r,l)}else c.push(e,l)}}}function Qv(t,n,e){return e&&t.componentProviders++,t.multi.push(n)-1}function sd(t,n,e,r){for(let i=e;i<r;i++)if(n[i]===t)return i;return-1}function r1(t,n,e,r){return ad(this.multi,[])}function o1(t,n,e,r){const i=this.multi;let o;if(this.providerFactory){const s=this.providerFactory.componentProviders,a=cr(e,e[F],this.providerFactory.index,r);o=a.slice(0,s),ad(i,o);for(let l=s;l<a.length;l++)o.push(a[l])}else o=[],ad(i,o);return o}function ad(t,n){for(let e=0;e<t.length;e++)n.push((0,t[e])());return n}function ge(t,n=[]){return e=>{e.providersResolver=(r,i)=>function n1(t,n,e){const r=ce();if(r.firstCreatePass){const i=Wt(t);id(e,r.data,r.blueprint,i,!0),id(n,r.data,r.blueprint,i,!1)}}(r,i?i(t):t,n)}}class gr{}class a1{}class ld extends gr{constructor(n,e,r){super(),this._parent=e,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new l_(this);const i=function St(t,n){const e=t[qf]||null;if(!e&&!0===n)throw new Error(`Type ${Le(t)} does not have '\u0275mod' property.`);return e}(n);this._bootstrapComponents=function Sn(t){return t instanceof Function?t():t}(i.bootstrap),this._r3Injector=bm(n,e,[{provide:gr,useValue:this},{provide:ua,useValue:this.componentFactoryResolver},...r],Le(n),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(n)}get injector(){return this._r3Injector}destroy(){const n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(e=>e()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}}class cd extends a1{constructor(n){super(),this.moduleType=n}create(n){return new ld(this.moduleType,n,[])}}function Qt(t,n,e){const r=ut()+t,i=E();return i[r]===J?hn(i,r,e?n.call(e):n()):function vo(t,n){return t[n]}(i,r)}function Ne(t,n,e,r){return oy(E(),ut(),t,n,e,r)}function iy(t,n,e,r,i){return function sy(t,n,e,r,i,o,s){const a=n+e;return hr(t,a,i,o)?hn(t,a+2,s?r.call(s,i,o):r(i,o)):Oo(t,a+2)}(E(),ut(),t,n,e,r,i)}function Oo(t,n){const e=t[n];return e===J?void 0:e}function oy(t,n,e,r,i,o){const s=n+e;return rt(t,s,i)?hn(t,s+1,o?r.call(o,i):r(i)):Oo(t,s+1)}function k(t,n){const e=ce();let r;const i=t+ie;e.firstCreatePass?(r=function O1(t,n){if(n)for(let e=n.length-1;e>=0;e--){const r=n[e];if(t===r.name)return r}}(n,e.pipeRegistry),e.data[i]=r,r.onDestroy&&(e.destroyHooks??=[]).push(i,r.onDestroy)):r=e.data[i];const o=r.factory||(r.factory=ar(r.type)),a=_t(C);try{const l=Rs(!1),c=o();return Rs(l),function lT(t,n,e,r){e>=t.data.length&&(t.data[e]=null,t.blueprint[e]=null),n[e]=r}(e,E(),i,c),c}finally{_t(a)}}function R(t,n,e){const r=t+ie,i=E(),o=function Lr(t,n){return t[n]}(i,r);return function To(t,n){return t[F].data[n].pure}(i,r)?oy(i,ut(),n,o.transform,e,o):o.transform(e)}function R1(){return this._results[Symbol.iterator]()}class dd{static#e=Symbol.iterator;get changes(){return this._changes||(this._changes=new B)}constructor(n=!1){this._emitDistinctChangesOnly=n,this.dirty=!0,this._results=[],this._changesDetected=!1,this._changes=null,this.length=0,this.first=void 0,this.last=void 0;const e=dd.prototype;e[Symbol.iterator]||(e[Symbol.iterator]=R1)}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,e){return this._results.reduce(n,e)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,e){const r=this;r.dirty=!1;const i=function Lt(t){return t.flat(Number.POSITIVE_INFINITY)}(n);(this._changesDetected=!function yM(t,n,e){if(t.length!==n.length)return!1;for(let r=0;r<t.length;r++){let i=t[r],o=n[r];if(e&&(i=e(i),o=e(o)),o!==i)return!1}return!0}(r._results,i,e))&&(r._results=i,r.length=i.length,r.last=i[this.length-1],r.first=i[0])}notifyOnChanges(){this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}setDirty(){this.dirty=!0}destroy(){this.changes.complete(),this.changes.unsubscribe()}}function N1(t,n,e,r=!0){const i=n[F];if(function lI(t,n,e,r){const i=Ze+r,o=e.length;r>0&&(e[i-1][Gt]=n),r<o-Ze?(n[Gt]=e[i],lg(e,Ze+r,n)):(e.push(n),n[Gt]=null),n[Me]=e;const s=n[Ui];null!==s&&e!==s&&function cI(t,n){const e=t[Nr];n[Ae]!==n[Me][Me][Ae]&&(t[lp]=!0),null===e?t[Nr]=[n]:e.push(n)}(s,n);const a=n[on];null!==a&&a.insertView(t),n[ee]|=128}(i,n,t,e),r){const o=Qc(e,t),s=n[X],a=Zs(s,t[sn]);null!==a&&function oI(t,n,e,r,i,o){r[Oe]=i,r[tt]=n,so(t,r,e,1,i,o)}(i,t[tt],s,n,a,o)}}let Yt=(()=>{class t{static#e=this.__NG_ELEMENT_ID__=V1}return t})();const F1=Yt,L1=class extends F1{constructor(n,e,r){super(),this._declarationLView=n,this._declarationTContainer=e,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,e){return this.createEmbeddedViewImpl(n,e)}createEmbeddedViewImpl(n,e,r){const i=function P1(t,n,e,r){const i=n.tView,a=pa(t,i,e,4096&t[ee]?4096:16,null,n,null,null,null,r?.injector??null,r?.hydrationInfo??null);a[Ui]=t[n.index];const c=t[on];return null!==c&&(a[on]=c.createEmbeddedView(i)),Hu(i,a,e),a}(this._declarationLView,this._declarationTContainer,n,{injector:e,hydrationInfo:r});return new mo(i)}};function V1(){return Aa(Ye(),E())}function Aa(t,n){return 4&t.type?new L1(n,t,ii(t,n)):null}let Xt=(()=>{class t{static#e=this.__NG_ELEMENT_ID__=$1}return t})();function $1(){return my(Ye(),E())}const G1=Xt,py=class extends G1{constructor(n,e,r){super(),this._lContainer=n,this._hostTNode=e,this._hostLView=r}get element(){return ii(this._hostTNode,this._hostLView)}get injector(){return new ht(this._hostTNode,this._hostLView)}get parentInjector(){const n=Ns(this._hostTNode,this._hostLView);if(Ic(n)){const e=Xi(n,this._hostLView),r=Yi(n);return new ht(e[F].data[r+8],e)}return new ht(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){const e=gy(this._lContainer);return null!==e&&e[n]||null}get length(){return this._lContainer.length-Ze}createEmbeddedView(n,e,r){let i,o;"number"==typeof r?i=r:null!=r&&(i=r.index,o=r.injector);const a=n.createEmbeddedViewImpl(e||{},o,null);return this.insertImpl(a,i,false),a}createComponent(n,e,r,i,o){const s=n&&!function eo(t){return"function"==typeof t}(n);let a;if(s)a=e;else{const v=e||{};a=v.index,r=v.injector,i=v.projectableNodes,o=v.environmentInjector||v.ngModuleRef}const l=s?n:new _o(le(n)),c=r||this.parentInjector;if(!o&&null==l.ngModule){const D=(s?c:this.parentInjector).get(un,null);D&&(o=D)}le(l.componentType??{});const f=l.create(c,i,null,o);return this.insertImpl(f.hostView,a,false),f}insert(n,e){return this.insertImpl(n,e,!1)}insertImpl(n,e,r){const i=n._lView;if(function R0(t){return ct(t[Me])}(i)){const l=this.indexOf(n);if(-1!==l)this.detach(l);else{const c=i[Me],u=new py(c,c[tt],c[Me]);u.detach(u.indexOf(n))}}const s=this._adjustIndex(e),a=this._lContainer;return N1(a,i,s,!r),n.attachToViewContainerRef(),lg(hd(a),s,n),n}move(n,e){return this.insert(n,e)}indexOf(n){const e=gy(this._lContainer);return null!==e?e.indexOf(n):-1}remove(n){const e=this._adjustIndex(n,-1),r=qs(this._lContainer,e);r&&(Ls(hd(this._lContainer),e),Wc(r[F],r))}detach(n){const e=this._adjustIndex(n,-1),r=qs(this._lContainer,e);return r&&null!=Ls(hd(this._lContainer),e)?new mo(r):null}_adjustIndex(n,e=0){return n??this.length+e}};function gy(t){return t[8]}function hd(t){return t[8]||(t[8]=[])}function my(t,n){let e;const r=n[t.index];return ct(r)?e=r:(e=Qm(r,n,null,t),n[t.index]=e,ga(n,e)),_y(e,n,t,r),new py(e,t,n)}let _y=function vy(t,n,e,r){if(t[sn])return;let i;i=8&e.type?De(r):function W1(t,n){const e=t[X],r=e.createComment(""),i=yt(n,t);return ur(e,Zs(e,i),r,function fI(t,n){return t.nextSibling(n)}(e,i),!1),r}(n,e),t[sn]=i};class fd{constructor(n){this.queryList=n,this.matches=null}clone(){return new fd(this.queryList)}setDirty(){this.queryList.setDirty()}}class pd{constructor(n=[]){this.queries=n}createEmbeddedView(n){const e=n.queries;if(null!==e){const r=null!==n.contentQueries?n.contentQueries[0]:e.length,i=[];for(let o=0;o<r;o++){const s=e.getByIndex(o);i.push(this.queries[s.indexInDeclarationView].clone())}return new pd(i)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let e=0;e<this.queries.length;e++)null!==Ey(n,e).matches&&this.queries[e].setDirty()}}class yy{constructor(n,e,r=null){this.predicate=n,this.flags=e,this.read=r}}class gd{constructor(n=[]){this.queries=n}elementStart(n,e){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,e)}elementEnd(n){for(let e=0;e<this.queries.length;e++)this.queries[e].elementEnd(n)}embeddedTView(n){let e=null;for(let r=0;r<this.length;r++){const i=null!==e?e.length:0,o=this.getByIndex(r).embeddedTView(n,i);o&&(o.indexInDeclarationView=r,null!==e?e.push(o):e=[o])}return null!==e?new gd(e):null}template(n,e){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,e)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}}class md{constructor(n,e=-1){this.metadata=n,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=e}elementStart(n,e){this.isApplyingToNode(e)&&this.matchTNode(n,e)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,e){this.elementStart(n,e)}embeddedTView(n,e){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,e),new md(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&1!=(1&this.metadata.flags)){const e=this._declarationNodeIndex;let r=n.parent;for(;null!==r&&8&r.type&&r.index!==e;)r=r.parent;return e===(null!==r?r.index:-1)}return this._appliesToNextNode}matchTNode(n,e){const r=this.metadata.predicate;if(Array.isArray(r))for(let i=0;i<r.length;i++){const o=r[i];this.matchTNodeWithReadOption(n,e,K1(e,o)),this.matchTNodeWithReadOption(n,e,Fs(e,n,o,!1,!1))}else r===Yt?4&e.type&&this.matchTNodeWithReadOption(n,e,-1):this.matchTNodeWithReadOption(n,e,Fs(e,n,r,!1,!1))}matchTNodeWithReadOption(n,e,r){if(null!==r){const i=this.metadata.read;if(null!==i)if(i===Xe||i===Xt||i===Yt&&4&e.type)this.addMatch(e.index,-2);else{const o=Fs(e,n,i,!1,!1);null!==o&&this.addMatch(e.index,o)}else this.addMatch(e.index,r)}}addMatch(n,e){null===this.matches?this.matches=[n,e]:this.matches.push(n,e)}}function K1(t,n){const e=t.localNames;if(null!==e)for(let r=0;r<e.length;r+=2)if(e[r]===n)return e[r+1];return null}function Y1(t,n,e,r){return-1===e?function Q1(t,n){return 11&t.type?ii(t,n):4&t.type?Aa(t,n):null}(n,t):-2===e?function X1(t,n,e){return e===Xe?ii(n,t):e===Yt?Aa(n,t):e===Xt?my(n,t):void 0}(t,n,r):cr(t,t[F],e,n)}function Cy(t,n,e,r){const i=n[on].queries[r];if(null===i.matches){const o=t.data,s=e.matches,a=[];for(let l=0;l<s.length;l+=2){const c=s[l];a.push(c<0?null:Y1(n,o[c],s[l+1],e.metadata.read))}i.matches=a}return i.matches}function _d(t,n,e,r){const i=t.queries.getByIndex(e),o=i.matches;if(null!==o){const s=Cy(t,n,i,e);for(let a=0;a<o.length;a+=2){const l=o[a];if(l>0)r.push(s[a/2]);else{const c=o[a+1],u=n[-l];for(let d=Ze;d<u.length;d++){const h=u[d];h[Ui]===h[Me]&&_d(h[F],h,c,r)}if(null!==u[Nr]){const d=u[Nr];for(let h=0;h<d.length;h++){const f=d[h];_d(f[F],f,c,r)}}}}}return r}function Ci(t){const n=E(),e=ce(),r=Hp();Dc(r+1);const i=Ey(e,r);if(t.dirty&&function A0(t){return 4==(4&t[ee])}(n)===(2==(2&i.metadata.flags))){if(null===i.matches)t.reset([]);else{const o=i.crossesNgTemplate?_d(e,n,r,[]):Cy(e,n,i,r);t.reset(o,pS),t.notifyOnChanges()}return!0}return!1}function xo(t,n,e,r){const i=ce();if(i.firstCreatePass){const o=Ye();wy(i,new yy(n,e,r),o.index),function eA(t,n){const e=t.contentQueries||(t.contentQueries=[]);n!==(e.length?e[e.length-1]:-1)&&e.push(t.queries.length-1,n)}(i,t),2==(2&e)&&(i.staticContentQueries=!0)}by(i,E(),e)}function Di(){return function J1(t,n){return t[on].queries[n].queryList}(E(),Hp())}function by(t,n,e){const r=new dd(4==(4&e));(function ZS(t,n,e,r){const i=Xm(n);i.push(e),t.firstCreatePass&&Jm(t).push(r,i.length-1)})(t,n,r,r.destroy),null===n[on]&&(n[on]=new pd),n[on].queries.push(new fd(r))}function wy(t,n,e){null===t.queries&&(t.queries=new gd),t.queries.track(new md(n,e))}function Ey(t,n){return t.queries.getByIndex(n)}const bA=new P("Application Initializer");let bd=(()=>{class t{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((e,r)=>{this.resolve=e,this.reject=r}),this.appInits=Y(bA,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const e=[];for(const i of this.appInits){const o=i();if(Ea(o))e.push(o);else if(A_(o)){const s=new Promise((a,l)=>{o.subscribe({complete:a,error:l})});e.push(s)}}const r=()=>{this.done=!0,this.resolve()};Promise.all(e).then(()=>{r()}).catch(i=>{this.reject(i)}),0===e.length&&r(),this.initialized=!0}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();const xn=new P("LocaleId",{providedIn:"root",factory:()=>Y(xn,ae.Optional|ae.SkipSelf)||function EA(){return typeof $localize<"u"&&$localize.locale||yi}()});let wd=(()=>{class t{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new Pe(!1)}add(){this.hasPendingTasks.next(!0);const e=this.taskId++;return this.pendingTasks.add(e),e}remove(e){this.pendingTasks.delete(e),0===this.pendingTasks.size&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks.next(!1)}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();const Gy=new P(""),Na=new P("");let Od,Id=(()=>{class t{constructor(e,r,i){this._ngZone=e,this.registry=r,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,Od||(function KA(t){Od=t}(i),i.addToWindow(r)),this._watchAngularEvents(),e.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{U.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let e=this._callbacks.pop();clearTimeout(e.timeoutId),e.doneCb(this._didWork)}this._didWork=!1});else{let e=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>!r.updateCb||!r.updateCb(e)||(clearTimeout(r.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(e=>({source:e.source,creationLocation:e.creationLocation,data:e.data})):[]}addCallback(e,r,i){let o=-1;r&&r>0&&(o=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==o),e(this._didWork,this.getPendingTasks())},r)),this._callbacks.push({doneCb:e,timeoutId:o,updateCb:i})}whenStable(e,r,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(e,r,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(e){this.registry.registerApplication(e,this)}unregisterApplication(e){this.registry.unregisterApplication(e)}findProviders(e,r,i){return[]}static#e=this.\u0275fac=function(r){return new(r||t)(j(U),j(Sd),j(Na))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})(),Sd=(()=>{class t{constructor(){this._applications=new Map}registerApplication(e,r){this._applications.set(e,r)}unregisterApplication(e){this._applications.delete(e)}unregisterAllApplications(){this._applications.clear()}getTestability(e){return this._applications.get(e)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(e,r=!0){return Od?.findTestabilityInTree(this,e,r)??null}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"platform"})}return t})(),Wn=null;const Wy=new P("AllowMultipleToken"),Td=new P("PlatformDestroyListeners"),qy=new P("appBootstrapListener");function Qy(t,n,e=[]){const r=`Platform: ${n}`,i=new P(r);return(o=[])=>{let s=xd();if(!s||s.injector.get(Wy,!1)){const a=[...e,...o,{provide:i,useValue:!0}];t?t(a):function XA(t){if(Wn&&!Wn.get(Wy,!1))throw new O(400,!1);(function Zy(){!function v0(t){Cp=t}(()=>{throw new O(600,!1)})})(),Wn=t;const n=t.get(Xy);(function Ky(t){t.get(um,null)?.forEach(e=>e())})(t)}(function Yy(t=[],n){return qt.create({name:n,providers:[{provide:cu,useValue:"platform"},{provide:Td,useValue:new Set([()=>Wn=null])},...t]})}(a,r))}return function ek(t){const n=xd();if(!n)throw new O(401,!1);return n}()}}function xd(){return Wn?.get(Xy)??null}let Xy=(()=>{class t{constructor(e){this._injector=e,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(e,r){const i=function tk(t="zone.js",n){return"noop"===t?new TS:"zone.js"===t?new U(n):t}(r?.ngZone,function Jy(t){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:t?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:t?.runCoalescing??!1}}({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return i.run(()=>{const o=function c1(t,n,e){return new ld(t,n,e)}(e.moduleType,this.injector,function iC(t){return[{provide:U,useFactory:t},{provide:ea,multi:!0,useFactory:()=>{const n=Y(rk,{optional:!0});return()=>n.initialize()}},{provide:rC,useFactory:nk},{provide:Sm,useFactory:Om}]}(()=>i)),s=o.injector.get(In,null);return i.runOutsideAngular(()=>{const a=i.onError.subscribe({next:l=>{s.handleError(l)}});o.onDestroy(()=>{Fa(this._modules,o),a.unsubscribe()})}),function eC(t,n,e){try{const r=e();return Ea(r)?r.catch(i=>{throw n.runOutsideAngular(()=>t.handleError(i)),i}):r}catch(r){throw n.runOutsideAngular(()=>t.handleError(r)),r}}(s,i,()=>{const a=o.injector.get(bd);return a.runInitializers(),a.donePromise.then(()=>(function Iv(t){Nt(t,"Expected localeId to be defined"),"string"==typeof t&&(Mv=t.toLowerCase().replace(/_/g,"-"))}(o.injector.get(xn,yi)||yi),this._moduleDoBootstrap(o),o))})})}bootstrapModule(e,r=[]){const i=tC({},r);return function QA(t,n,e){const r=new cd(e);return Promise.resolve(r)}(0,0,e).then(o=>this.bootstrapModuleFactory(o,i))}_moduleDoBootstrap(e){const r=e.injector.get(Ro);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(i=>r.bootstrap(i));else{if(!e.instance.ngDoBootstrap)throw new O(-403,!1);e.instance.ngDoBootstrap(r)}this._modules.push(e)}onDestroy(e){this._destroyListeners.push(e)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new O(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());const e=this._injector.get(Td,null);e&&(e.forEach(r=>r()),e.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static#e=this.\u0275fac=function(r){return new(r||t)(j(qt))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"platform"})}return t})();function tC(t,n){return Array.isArray(n)?n.reduce(tC,t):{...t,...n}}let Ro=(()=>{class t{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=Y(rC),this.zoneIsStable=Y(Sm),this.componentTypes=[],this.components=[],this.isStable=Y(wd).hasPendingTasks.pipe(Tr(e=>e?Pt(!1):this.zoneIsStable),function TE(t,n=Or){return t=t??xE,Je((e,r)=>{let i,o=!0;e.subscribe(Ge(r,s=>{const a=n(s);(o||!t(i,a))&&(o=!1,i=a,r.next(s))}))})}(),$l()),this._injector=Y(un)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(e,r){const i=e instanceof gm;if(!this._injector.get(bd).done)throw!i&&function ji(t){const n=le(t)||qe(t)||lt(t);return null!==n&&n.standalone}(e),new O(405,!1);let s;s=i?e:this._injector.get(ua).resolveComponentFactory(e),this.componentTypes.push(s.componentType);const a=function YA(t){return t.isBoundToModule}(s)?void 0:this._injector.get(gr),c=s.create(qt.NULL,[],r||s.selector,a),u=c.location.nativeElement,d=c.injector.get(Gy,null);return d?.registerApplication(u),c.onDestroy(()=>{this.detachView(c.hostView),Fa(this.components,c),d?.unregisterApplication(u)}),this._loadComponent(c),c}tick(){if(this._runningTick)throw new O(101,!1);try{this._runningTick=!0;for(let e of this._views)e.detectChanges()}catch(e){this.internalErrorHandler(e)}finally{this._runningTick=!1}}attachView(e){const r=e;this._views.push(r),r.attachToAppRef(this)}detachView(e){const r=e;Fa(this._views,r),r.detachFromAppRef()}_loadComponent(e){this.attachView(e.hostView),this.tick(),this.components.push(e);const r=this._injector.get(qy,[]);r.push(...this._bootstrapListeners),r.forEach(i=>i(e))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(e=>e()),this._views.slice().forEach(e=>e.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(e){return this._destroyListeners.push(e),()=>Fa(this._destroyListeners,e)}destroy(){if(this._destroyed)throw new O(406,!1);const e=this._injector;e.destroy&&!e.destroyed&&e.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Fa(t,n){const e=t.indexOf(n);e>-1&&t.splice(e,1)}const rC=new P("",{providedIn:"root",factory:()=>Y(In).handleError.bind(void 0)});function nk(){const t=Y(U),n=Y(In);return e=>t.runOutsideAngular(()=>n.handleError(e))}let rk=(()=>{class t{constructor(){this.zone=Y(U),this.applicationRef=Y(Ro)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();let Ei=(()=>{class t{static#e=this.__NG_ELEMENT_ID__=ok}return t})();function ok(t){return function sk(t,n,e){if(sr(t)&&!e){const r=Ot(t.index,n);return new mo(r,r)}return 47&t.type?new mo(n[Ae],n):null}(Ye(),E(),16==(16&t))}class lC{constructor(){}supports(n){return ya(n)}create(n){return new hk(n)}}const dk=(t,n)=>n;class hk{constructor(n){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=n||dk}forEachItem(n){let e;for(e=this._itHead;null!==e;e=e._next)n(e)}forEachOperation(n){let e=this._itHead,r=this._removalsHead,i=0,o=null;for(;e||r;){const s=!r||e&&e.currentIndex<uC(r,i,o)?e:r,a=uC(s,i,o),l=s.currentIndex;if(s===r)i--,r=r._nextRemoved;else if(e=e._next,null==s.previousIndex)i++;else{o||(o=[]);const c=a-i,u=l-i;if(c!=u){for(let h=0;h<c;h++){const f=h<o.length?o[h]:o[h]=0,g=f+h;u<=g&&g<c&&(o[h]=f+1)}o[s.previousIndex]=u-c}}a!==l&&n(s,a,l)}}forEachPreviousItem(n){let e;for(e=this._previousItHead;null!==e;e=e._nextPrevious)n(e)}forEachAddedItem(n){let e;for(e=this._additionsHead;null!==e;e=e._nextAdded)n(e)}forEachMovedItem(n){let e;for(e=this._movesHead;null!==e;e=e._nextMoved)n(e)}forEachRemovedItem(n){let e;for(e=this._removalsHead;null!==e;e=e._nextRemoved)n(e)}forEachIdentityChange(n){let e;for(e=this._identityChangesHead;null!==e;e=e._nextIdentityChange)n(e)}diff(n){if(null==n&&(n=[]),!ya(n))throw new O(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let i,o,s,e=this._itHead,r=!1;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)o=n[a],s=this._trackByFn(a,o),null!==e&&Object.is(e.trackById,s)?(r&&(e=this._verifyReinsertion(e,o,s,a)),Object.is(e.item,o)||this._addIdentityChange(e,o)):(e=this._mismatch(e,o,s,a),r=!0),e=e._next}else i=0,function HO(t,n){if(Array.isArray(t))for(let e=0;e<t.length;e++)n(t[e]);else{const e=t[Symbol.iterator]();let r;for(;!(r=e.next()).done;)n(r.value)}}(n,a=>{s=this._trackByFn(i,a),null!==e&&Object.is(e.trackById,s)?(r&&(e=this._verifyReinsertion(e,a,s,i)),Object.is(e.item,a)||this._addIdentityChange(e,a)):(e=this._mismatch(e,a,s,i),r=!0),e=e._next,i++}),this.length=i;return this._truncate(e),this.collection=n,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;null!==n;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;null!==n;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;null!==n;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,e,r,i){let o;return null===n?o=this._itTail:(o=n._prev,this._remove(n)),null!==(n=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(n.item,e)||this._addIdentityChange(n,e),this._reinsertAfter(n,o,i)):null!==(n=null===this._linkedRecords?null:this._linkedRecords.get(r,i))?(Object.is(n.item,e)||this._addIdentityChange(n,e),this._moveAfter(n,o,i)):n=this._addAfter(new fk(e,r),o,i),n}_verifyReinsertion(n,e,r,i){let o=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==o?n=this._reinsertAfter(o,n._prev,i):n.currentIndex!=i&&(n.currentIndex=i,this._addToMoves(n,i)),n}_truncate(n){for(;null!==n;){const e=n._next;this._addToRemovals(this._unlink(n)),n=e}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,e,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(n);const i=n._prevRemoved,o=n._nextRemoved;return null===i?this._removalsHead=o:i._nextRemoved=o,null===o?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(n,e,r),this._addToMoves(n,r),n}_moveAfter(n,e,r){return this._unlink(n),this._insertAfter(n,e,r),this._addToMoves(n,r),n}_addAfter(n,e,r){return this._insertAfter(n,e,r),this._additionsTail=null===this._additionsTail?this._additionsHead=n:this._additionsTail._nextAdded=n,n}_insertAfter(n,e,r){const i=null===e?this._itHead:e._next;return n._next=i,n._prev=e,null===i?this._itTail=n:i._prev=n,null===e?this._itHead=n:e._next=n,null===this._linkedRecords&&(this._linkedRecords=new cC),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){null!==this._linkedRecords&&this._linkedRecords.remove(n);const e=n._prev,r=n._next;return null===e?this._itHead=r:e._next=r,null===r?this._itTail=e:r._prev=e,n}_addToMoves(n,e){return n.previousIndex===e||(this._movesTail=null===this._movesTail?this._movesHead=n:this._movesTail._nextMoved=n),n}_addToRemovals(n){return null===this._unlinkedRecords&&(this._unlinkedRecords=new cC),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,e){return n.item=e,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=n:this._identityChangesTail._nextIdentityChange=n,n}}class fk{constructor(n,e){this.item=n,this.trackById=e,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class pk{constructor(){this._head=null,this._tail=null}add(n){null===this._head?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,e){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===e||e<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){const e=n._prevDup,r=n._nextDup;return null===e?this._head=r:e._nextDup=r,null===r?this._tail=e:r._prevDup=e,null===this._head}}class cC{constructor(){this.map=new Map}put(n){const e=n.trackById;let r=this.map.get(e);r||(r=new pk,this.map.set(e,r)),r.add(n)}get(n,e){const i=this.map.get(n);return i?i.get(n,e):null}remove(n){const e=n.trackById;return this.map.get(e).remove(n)&&this.map.delete(e),n}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function uC(t,n,e){const r=t.previousIndex;if(null===r)return r;let i=0;return e&&r<e.length&&(i=e[r]),r+n+i}class dC{constructor(){}supports(n){return n instanceof Map||zu(n)}create(){return new gk}}class gk{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(n){let e;for(e=this._mapHead;null!==e;e=e._next)n(e)}forEachPreviousItem(n){let e;for(e=this._previousMapHead;null!==e;e=e._nextPrevious)n(e)}forEachChangedItem(n){let e;for(e=this._changesHead;null!==e;e=e._nextChanged)n(e)}forEachAddedItem(n){let e;for(e=this._additionsHead;null!==e;e=e._nextAdded)n(e)}forEachRemovedItem(n){let e;for(e=this._removalsHead;null!==e;e=e._nextRemoved)n(e)}diff(n){if(n){if(!(n instanceof Map||zu(n)))throw new O(900,!1)}else n=new Map;return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let e=this._mapHead;if(this._appendAfter=null,this._forEach(n,(r,i)=>{if(e&&e.key===i)this._maybeAddToChanges(e,r),this._appendAfter=e,e=e._next;else{const o=this._getOrCreateRecordForKey(i,r);e=this._insertBeforeOrAppend(e,o)}}),e){e._prev&&(e._prev._next=null),this._removalsHead=e;for(let r=e;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(n,e){if(n){const r=n._prev;return e._next=n,e._prev=r,n._prev=e,r&&(r._next=e),n===this._mapHead&&(this._mapHead=e),this._appendAfter=n,n}return this._appendAfter?(this._appendAfter._next=e,e._prev=this._appendAfter):this._mapHead=e,this._appendAfter=e,null}_getOrCreateRecordForKey(n,e){if(this._records.has(n)){const i=this._records.get(n);this._maybeAddToChanges(i,e);const o=i._prev,s=i._next;return o&&(o._next=s),s&&(s._prev=o),i._next=null,i._prev=null,i}const r=new mk(n);return this._records.set(n,r),r.currentValue=e,this._addToAdditions(r),r}_reset(){if(this.isDirty){let n;for(this._previousMapHead=this._mapHead,n=this._previousMapHead;null!==n;n=n._next)n._nextPrevious=n._next;for(n=this._changesHead;null!==n;n=n._nextChanged)n.previousValue=n.currentValue;for(n=this._additionsHead;null!=n;n=n._nextAdded)n.previousValue=n.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(n,e){Object.is(e,n.currentValue)||(n.previousValue=n.currentValue,n.currentValue=e,this._addToChanges(n))}_addToAdditions(n){null===this._additionsHead?this._additionsHead=this._additionsTail=n:(this._additionsTail._nextAdded=n,this._additionsTail=n)}_addToChanges(n){null===this._changesHead?this._changesHead=this._changesTail=n:(this._changesTail._nextChanged=n,this._changesTail=n)}_forEach(n,e){n instanceof Map?n.forEach(e):Object.keys(n).forEach(r=>e(n[r],r))}}class mk{constructor(n){this.key=n,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function hC(){return new Ba([new lC])}let Ba=(()=>{class t{static#e=this.\u0275prov=Q({token:t,providedIn:"root",factory:hC});constructor(e){this.factories=e}static create(e,r){if(null!=r){const i=r.factories.slice();e=e.concat(i)}return new t(e)}static extend(e){return{provide:t,useFactory:r=>t.create(e,r||hC()),deps:[[t,new Fc,new Nc]]}}find(e){const r=this.factories.find(i=>i.supports(e));if(null!=r)return r;throw new O(901,!1)}}return t})();function fC(){return new Po([new dC])}let Po=(()=>{class t{static#e=this.\u0275prov=Q({token:t,providedIn:"root",factory:fC});constructor(e){this.factories=e}static create(e,r){if(r){const i=r.factories.slice();e=e.concat(i)}return new t(e)}static extend(e){return{provide:t,useFactory:r=>t.create(e,r||fC()),deps:[[t,new Fc,new Nc]]}}find(e){const r=this.factories.find(i=>i.supports(e));if(r)return r;throw new O(901,!1)}}return t})();const yk=Qy(null,"core",[]);let Ck=(()=>{class t{constructor(e){}static#e=this.\u0275fac=function(r){return new(r||t)(j(Ro))};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({})}return t})();let Ld=null;function No(){return Ld}class Rk{}const Et=new P("DocumentToken");function xC(t,n){n=encodeURIComponent(n);for(const e of t.split(";")){const r=e.indexOf("="),[i,o]=-1==r?[e,""]:[e.slice(0,r),e.slice(r+1)];if(i.trim()===n)return decodeURIComponent(o)}return null}class DR{constructor(n,e,r,i){this.$implicit=n,this.ngForOf=e,this.index=r,this.count=i}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let Ii=(()=>{class t{set ngForOf(e){this._ngForOf=e,this._ngForOfDirty=!0}set ngForTrackBy(e){this._trackByFn=e}get ngForTrackBy(){return this._trackByFn}constructor(e,r,i){this._viewContainer=e,this._template=r,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(e){e&&(this._template=e)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const e=this._ngForOf;!this._differ&&e&&(this._differ=this._differs.find(e).create(this.ngForTrackBy))}if(this._differ){const e=this._differ.diff(this._ngForOf);e&&this._applyChanges(e)}}_applyChanges(e){const r=this._viewContainer;e.forEachOperation((i,o,s)=>{if(null==i.previousIndex)r.createEmbeddedView(this._template,new DR(i.item,this._ngForOf,-1,-1),null===s?void 0:s);else if(null==s)r.remove(null===o?void 0:o);else if(null!==o){const a=r.get(o);r.move(a,s),RC(a,i)}});for(let i=0,o=r.length;i<o;i++){const a=r.get(i).context;a.index=i,a.count=o,a.ngForOf=this._ngForOf}e.forEachIdentityChange(i=>{RC(r.get(i.currentIndex),i)})}static ngTemplateContextGuard(e,r){return!0}static#e=this.\u0275fac=function(r){return new(r||t)(C(Xt),C(Yt),C(Ba))};static#t=this.\u0275dir=H({type:t,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}return t})();function RC(t,n){t.context.$implicit=n.item}let Rn=(()=>{class t{constructor(e,r){this._viewContainer=e,this._context=new bR,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(e){this._context.$implicit=this._context.ngIf=e,this._updateView()}set ngIfThen(e){PC("ngIfThen",e),this._thenTemplateRef=e,this._thenViewRef=null,this._updateView()}set ngIfElse(e){PC("ngIfElse",e),this._elseTemplateRef=e,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(e,r){return!0}static#e=this.\u0275fac=function(r){return new(r||t)(C(Xt),C(Yt))};static#t=this.\u0275dir=H({type:t,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}return t})();class bR{constructor(){this.$implicit=null,this.ngIf=null}}function PC(t,n){if(n&&!n.createEmbeddedView)throw new Error(`${t} must be a TemplateRef, but received '${Le(n)}'.`)}let _r=(()=>{class t{constructor(e,r,i){this._ngEl=e,this._differs=r,this._renderer=i,this._ngStyle=null,this._differ=null}set ngStyle(e){this._ngStyle=e,!this._differ&&e&&(this._differ=this._differs.find(e).create())}ngDoCheck(){if(this._differ){const e=this._differ.diff(this._ngStyle);e&&this._applyChanges(e)}}_setStyle(e,r){const[i,o]=e.split("."),s=-1===i.indexOf("-")?void 0:Hn.DashCase;null!=r?this._renderer.setStyle(this._ngEl.nativeElement,i,o?`${r}${o}`:r,s):this._renderer.removeStyle(this._ngEl.nativeElement,i,s)}_applyChanges(e){e.forEachRemovedItem(r=>this._setStyle(r.key,null)),e.forEachAddedItem(r=>this._setStyle(r.key,r.currentValue)),e.forEachChangedItem(r=>this._setStyle(r.key,r.currentValue))}static#e=this.\u0275fac=function(r){return new(r||t)(C(Xe),C(Po),C(Un))};static#t=this.\u0275dir=H({type:t,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"},standalone:!0})}return t})();class IR{createSubscription(n,e){return wp(()=>n.subscribe({next:e,error:r=>{throw r}}))}dispose(n){wp(()=>n.unsubscribe())}}class SR{createSubscription(n,e){return n.then(e,r=>{throw r})}dispose(n){}}const OR=new SR,TR=new IR;let FC=(()=>{class t{constructor(e){this._latestValue=null,this._subscription=null,this._obj=null,this._strategy=null,this._ref=e}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(e){return this._obj?e!==this._obj?(this._dispose(),this.transform(e)):this._latestValue:(e&&this._subscribe(e),this._latestValue)}_subscribe(e){this._obj=e,this._strategy=this._selectStrategy(e),this._subscription=this._strategy.createSubscription(e,r=>this._updateLatestValue(e,r))}_selectStrategy(e){if(Ea(e))return OR;if(A_(e))return TR;throw function en(t,n){return new O(2100,!1)}()}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(e,r){e===this._obj&&(this._latestValue=r,this._ref.markForCheck())}static#e=this.\u0275fac=function(r){return new(r||t)(C(Ei,16))};static#t=this.\u0275pipe=at({name:"async",type:t,pure:!1,standalone:!0})}return t})(),qR=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({})}return t})();const VC="browser";function BC(t){return t===VC}function jC(t){return"server"===t}class HC{}class CP extends Rk{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class eh extends CP{static makeCurrent(){!function kk(t){Ld||(Ld=t)}(new eh)}onAndCancel(n,e,r){return n.addEventListener(e,r),()=>{n.removeEventListener(e,r)}}dispatchEvent(n,e){n.dispatchEvent(e)}remove(n){n.parentNode&&n.parentNode.removeChild(n)}createElement(n,e){return(e=e||this.getDefaultDocument()).createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,e){return"window"===e?window:"document"===e?n:"body"===e?n.body:null}getBaseHref(n){const e=function DP(){return Bo=Bo||document.querySelector("base"),Bo?Bo.getAttribute("href"):null}();return null==e?null:function bP(t){Ja=Ja||document.createElement("a"),Ja.setAttribute("href",t);const n=Ja.pathname;return"/"===n.charAt(0)?n:`/${n}`}(e)}resetBaseElement(){Bo=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return xC(document.cookie,n)}}let Ja,Bo=null,EP=(()=>{class t{build(){return new XMLHttpRequest}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();const th=new P("EventManagerPlugins");let WC=(()=>{class t{constructor(e,r){this._zone=r,this._eventNameToPlugin=new Map,e.forEach(i=>{i.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,r,i){return this._findPluginFor(r).addEventListener(e,r,i)}getZone(){return this._zone}_findPluginFor(e){let r=this._eventNameToPlugin.get(e);if(r)return r;if(r=this._plugins.find(o=>o.supports(e)),!r)throw new O(5101,!1);return this._eventNameToPlugin.set(e,r),r}static#e=this.\u0275fac=function(r){return new(r||t)(j(th),j(U))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();class qC{constructor(n){this._doc=n}}const nh="ng-app-id";let ZC=(()=>{class t{constructor(e,r,i,o={}){this.doc=e,this.appId=r,this.nonce=i,this.platformId=o,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=jC(o),this.resetHostNodes()}addStyles(e){for(const r of e)1===this.changeUsageCount(r,1)&&this.onStyleAdded(r)}removeStyles(e){for(const r of e)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){const e=this.styleNodesInDOM;e&&(e.forEach(r=>r.remove()),e.clear());for(const r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(e){this.hostNodes.add(e);for(const r of this.getAllStyles())this.addStyleToHost(e,r)}removeHost(e){this.hostNodes.delete(e)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(e){for(const r of this.hostNodes)this.addStyleToHost(r,e)}onStyleRemoved(e){const r=this.styleRef;r.get(e)?.elements?.forEach(i=>i.remove()),r.delete(e)}collectServerRenderedStyles(){const e=this.doc.head?.querySelectorAll(`style[${nh}="${this.appId}"]`);if(e?.length){const r=new Map;return e.forEach(i=>{null!=i.textContent&&r.set(i.textContent,i)}),r}return null}changeUsageCount(e,r){const i=this.styleRef;if(i.has(e)){const o=i.get(e);return o.usage+=r,o.usage}return i.set(e,{usage:r,elements:[]}),r}getStyleElement(e,r){const i=this.styleNodesInDOM,o=i?.get(r);if(o?.parentNode===e)return i.delete(r),o.removeAttribute(nh),o;{const s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(nh,this.appId),s}}addStyleToHost(e,r){const i=this.getStyleElement(e,r);e.appendChild(i);const o=this.styleRef,s=o.get(r)?.elements;s?s.push(i):o.set(r,{elements:[i],usage:1})}resetHostNodes(){const e=this.hostNodes;e.clear(),e.add(this.doc.head)}static#e=this.\u0275fac=function(r){return new(r||t)(j(Et),j(ia),j(dm,8),j(Mn))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();const rh={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},ih=/%COMP%/g,OP=new P("RemoveStylesOnCompDestroy",{providedIn:"root",factory:()=>!1});function QC(t,n){return n.map(e=>e.replace(ih,t))}let YC=(()=>{class t{constructor(e,r,i,o,s,a,l,c=null){this.eventManager=e,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.rendererByCompId=new Map,this.platformIsServer=jC(a),this.defaultRenderer=new oh(e,s,l,this.platformIsServer)}createRenderer(e,r){if(!e||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===zt.ShadowDom&&(r={...r,encapsulation:zt.Emulated});const i=this.getOrCreateRenderer(e,r);return i instanceof JC?i.applyToHost(e):i instanceof sh&&i.applyStyles(),i}getOrCreateRenderer(e,r){const i=this.rendererByCompId;let o=i.get(r.id);if(!o){const s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case zt.Emulated:o=new JC(l,c,r,this.appId,u,s,a,d);break;case zt.ShadowDom:return new kP(l,c,e,r,s,a,this.nonce,d);default:o=new sh(l,c,r,u,s,a,d)}i.set(r.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}static#e=this.\u0275fac=function(r){return new(r||t)(j(WC),j(ZC),j(ia),j(OP),j(Et),j(Mn),j(U),j(dm))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();class oh{constructor(n,e,r,i){this.eventManager=n,this.doc=e,this.ngZone=r,this.platformIsServer=i,this.data=Object.create(null),this.destroyNode=null}destroy(){}createElement(n,e){return e?this.doc.createElementNS(rh[e]||e,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,e){(XC(n)?n.content:n).appendChild(e)}insertBefore(n,e,r){n&&(XC(n)?n.content:n).insertBefore(e,r)}removeChild(n,e){n&&n.removeChild(e)}selectRootElement(n,e){let r="string"==typeof n?this.doc.querySelector(n):n;if(!r)throw new O(-5104,!1);return e||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,e,r,i){if(i){e=i+":"+e;const o=rh[i];o?n.setAttributeNS(o,e,r):n.setAttribute(e,r)}else n.setAttribute(e,r)}removeAttribute(n,e,r){if(r){const i=rh[r];i?n.removeAttributeNS(i,e):n.removeAttribute(`${r}:${e}`)}else n.removeAttribute(e)}addClass(n,e){n.classList.add(e)}removeClass(n,e){n.classList.remove(e)}setStyle(n,e,r,i){i&(Hn.DashCase|Hn.Important)?n.style.setProperty(e,r,i&Hn.Important?"important":""):n.style[e]=r}removeStyle(n,e,r){r&Hn.DashCase?n.style.removeProperty(e):n.style[e]=""}setProperty(n,e,r){n[e]=r}setValue(n,e){n.nodeValue=e}listen(n,e,r){if("string"==typeof n&&!(n=No().getGlobalEventTarget(this.doc,n)))throw new Error(`Unsupported event target ${n} for event ${e}`);return this.eventManager.addEventListener(n,e,this.decoratePreventDefault(r))}decoratePreventDefault(n){return e=>{if("__ngUnwrap__"===e)return n;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>n(e)):n(e))&&e.preventDefault()}}}function XC(t){return"TEMPLATE"===t.tagName&&void 0!==t.content}class kP extends oh{constructor(n,e,r,i,o,s,a,l){super(n,o,s,l),this.sharedStylesHost=e,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const c=QC(i.id,i.styles);for(const u of c){const d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=u,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,e){return super.appendChild(this.nodeOrShadowRoot(n),e)}insertBefore(n,e,r){return super.insertBefore(this.nodeOrShadowRoot(n),e,r)}removeChild(n,e){return super.removeChild(this.nodeOrShadowRoot(n),e)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class sh extends oh{constructor(n,e,r,i,o,s,a,l){super(n,o,s,a),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=i,this.styles=l?QC(l,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class JC extends sh{constructor(n,e,r,i,o,s,a,l){const c=i+"-"+r.id;super(n,e,r,o,s,a,l,c),this.contentAttr=function TP(t){return"_ngcontent-%COMP%".replace(ih,t)}(c),this.hostAttr=function xP(t){return"_nghost-%COMP%".replace(ih,t)}(c)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,e){const r=super.createElement(n,e);return super.setAttribute(r,this.contentAttr,""),r}}let RP=(()=>{class t extends qC{constructor(e){super(e)}supports(e){return!0}addEventListener(e,r,i){return e.addEventListener(r,i,!1),()=>this.removeEventListener(e,r,i)}removeEventListener(e,r,i){return e.removeEventListener(r,i)}static#e=this.\u0275fac=function(r){return new(r||t)(j(Et))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();const eD=["alt","control","meta","shift"],PP={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},NP={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey};let FP=(()=>{class t extends qC{constructor(e){super(e)}supports(e){return null!=t.parseEventName(e)}addEventListener(e,r,i){const o=t.parseEventName(r),s=t.eventCallback(o.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>No().onAndCancel(e,o.domEventName,s))}static parseEventName(e){const r=e.toLowerCase().split("."),i=r.shift();if(0===r.length||"keydown"!==i&&"keyup"!==i)return null;const o=t._normalizeKey(r.pop());let s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),eD.forEach(c=>{const u=r.indexOf(c);u>-1&&(r.splice(u,1),s+=c+".")}),s+=o,0!=r.length||0===o.length)return null;const l={};return l.domEventName=i,l.fullKey=s,l}static matchEventFullKeyCode(e,r){let i=PP[e.key]||e.key,o="";return r.indexOf("code.")>-1&&(i=e.code,o="code."),!(null==i||!i)&&(i=i.toLowerCase()," "===i?i="space":"."===i&&(i="dot"),eD.forEach(s=>{s!==i&&(0,NP[s])(e)&&(o+=s+".")}),o+=i,o===r)}static eventCallback(e,r,i){return o=>{t.matchEventFullKeyCode(o,e)&&i.runGuarded(()=>r(o))}}static _normalizeKey(e){return"esc"===e?"escape":e}static#e=this.\u0275fac=function(r){return new(r||t)(j(Et))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();const jP=Qy(yk,"browser",[{provide:Mn,useValue:VC},{provide:um,useValue:function LP(){eh.makeCurrent()},multi:!0},{provide:Et,useFactory:function BP(){return function DI(t){Jc=t}(document),document},deps:[]}]),HP=new P(""),rD=[{provide:Na,useClass:class wP{addToWindow(n){Ce.getAngularTestability=(r,i=!0)=>{const o=n.findTestabilityInTree(r,i);if(null==o)throw new O(5103,!1);return o},Ce.getAllAngularTestabilities=()=>n.getAllTestabilities(),Ce.getAllAngularRootElements=()=>n.getAllRootElements(),Ce.frameworkStabilizers||(Ce.frameworkStabilizers=[]),Ce.frameworkStabilizers.push(r=>{const i=Ce.getAllAngularTestabilities();let o=i.length,s=!1;const a=function(l){s=s||l,o--,0==o&&r(s)};i.forEach(l=>{l.whenStable(a)})})}findTestabilityInTree(n,e,r){return null==e?null:n.getTestability(e)??(r?No().isShadowRoot(e)?this.findTestabilityInTree(n,e.host,!0):this.findTestabilityInTree(n,e.parentElement,!0):null)}},deps:[]},{provide:Gy,useClass:Id,deps:[U,Sd,Na]},{provide:Id,useClass:Id,deps:[U,Sd,Na]}],iD=[{provide:cu,useValue:"root"},{provide:In,useFactory:function VP(){return new In},deps:[]},{provide:th,useClass:RP,multi:!0,deps:[Et,U,Mn]},{provide:th,useClass:FP,multi:!0,deps:[Et]},YC,ZC,WC,{provide:_m,useExisting:YC},{provide:HC,useClass:EP,deps:[]},[]];let zP=(()=>{class t{constructor(e){}static withServerTransition(e){return{ngModule:t,providers:[{provide:ia,useValue:e.appId}]}}static#e=this.\u0275fac=function(r){return new(r||t)(j(HP,12))};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({providers:[...iD,...rD],imports:[qR,Ck]})}return t})();function lD(t,n,e,r,i,o,s){try{var a=t[o](s),l=a.value}catch(c){return void e(c)}a.done?n(l):Promise.resolve(l).then(r,i)}function re(t){return function(){var n=this,e=arguments;return new Promise(function(r,i){var o=t.apply(n,e);function s(l){lD(o,r,i,s,a,"next",l)}function a(l){lD(o,r,i,s,a,"throw",l)}s(void 0)})}}function cD(t,n){return se(n)?Pi(t,n,1):Pi(t,1)}function dD(t){return Je((n,e)=>{try{n.subscribe(e)}finally{e.add(t)}})}typeof window<"u"&&window;class el{}class tl{}class tn{constructor(n){this.normalizedNames=new Map,this.lazyUpdate=null,n?"string"==typeof n?this.lazyInit=()=>{this.headers=new Map,n.split("\n").forEach(e=>{const r=e.indexOf(":");if(r>0){const i=e.slice(0,r),o=i.toLowerCase(),s=e.slice(r+1).trim();this.maybeSetNormalizedName(i,o),this.headers.has(o)?this.headers.get(o).push(s):this.headers.set(o,[s])}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((e,r)=>{this.setHeaderEntries(r,e)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([e,r])=>{this.setHeaderEntries(e,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();const e=this.headers.get(n.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,e){return this.clone({name:n,value:e,op:"a"})}set(n,e){return this.clone({name:n,value:e,op:"s"})}delete(n,e){return this.clone({name:n,value:e,op:"d"})}maybeSetNormalizedName(n,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,n)}init(){this.lazyInit&&(this.lazyInit instanceof tn?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(e=>{this.headers.set(e,n.headers.get(e)),this.normalizedNames.set(e,n.normalizedNames.get(e))})}clone(n){const e=new tn;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof tn?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([n]),e}applyUpdate(n){const e=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if("string"==typeof r&&(r=[r]),0===r.length)return;this.maybeSetNormalizedName(n.name,e);const i=("a"===n.op?this.headers.get(e):void 0)||[];i.push(...r),this.headers.set(e,i);break;case"d":const o=n.value;if(o){let s=this.headers.get(e);if(!s)return;s=s.filter(a=>-1===o.indexOf(a)),0===s.length?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,s)}else this.headers.delete(e),this.normalizedNames.delete(e)}}setHeaderEntries(n,e){const r=(Array.isArray(e)?e:[e]).map(o=>o.toString()),i=n.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(n,i)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>n(this.normalizedNames.get(e),this.headers.get(e)))}}class KP{encodeKey(n){return hD(n)}encodeValue(n){return hD(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}}const YP=/%(\d[a-f0-9])/gi,XP={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function hD(t){return encodeURIComponent(t).replace(YP,(n,e)=>XP[e]??n)}function nl(t){return`${t}`}class Zn{constructor(n={}){if(this.updates=null,this.cloneFrom=null,this.encoder=n.encoder||new KP,n.fromString){if(n.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=function QP(t,n){const e=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(i=>{const o=i.indexOf("="),[s,a]=-1==o?[n.decodeKey(i),""]:[n.decodeKey(i.slice(0,o)),n.decodeValue(i.slice(o+1))],l=e.get(s)||[];l.push(a),e.set(s,l)}),e}(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(e=>{const r=n.fromObject[e],i=Array.isArray(r)?r.map(nl):[nl(r)];this.map.set(e,i)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();const e=this.map.get(n);return e?e[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,e){return this.clone({param:n,value:e,op:"a"})}appendAll(n){const e=[];return Object.keys(n).forEach(r=>{const i=n[r];Array.isArray(i)?i.forEach(o=>{e.push({param:r,value:o,op:"a"})}):e.push({param:r,value:i,op:"a"})}),this.clone(e)}set(n,e){return this.clone({param:n,value:e,op:"s"})}delete(n,e){return this.clone({param:n,value:e,op:"d"})}toString(){return this.init(),this.keys().map(n=>{const e=this.encoder.encodeKey(n);return this.map.get(n).map(r=>e+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>""!==n).join("&")}clone(n){const e=new Zn({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(n),e}init(){null===this.map&&(this.map=new Map),null!==this.cloneFrom&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":const e=("a"===n.op?this.map.get(n.param):void 0)||[];e.push(nl(n.value)),this.map.set(n.param,e);break;case"d":if(void 0===n.value){this.map.delete(n.param);break}{let r=this.map.get(n.param)||[];const i=r.indexOf(nl(n.value));-1!==i&&r.splice(i,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}}}),this.cloneFrom=this.updates=null)}}class JP{constructor(){this.map=new Map}set(n,e){return this.map.set(n,e),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}}function fD(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer}function pD(t){return typeof Blob<"u"&&t instanceof Blob}function gD(t){return typeof FormData<"u"&&t instanceof FormData}class jo{constructor(n,e,r,i){let o;if(this.url=e,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=n.toUpperCase(),function eN(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}(this.method)||i?(this.body=void 0!==r?r:null,o=i):o=r,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params)),this.headers||(this.headers=new tn),this.context||(this.context=new JP),this.params){const s=this.params.toString();if(0===s.length)this.urlWithParams=e;else{const a=e.indexOf("?");this.urlWithParams=e+(-1===a?"?":a<e.length-1?"&":"")+s}}else this.params=new Zn,this.urlWithParams=e}serializeBody(){return null===this.body?null:fD(this.body)||pD(this.body)||gD(this.body)||function tN(t){return typeof URLSearchParams<"u"&&t instanceof URLSearchParams}(this.body)||"string"==typeof this.body?this.body:this.body instanceof Zn?this.body.toString():"object"==typeof this.body||"boolean"==typeof this.body||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return null===this.body||gD(this.body)?null:pD(this.body)?this.body.type||null:fD(this.body)?null:"string"==typeof this.body?"text/plain":this.body instanceof Zn?"application/x-www-form-urlencoded;charset=UTF-8":"object"==typeof this.body||"number"==typeof this.body||"boolean"==typeof this.body?"application/json":null}clone(n={}){const e=n.method||this.method,r=n.url||this.url,i=n.responseType||this.responseType,o=void 0!==n.body?n.body:this.body,s=void 0!==n.withCredentials?n.withCredentials:this.withCredentials,a=void 0!==n.reportProgress?n.reportProgress:this.reportProgress;let l=n.headers||this.headers,c=n.params||this.params;const u=n.context??this.context;return void 0!==n.setHeaders&&(l=Object.keys(n.setHeaders).reduce((d,h)=>d.set(h,n.setHeaders[h]),l)),n.setParams&&(c=Object.keys(n.setParams).reduce((d,h)=>d.set(h,n.setParams[h]),c)),new jo(e,r,o,{params:c,headers:l,context:u,reportProgress:a,responseType:i,withCredentials:s})}}var Si=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}(Si||{});class lh{constructor(n,e=200,r="OK"){this.headers=n.headers||new tn,this.status=void 0!==n.status?n.status:e,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}}class ch extends lh{constructor(n={}){super(n),this.type=Si.ResponseHeader}clone(n={}){return new ch({headers:n.headers||this.headers,status:void 0!==n.status?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}}class Oi extends lh{constructor(n={}){super(n),this.type=Si.Response,this.body=void 0!==n.body?n.body:null}clone(n={}){return new Oi({body:void 0!==n.body?n.body:this.body,headers:n.headers||this.headers,status:void 0!==n.status?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}}class mD extends lh{constructor(n){super(n,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.message=this.status>=200&&this.status<300?`Http failure during parsing for ${n.url||"(unknown url)"}`:`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}}function uh(t,n){return{body:n,headers:t.headers,context:t.context,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials}}let dh=(()=>{class t{constructor(e){this.handler=e}request(e,r,i={}){let o;if(e instanceof jo)o=e;else{let l,c;l=i.headers instanceof tn?i.headers:new tn(i.headers),i.params&&(c=i.params instanceof Zn?i.params:new Zn({fromObject:i.params})),o=new jo(e,r,void 0!==i.body?i.body:null,{headers:l,context:i.context,params:c,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials})}const s=Pt(o).pipe(cD(l=>this.handler.handle(l)));if(e instanceof jo||"events"===i.observe)return s;const a=s.pipe(function uD(t,n){return Je((e,r)=>{let i=0;e.subscribe(Ge(r,o=>t.call(n,o,i++)&&r.next(o)))})}(l=>l instanceof Oi));switch(i.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return a.pipe(ze(l=>{if(null!==l.body&&!(l.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return l.body}));case"blob":return a.pipe(ze(l=>{if(null!==l.body&&!(l.body instanceof Blob))throw new Error("Response is not a Blob.");return l.body}));case"text":return a.pipe(ze(l=>{if(null!==l.body&&"string"!=typeof l.body)throw new Error("Response is not a string.");return l.body}));default:return a.pipe(ze(l=>l.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${i.observe}}`)}}delete(e,r={}){return this.request("DELETE",e,r)}get(e,r={}){return this.request("GET",e,r)}head(e,r={}){return this.request("HEAD",e,r)}jsonp(e,r){return this.request("JSONP",e,{params:(new Zn).append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(e,r={}){return this.request("OPTIONS",e,r)}patch(e,r,i={}){return this.request("PATCH",e,uh(i,r))}post(e,r,i={}){return this.request("POST",e,uh(i,r))}put(e,r,i={}){return this.request("PUT",e,uh(i,r))}static#e=this.\u0275fac=function(r){return new(r||t)(j(el))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();function yD(t,n){return n(t)}function rN(t,n){return(e,r)=>n.intercept(e,{handle:i=>t(i,r)})}const oN=new P(""),Ho=new P(""),CD=new P("");function sN(){let t=null;return(n,e)=>{null===t&&(t=(Y(oN,{optional:!0})??[]).reduceRight(rN,yD));const r=Y(wd),i=r.add();return t(n,e).pipe(dD(()=>r.remove(i)))}}let DD=(()=>{class t extends el{constructor(e,r){super(),this.backend=e,this.injector=r,this.chain=null,this.pendingTasks=Y(wd)}handle(e){if(null===this.chain){const i=Array.from(new Set([...this.injector.get(Ho),...this.injector.get(CD,[])]));this.chain=i.reduceRight((o,s)=>function iN(t,n,e){return(r,i)=>e.runInContext(()=>n(r,o=>t(o,i)))}(o,s,this.injector),yD)}const r=this.pendingTasks.add();return this.chain(e,i=>this.backend.handle(i)).pipe(dD(()=>this.pendingTasks.remove(r)))}static#e=this.\u0275fac=function(r){return new(r||t)(j(tl),j(un))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();const uN=/^\)\]\}',?\n/;let wD=(()=>{class t{constructor(e){this.xhrFactory=e}handle(e){if("JSONP"===e.method)throw new O(-2800,!1);const r=this.xhrFactory;return(r.\u0275loadImpl?ir(r.\u0275loadImpl()):Pt(null)).pipe(Tr(()=>new ve(o=>{const s=r.build();if(s.open(e.method,e.urlWithParams),e.withCredentials&&(s.withCredentials=!0),e.headers.forEach((v,D)=>s.setRequestHeader(v,D.join(","))),e.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!e.headers.has("Content-Type")){const v=e.detectContentTypeHeader();null!==v&&s.setRequestHeader("Content-Type",v)}if(e.responseType){const v=e.responseType.toLowerCase();s.responseType="json"!==v?v:"text"}const a=e.serializeBody();let l=null;const c=()=>{if(null!==l)return l;const v=s.statusText||"OK",D=new tn(s.getAllResponseHeaders()),I=function dN(t){return"responseURL"in t&&t.responseURL?t.responseURL:/^X-Request-URL:/m.test(t.getAllResponseHeaders())?t.getResponseHeader("X-Request-URL"):null}(s)||e.url;return l=new ch({headers:D,status:s.status,statusText:v,url:I}),l},u=()=>{let{headers:v,status:D,statusText:I,url:w}=c(),N=null;204!==D&&(N=typeof s.response>"u"?s.responseText:s.response),0===D&&(D=N?200:0);let z=D>=200&&D<300;if("json"===e.responseType&&"string"==typeof N){const ne=N;N=N.replace(uN,"");try{N=""!==N?JSON.parse(N):null}catch(Ue){N=ne,z&&(z=!1,N={error:Ue,text:N})}}z?(o.next(new Oi({body:N,headers:v,status:D,statusText:I,url:w||void 0})),o.complete()):o.error(new mD({error:N,headers:v,status:D,statusText:I,url:w||void 0}))},d=v=>{const{url:D}=c(),I=new mD({error:v,status:s.status||0,statusText:s.statusText||"Unknown Error",url:D||void 0});o.error(I)};let h=!1;const f=v=>{h||(o.next(c()),h=!0);let D={type:Si.DownloadProgress,loaded:v.loaded};v.lengthComputable&&(D.total=v.total),"text"===e.responseType&&s.responseText&&(D.partialText=s.responseText),o.next(D)},g=v=>{let D={type:Si.UploadProgress,loaded:v.loaded};v.lengthComputable&&(D.total=v.total),o.next(D)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),e.reportProgress&&(s.addEventListener("progress",f),null!==a&&s.upload&&s.upload.addEventListener("progress",g)),s.send(a),o.next({type:Si.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",d),e.reportProgress&&(s.removeEventListener("progress",f),null!==a&&s.upload&&s.upload.removeEventListener("progress",g)),s.readyState!==s.DONE&&s.abort()}})))}static#e=this.\u0275fac=function(r){return new(r||t)(j(HC))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();const hh=new P("XSRF_ENABLED"),ED=new P("XSRF_COOKIE_NAME",{providedIn:"root",factory:()=>"XSRF-TOKEN"}),MD=new P("XSRF_HEADER_NAME",{providedIn:"root",factory:()=>"X-XSRF-TOKEN"});class ID{}let pN=(()=>{class t{constructor(e,r,i){this.doc=e,this.platform=r,this.cookieName=i,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if("server"===this.platform)return null;const e=this.doc.cookie||"";return e!==this.lastCookieString&&(this.parseCount++,this.lastToken=xC(e,this.cookieName),this.lastCookieString=e),this.lastToken}static#e=this.\u0275fac=function(r){return new(r||t)(j(Et),j(Mn),j(ED))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();function gN(t,n){const e=t.url.toLowerCase();if(!Y(hh)||"GET"===t.method||"HEAD"===t.method||e.startsWith("http://")||e.startsWith("https://"))return n(t);const r=Y(ID).getToken(),i=Y(MD);return null!=r&&!t.headers.has(i)&&(t=t.clone({headers:t.headers.set(i,r)})),n(t)}var Kn=function(t){return t[t.Interceptors=0]="Interceptors",t[t.LegacyInterceptors=1]="LegacyInterceptors",t[t.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",t[t.NoXsrfProtection=3]="NoXsrfProtection",t[t.JsonpSupport=4]="JsonpSupport",t[t.RequestsMadeViaParent=5]="RequestsMadeViaParent",t[t.Fetch=6]="Fetch",t}(Kn||{});function mN(...t){const n=[dh,wD,DD,{provide:el,useExisting:DD},{provide:tl,useExisting:wD},{provide:Ho,useValue:gN,multi:!0},{provide:hh,useValue:!0},{provide:ID,useClass:pN}];for(const e of t)n.push(...e.\u0275providers);return function su(t){return{\u0275providers:t}}(n)}const SD=new P("LEGACY_INTERCEPTOR_FN");function _N(){return function vr(t,n){return{\u0275kind:t,\u0275providers:n}}(Kn.LegacyInterceptors,[{provide:SD,useFactory:sN},{provide:Ho,useExisting:SD,multi:!0}])}let vN=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({providers:[mN(_N())]})}return t})();const{isArray:EN}=Array,{getPrototypeOf:MN,prototype:IN,keys:SN}=Object;const{isArray:TN}=Array;function TD(t,n){return t.reduce((e,r,i)=>(e[r]=n[i],e),{})}function Rt(t){return t<=0?()=>zl:Je((n,e)=>{let r=0;n.subscribe(Ge(e,i=>{++r<=t&&(e.next(i),t<=r&&e.complete())}))})}function Se(t){return Je((n,e)=>{st(t).subscribe(Ge(e,()=>e.complete(),Al)),!e.closed&&n.subscribe(e)})}const AD=["*"];class nn{_clearListeners(){for(const n of this._listeners)n.remove();this._listeners=[]}constructor(n){this._ngZone=n,this._pending=[],this._listeners=[],this._targetStream=new Pe(void 0)}getLazyEmitter(n){return this._targetStream.pipe(Tr(e=>{const r=new ve(i=>{if(!e)return void this._pending.push({observable:r,observer:i});const o=e.addListener(n,s=>{this._ngZone.run(()=>i.next(s))});if(o)return this._listeners.push(o),()=>o.remove();i.complete()});return r}))}setTarget(n){const e=this._targetStream.value;n!==e&&(e&&(this._clearListeners(),this._pending=[]),this._targetStream.next(n),this._pending.forEach(r=>r.observable.subscribe(r.observer)),this._pending=[])}destroy(){this._clearListeners(),this._pending=[],this._targetStream.complete()}}const zo={center:{lat:37.421995,lng:-122.084092},zoom:17,mapTypeId:"roadmap"};let it=(()=>{class t{set center(e){this._center=e}set zoom(e){this._zoom=e}set options(e){this._options=e||zo}constructor(e,r,i){if(this._elementRef=e,this._ngZone=r,this._eventManager=new nn(Y(U)),this.height="500px",this.width="500px",this._options=zo,this.mapInitialized=new B,this.authFailure=new B,this.boundsChanged=this._eventManager.getLazyEmitter("bounds_changed"),this.centerChanged=this._eventManager.getLazyEmitter("center_changed"),this.mapClick=this._eventManager.getLazyEmitter("click"),this.mapDblclick=this._eventManager.getLazyEmitter("dblclick"),this.mapDrag=this._eventManager.getLazyEmitter("drag"),this.mapDragend=this._eventManager.getLazyEmitter("dragend"),this.mapDragstart=this._eventManager.getLazyEmitter("dragstart"),this.headingChanged=this._eventManager.getLazyEmitter("heading_changed"),this.idle=this._eventManager.getLazyEmitter("idle"),this.maptypeidChanged=this._eventManager.getLazyEmitter("maptypeid_changed"),this.mapMousemove=this._eventManager.getLazyEmitter("mousemove"),this.mapMouseout=this._eventManager.getLazyEmitter("mouseout"),this.mapMouseover=this._eventManager.getLazyEmitter("mouseover"),this.projectionChanged=this._eventManager.getLazyEmitter("projection_changed"),this.mapRightclick=this._eventManager.getLazyEmitter("rightclick"),this.tilesloaded=this._eventManager.getLazyEmitter("tilesloaded"),this.tiltChanged=this._eventManager.getLazyEmitter("tilt_changed"),this.zoomChanged=this._eventManager.getLazyEmitter("zoom_changed"),this._isBrowser=BC(i),this._isBrowser){const o=window;this._existingAuthFailureCallback=o.gm_authFailure,o.gm_authFailure=()=>{this._existingAuthFailureCallback&&this._existingAuthFailureCallback(),this.authFailure.emit()}}}ngOnChanges(e){(e.height||e.width)&&this._setSize();const r=this.googleMap;r&&(e.options&&r.setOptions(this._combineOptions()),e.center&&this._center&&r.setCenter(this._center),e.zoom&&null!=this._zoom&&r.setZoom(this._zoom),e.mapTypeId&&this.mapTypeId&&r.setMapTypeId(this.mapTypeId))}ngOnInit(){this._isBrowser&&(this._mapEl=this._elementRef.nativeElement.querySelector(".map-container"),this._setSize(),this._ngZone.runOutsideAngular(()=>{this.googleMap=new google.maps.Map(this._mapEl,this._combineOptions())}),this._eventManager.setTarget(this.googleMap),this.mapInitialized.emit(this.googleMap))}ngOnDestroy(){this._eventManager.destroy(),this._isBrowser&&(window.gm_authFailure=this._existingAuthFailureCallback)}fitBounds(e,r){this._assertInitialized(),this.googleMap.fitBounds(e,r)}panBy(e,r){this._assertInitialized(),this.googleMap.panBy(e,r)}panTo(e){this._assertInitialized(),this.googleMap.panTo(e)}panToBounds(e,r){this._assertInitialized(),this.googleMap.panToBounds(e,r)}getBounds(){return this._assertInitialized(),this.googleMap.getBounds()||null}getCenter(){return this._assertInitialized(),this.googleMap.getCenter()}getClickableIcons(){return this._assertInitialized(),this.googleMap.getClickableIcons()}getHeading(){return this._assertInitialized(),this.googleMap.getHeading()}getMapTypeId(){return this._assertInitialized(),this.googleMap.getMapTypeId()}getProjection(){return this._assertInitialized(),this.googleMap.getProjection()||null}getStreetView(){return this._assertInitialized(),this.googleMap.getStreetView()}getTilt(){return this._assertInitialized(),this.googleMap.getTilt()}getZoom(){return this._assertInitialized(),this.googleMap.getZoom()}get controls(){return this._assertInitialized(),this.googleMap.controls}get data(){return this._assertInitialized(),this.googleMap.data}get mapTypes(){return this._assertInitialized(),this.googleMap.mapTypes}get overlayMapTypes(){return this._assertInitialized(),this.googleMap.overlayMapTypes}_setSize(){if(this._mapEl){const e=this._mapEl.style;e.height=null===this.height?"":PD(this.height)||"500px",e.width=null===this.width?"":PD(this.width)||"500px"}}_combineOptions(){const e=this._options||{};return{...e,center:this._center||e.center||zo.center,zoom:this._zoom??e.zoom??zo.zoom,mapTypeId:this.mapTypeId||e.mapTypeId||zo.mapTypeId}}_assertInitialized(){}static#e=this.\u0275fac=function(r){return new(r||t)(C(Xe),C(U),C(Mn))};static#t=this.\u0275cmp=$t({type:t,selectors:[["google-map"]],inputs:{height:"height",width:"width",mapTypeId:"mapTypeId",center:"center",zoom:"zoom",options:"options"},outputs:{mapInitialized:"mapInitialized",authFailure:"authFailure",boundsChanged:"boundsChanged",centerChanged:"centerChanged",mapClick:"mapClick",mapDblclick:"mapDblclick",mapDrag:"mapDrag",mapDragend:"mapDragend",mapDragstart:"mapDragstart",headingChanged:"headingChanged",idle:"idle",maptypeidChanged:"maptypeidChanged",mapMousemove:"mapMousemove",mapMouseout:"mapMouseout",mapMouseover:"mapMouseover",projectionChanged:"projectionChanged",mapRightclick:"mapRightclick",tilesloaded:"tilesloaded",tiltChanged:"tiltChanged",zoomChanged:"zoomChanged"},exportAs:["googleMap"],features:[Qe],ngContentSelectors:AD,decls:2,vars:0,consts:[[1,"map-container"]],template:function(r,i){1&r&&(function Ku(t){const n=E()[Ae][tt];if(!n.projection){const r=n.projection=to(t?t.length:1,null),i=r.slice();let o=n.child;for(;null!==o;){const s=t?_T(o,t):0;null!==s&&(i[s]?i[s].projectionNext=o:r[s]=o,i[s]=o),o=o.next}}}(),W(0,"div",0),function Qu(t,n=0,e){const r=E(),i=ce(),o=ai(i,ie+t,16,null,e||null);null===o.projection&&(o.projection=n),vc(),(!r[Dn]||Vr())&&32!=(32&o.flags)&&function pI(t,n,e){Ug(n[X],0,n,e,Zc(t,e,n),Lg(e.parent||n[tt],e,n))}(i,r,o)}(1))},encapsulation:2,changeDetection:0})}return t})();const kN=/([A-Za-z%]+)$/;function PD(t){return null==t?"":kN.test(t)?t:`${t}px`}let KN=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({})}return t})();function FD(t){return Je((n,e)=>{let o,r=null,i=!1;r=n.subscribe(Ge(e,void 0,void 0,s=>{o=st(t(s,FD(t)(n))),r?(r.unsubscribe(),r=null,o.subscribe(e)):i=!0})),i&&(r.unsubscribe(),r=null,o.subscribe(e))})}function yr(t){return!!t&&(t instanceof ve||se(t.lift)&&se(t.subscribe))}function LD(...t){const n=function Nf(t){return se(Ul(t))?t.pop():void 0}(t),{args:e,keys:r}=function OD(t){if(1===t.length){const n=t[0];if(EN(n))return{args:n,keys:null};if(function ON(t){return t&&"object"==typeof t&&MN(t)===IN}(n)){const e=SN(n);return{args:e.map(r=>n[r]),keys:e}}}return{args:t,keys:null}}(t),i=new ve(o=>{const{length:s}=e;if(!s)return void o.complete();const a=new Array(s);let l=s,c=s;for(let u=0;u<s;u++){let d=!1;st(e[u]).subscribe(Ge(o,h=>{d||(d=!0,c--),a[u]=h},()=>l--,void 0,()=>{(!l||!d)&&(c||o.next(r?TD(r,a):a),o.complete())}))}});return n?i.pipe(function fh(t){return ze(n=>function xN(t,n){return TN(n)?t(...n):t(n)}(t,n))}(n)):i}function il(...t){return function YN(){return Rf(1)}()(ir(t,Ni(t)))}function VD(t){return new ve(n=>{st(t()).subscribe(n)})}const gh={now:()=>(gh.delegate||Date).now(),delegate:void 0};class XN extends te{constructor(n=1/0,e=1/0,r=gh){super(),this._bufferSize=n,this._windowTime=e,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=e===1/0,this._bufferSize=Math.max(1,n),this._windowTime=Math.max(1,e)}next(n){const{isStopped:e,_buffer:r,_infiniteTimeWindow:i,_timestampProvider:o,_windowTime:s}=this;e||(r.push(n),!i&&r.push(o.now()+s)),this._trimBuffer(),super.next(n)}_subscribe(n){this._throwIfClosed(),this._trimBuffer();const e=this._innerSubscribe(n),{_infiniteTimeWindow:r,_buffer:i}=this,o=i.slice();for(let s=0;s<o.length&&!n.closed;s+=r?1:2)n.next(o[s]);return this._checkFinalizedStatuses(n),e}_trimBuffer(){const{_bufferSize:n,_timestampProvider:e,_buffer:r,_infiniteTimeWindow:i}=this,o=(i?1:2)*n;if(n<1/0&&o<r.length&&r.splice(0,r.length-o),!i){const s=e.now();let a=0;for(let l=1;l<r.length&&r[l]<=s;l+=2)a=l;a&&r.splice(0,a+1)}}}function BD(t,n,e){let r,i=!1;return t&&"object"==typeof t?({bufferSize:r=1/0,windowTime:n=1/0,refCount:i=!1,scheduler:e}=t):r=t??1/0,$l({connector:()=>new XN(r,n,e),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:i})}class Uo{}let jD=(()=>{class t extends Uo{getTranslation(e){return Pt({})}static \u0275fac=function(){let e;return function(i){return(e||(e=ke(t)))(i||t)}}();static \u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();class mh{}let HD=(()=>{class t{handle(e){return e.key}static \u0275fac=function(r){return new(r||t)};static \u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();function ol(t,n){if(t===n)return!0;if(null===t||null===n)return!1;if(t!=t&&n!=n)return!0;let i,o,s,e=typeof t;if(e==typeof n&&"object"==e){if(!Array.isArray(t)){if(Array.isArray(n))return!1;for(o in s=Object.create(null),t){if(!ol(t[o],n[o]))return!1;s[o]=!0}for(o in n)if(!(o in s)&&typeof n[o]<"u")return!1;return!0}if(!Array.isArray(n))return!1;if((i=t.length)==n.length){for(o=0;o<i;o++)if(!ol(t[o],n[o]))return!1;return!0}}return!1}function Qn(t){return typeof t<"u"&&null!==t}function _h(t){return t&&"object"==typeof t&&!Array.isArray(t)}function zD(t,n){let e=Object.assign({},t);return _h(t)&&_h(n)&&Object.keys(n).forEach(r=>{_h(n[r])?r in t?e[r]=zD(t[r],n[r]):Object.assign(e,{[r]:n[r]}):Object.assign(e,{[r]:n[r]})}),e}class sl{}let UD=(()=>{class t extends sl{templateMatcher=/{{\s?([^{}\s]*)\s?}}/g;interpolate(e,r){let i;return i="string"==typeof e?this.interpolateString(e,r):"function"==typeof e?this.interpolateFunction(e,r):e,i}getValue(e,r){let i="string"==typeof r?r.split("."):[r];r="";do{r+=i.shift(),!Qn(e)||!Qn(e[r])||"object"!=typeof e[r]&&i.length?i.length?r+=".":e=void 0:(e=e[r],r="")}while(i.length);return e}interpolateFunction(e,r){return e(r)}interpolateString(e,r){return r?e.replace(this.templateMatcher,(i,o)=>{let s=this.getValue(r,o);return Qn(s)?s:i}):e}static \u0275fac=function(){let e;return function(i){return(e||(e=ke(t)))(i||t)}}();static \u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();class al{}let $D=(()=>{class t extends al{compile(e,r){return e}compileTranslations(e,r){return e}static \u0275fac=function(){let e;return function(i){return(e||(e=ke(t)))(i||t)}}();static \u0275prov=Q({token:t,factory:t.\u0275fac})}return t})();class GD{defaultLang;currentLang=this.defaultLang;translations={};langs=[];onTranslationChange=new B;onLangChange=new B;onDefaultLangChange=new B}const vh=new P("USE_STORE"),yh=new P("USE_DEFAULT_LANG"),Ch=new P("DEFAULT_LANGUAGE"),Dh=new P("USE_EXTEND");let Cr=(()=>{class t{store;currentLoader;compiler;parser;missingTranslationHandler;useDefaultLang;isolate;extend;loadingTranslations;pending=!1;_onTranslationChange=new B;_onLangChange=new B;_onDefaultLangChange=new B;_defaultLang;_currentLang;_langs=[];_translations={};_translationRequests={};get onTranslationChange(){return this.isolate?this._onTranslationChange:this.store.onTranslationChange}get onLangChange(){return this.isolate?this._onLangChange:this.store.onLangChange}get onDefaultLangChange(){return this.isolate?this._onDefaultLangChange:this.store.onDefaultLangChange}get defaultLang(){return this.isolate?this._defaultLang:this.store.defaultLang}set defaultLang(e){this.isolate?this._defaultLang=e:this.store.defaultLang=e}get currentLang(){return this.isolate?this._currentLang:this.store.currentLang}set currentLang(e){this.isolate?this._currentLang=e:this.store.currentLang=e}get langs(){return this.isolate?this._langs:this.store.langs}set langs(e){this.isolate?this._langs=e:this.store.langs=e}get translations(){return this.isolate?this._translations:this.store.translations}set translations(e){this.isolate?this._translations=e:this.store.translations=e}constructor(e,r,i,o,s,a=!0,l=!1,c=!1,u){this.store=e,this.currentLoader=r,this.compiler=i,this.parser=o,this.missingTranslationHandler=s,this.useDefaultLang=a,this.isolate=l,this.extend=c,u&&this.setDefaultLang(u)}setDefaultLang(e){if(e===this.defaultLang)return;let r=this.retrieveTranslations(e);typeof r<"u"?(null==this.defaultLang&&(this.defaultLang=e),r.pipe(Rt(1)).subscribe(i=>{this.changeDefaultLang(e)})):this.changeDefaultLang(e)}getDefaultLang(){return this.defaultLang}use(e){if(e===this.currentLang)return Pt(this.translations[e]);let r=this.retrieveTranslations(e);return typeof r<"u"?(this.currentLang||(this.currentLang=e),r.pipe(Rt(1)).subscribe(i=>{this.changeLang(e)}),r):(this.changeLang(e),Pt(this.translations[e]))}retrieveTranslations(e){let r;return(typeof this.translations[e]>"u"||this.extend)&&(this._translationRequests[e]=this._translationRequests[e]||this.getTranslation(e),r=this._translationRequests[e]),r}getTranslation(e){this.pending=!0;const r=this.currentLoader.getTranslation(e).pipe(BD(1),Rt(1));return this.loadingTranslations=r.pipe(ze(i=>this.compiler.compileTranslations(i,e)),BD(1),Rt(1)),this.loadingTranslations.subscribe({next:i=>{this.translations[e]=this.extend&&this.translations[e]?{...i,...this.translations[e]}:i,this.updateLangs(),this.pending=!1},error:i=>{this.pending=!1}}),r}setTranslation(e,r,i=!1){r=this.compiler.compileTranslations(r,e),this.translations[e]=(i||this.extend)&&this.translations[e]?zD(this.translations[e],r):r,this.updateLangs(),this.onTranslationChange.emit({lang:e,translations:this.translations[e]})}getLangs(){return this.langs}addLangs(e){e.forEach(r=>{-1===this.langs.indexOf(r)&&this.langs.push(r)})}updateLangs(){this.addLangs(Object.keys(this.translations))}getParsedResult(e,r,i){let o;if(r instanceof Array){let s={},a=!1;for(let l of r)s[l]=this.getParsedResult(e,l,i),yr(s[l])&&(a=!0);return a?LD(r.map(c=>yr(s[c])?s[c]:Pt(s[c]))).pipe(ze(c=>{let u={};return c.forEach((d,h)=>{u[r[h]]=d}),u})):s}if(e&&(o=this.parser.interpolate(this.parser.getValue(e,r),i)),typeof o>"u"&&null!=this.defaultLang&&this.defaultLang!==this.currentLang&&this.useDefaultLang&&(o=this.parser.interpolate(this.parser.getValue(this.translations[this.defaultLang],r),i)),typeof o>"u"){let s={key:r,translateService:this};typeof i<"u"&&(s.interpolateParams=i),o=this.missingTranslationHandler.handle(s)}return typeof o<"u"?o:r}get(e,r){if(!Qn(e)||!e.length)throw new Error('Parameter "key" required');if(this.pending)return this.loadingTranslations.pipe(cD(i=>yr(i=this.getParsedResult(i,e,r))?i:Pt(i)));{let i=this.getParsedResult(this.translations[this.currentLang],e,r);return yr(i)?i:Pt(i)}}getStreamOnTranslationChange(e,r){if(!Qn(e)||!e.length)throw new Error('Parameter "key" required');return il(VD(()=>this.get(e,r)),this.onTranslationChange.pipe(Tr(i=>{const o=this.getParsedResult(i.translations,e,r);return"function"==typeof o.subscribe?o:Pt(o)})))}stream(e,r){if(!Qn(e)||!e.length)throw new Error('Parameter "key" required');return il(VD(()=>this.get(e,r)),this.onLangChange.pipe(Tr(i=>{const o=this.getParsedResult(i.translations,e,r);return yr(o)?o:Pt(o)})))}instant(e,r){if(!Qn(e)||!e.length)throw new Error('Parameter "key" required');let i=this.getParsedResult(this.translations[this.currentLang],e,r);if(yr(i)){if(e instanceof Array){let o={};return e.forEach((s,a)=>{o[e[a]]=e[a]}),o}return e}return i}set(e,r,i=this.currentLang){this.translations[i][e]=this.compiler.compile(r,i),this.updateLangs(),this.onTranslationChange.emit({lang:i,translations:this.translations[i]})}changeLang(e){this.currentLang=e,this.onLangChange.emit({lang:e,translations:this.translations[e]}),null==this.defaultLang&&this.changeDefaultLang(e)}changeDefaultLang(e){this.defaultLang=e,this.onDefaultLangChange.emit({lang:e,translations:this.translations[e]})}reloadLang(e){return this.resetLang(e),this.getTranslation(e)}resetLang(e){this._translationRequests[e]=void 0,this.translations[e]=void 0}getBrowserLang(){if(typeof window>"u"||typeof window.navigator>"u")return;let e=window.navigator.languages?window.navigator.languages[0]:null;return e=e||window.navigator.language||window.navigator.browserLanguage||window.navigator.userLanguage,typeof e>"u"?void 0:(-1!==e.indexOf("-")&&(e=e.split("-")[0]),-1!==e.indexOf("_")&&(e=e.split("_")[0]),e)}getBrowserCultureLang(){if(typeof window>"u"||typeof window.navigator>"u")return;let e=window.navigator.languages?window.navigator.languages[0]:null;return e=e||window.navigator.language||window.navigator.browserLanguage||window.navigator.userLanguage,e}static \u0275fac=function(r){return new(r||t)(j(GD),j(Uo),j(al),j(sl),j(mh),j(yh),j(vh),j(Dh),j(Ch))};static \u0275prov=Q({token:t,factory:t.\u0275fac})}return t})(),Yn=(()=>{class t{translate;_ref;value="";lastKey=null;lastParams=[];onTranslationChange;onLangChange;onDefaultLangChange;constructor(e,r){this.translate=e,this._ref=r}updateValue(e,r,i){let o=s=>{this.value=void 0!==s?s:e,this.lastKey=e,this._ref.markForCheck()};if(i){let s=this.translate.getParsedResult(i,e,r);yr(s.subscribe)?s.subscribe(o):o(s)}this.translate.get(e,r).subscribe(o)}transform(e,...r){if(!e||!e.length)return e;if(ol(e,this.lastKey)&&ol(r,this.lastParams))return this.value;let i;if(Qn(r[0])&&r.length)if("string"==typeof r[0]&&r[0].length){let o=r[0].replace(/(\')?([a-zA-Z0-9_]+)(\')?(\s)?:/g,'"$2":').replace(/:(\s)?(\')(.*?)(\')/g,':"$3"');try{i=JSON.parse(o)}catch{throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${r[0]}`)}}else"object"==typeof r[0]&&!Array.isArray(r[0])&&(i=r[0]);return this.lastKey=e,this.lastParams=r,this.updateValue(e,i),this._dispose(),this.onTranslationChange||(this.onTranslationChange=this.translate.onTranslationChange.subscribe(o=>{this.lastKey&&o.lang===this.translate.currentLang&&(this.lastKey=null,this.updateValue(e,i,o.translations))})),this.onLangChange||(this.onLangChange=this.translate.onLangChange.subscribe(o=>{this.lastKey&&(this.lastKey=null,this.updateValue(e,i,o.translations))})),this.onDefaultLangChange||(this.onDefaultLangChange=this.translate.onDefaultLangChange.subscribe(()=>{this.lastKey&&(this.lastKey=null,this.updateValue(e,i))})),this.value}_dispose(){typeof this.onTranslationChange<"u"&&(this.onTranslationChange.unsubscribe(),this.onTranslationChange=void 0),typeof this.onLangChange<"u"&&(this.onLangChange.unsubscribe(),this.onLangChange=void 0),typeof this.onDefaultLangChange<"u"&&(this.onDefaultLangChange.unsubscribe(),this.onDefaultLangChange=void 0)}ngOnDestroy(){this._dispose()}static \u0275fac=function(r){return new(r||t)(C(Cr,16),C(Ei,16))};static \u0275pipe=at({name:"translate",type:t,pure:!1});static \u0275prov=Q({token:t,factory:t.\u0275fac})}return t})(),JN=(()=>{class t{static forRoot(e={}){return{ngModule:t,providers:[e.loader||{provide:Uo,useClass:jD},e.compiler||{provide:al,useClass:$D},e.parser||{provide:sl,useClass:UD},e.missingTranslationHandler||{provide:mh,useClass:HD},GD,{provide:vh,useValue:e.isolate},{provide:yh,useValue:e.useDefaultLang},{provide:Dh,useValue:e.extend},{provide:Ch,useValue:e.defaultLanguage},Cr]}}static forChild(e={}){return{ngModule:t,providers:[e.loader||{provide:Uo,useClass:jD},e.compiler||{provide:al,useClass:$D},e.parser||{provide:sl,useClass:UD},e.missingTranslationHandler||{provide:mh,useClass:HD},{provide:vh,useValue:e.isolate},{provide:yh,useValue:e.useDefaultLang},{provide:Dh,useValue:e.extend},{provide:Ch,useValue:e.defaultLanguage},Cr]}}static \u0275fac=function(r){return new(r||t)};static \u0275mod=et({type:t});static \u0275inj=We({})}return t})(),WD=(()=>{class t{constructor(e,r){this._renderer=e,this._elementRef=r,this.onChange=i=>{},this.onTouched=()=>{}}setProperty(e,r){this._renderer.setProperty(this._elementRef.nativeElement,e,r)}registerOnTouched(e){this.onTouched=e}registerOnChange(e){this.onChange=e}setDisabledState(e){this.setProperty("disabled",e)}static#e=this.\u0275fac=function(r){return new(r||t)(C(Un),C(Xe))};static#t=this.\u0275dir=H({type:t})}return t})(),Dr=(()=>{class t extends WD{static#e=this.\u0275fac=function(){let e;return function(i){return(e||(e=ke(t)))(i||t)}}();static#t=this.\u0275dir=H({type:t,features:[de]})}return t})();const mn=new P("NgValueAccessor"),eF={provide:mn,useExisting:ye(()=>bh),multi:!0};let bh=(()=>{class t extends Dr{writeValue(e){this.setProperty("checked",e)}static#e=this.\u0275fac=function(){let e;return function(i){return(e||(e=ke(t)))(i||t)}}();static#t=this.\u0275dir=H({type:t,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(r,i){1&r&&V("change",function(s){return i.onChange(s.target.checked)})("blur",function(){return i.onTouched()})},features:[ge([eF]),de]})}return t})();const tF={provide:mn,useExisting:ye(()=>br),multi:!0},rF=new P("CompositionEventMode");let br=(()=>{class t extends WD{constructor(e,r,i){super(e,r),this._compositionMode=i,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function nF(){const t=No()?No().getUserAgent():"";return/android (\d+)/.test(t.toLowerCase())}())}writeValue(e){this.setProperty("value",e??"")}_handleInput(e){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(e)}_compositionStart(){this._composing=!0}_compositionEnd(e){this._composing=!1,this._compositionMode&&this.onChange(e)}static#e=this.\u0275fac=function(r){return new(r||t)(C(Un),C(Xe),C(rF,8))};static#t=this.\u0275dir=H({type:t,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,i){1&r&&V("input",function(s){return i._handleInput(s.target.value)})("blur",function(){return i.onTouched()})("compositionstart",function(){return i._compositionStart()})("compositionend",function(s){return i._compositionEnd(s.target.value)})},features:[ge([tF]),de]})}return t})();const ot=new P("NgValidators"),Jn=new P("NgAsyncValidators");function tb(t){if(!t)return ll;let n,e;return"string"==typeof t?(e="","^"!==t.charAt(0)&&(e+="^"),e+=t,"$"!==t.charAt(t.length-1)&&(e+="$"),n=new RegExp(e)):(e=t.toString(),n=t),r=>{if(function Xn(t){return null==t||("string"==typeof t||Array.isArray(t))&&0===t.length}(r.value))return null;const i=r.value;return n.test(i)?null:{pattern:{requiredPattern:e,actualValue:i}}}}function ll(t){return null}function nb(t){return null!=t}function rb(t){return Ea(t)?ir(t):t}function ib(t){let n={};return t.forEach(e=>{n=null!=e?{...n,...e}:n}),0===Object.keys(n).length?null:n}function ob(t,n){return n.map(e=>e(t))}function sb(t){return t.map(n=>function oF(t){return!t.validate}(n)?n:e=>n.validate(e))}function wh(t){return null!=t?function ab(t){if(!t)return null;const n=t.filter(nb);return 0==n.length?null:function(e){return ib(ob(e,n))}}(sb(t)):null}function Eh(t){return null!=t?function lb(t){if(!t)return null;const n=t.filter(nb);return 0==n.length?null:function(e){return LD(ob(e,n).map(rb)).pipe(ze(ib))}}(sb(t)):null}function cb(t,n){return null===t?[n]:Array.isArray(t)?[...t,n]:[t,n]}function ub(t){return t._rawValidators}function db(t){return t._rawAsyncValidators}function Mh(t){return t?Array.isArray(t)?t:[t]:[]}function cl(t,n){return Array.isArray(t)?t.includes(n):t===n}function hb(t,n){const e=Mh(n);return Mh(t).forEach(i=>{cl(e,i)||e.push(i)}),e}function fb(t,n){return Mh(n).filter(e=>!cl(t,e))}class pb{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=wh(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=Eh(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,e){return!!this.control&&this.control.hasError(n,e)}getError(n,e){return this.control?this.control.getError(n,e):null}}class mt extends pb{get formDirective(){return null}get path(){return null}}class er extends pb{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class gb{constructor(n){this._cd=n}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}}let $o=(()=>{class t extends gb{constructor(e){super(e)}static#e=this.\u0275fac=function(r){return new(r||t)(C(er,2))};static#t=this.\u0275dir=H({type:t,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,i){2&r&&Gn("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)},features:[de]})}return t})(),mb=(()=>{class t extends gb{constructor(e){super(e)}static#e=this.\u0275fac=function(r){return new(r||t)(C(mt,10))};static#t=this.\u0275dir=H({type:t,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,i){2&r&&Gn("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)("ng-submitted",i.isSubmitted)},features:[de]})}return t})();const Go="VALID",dl="INVALID",xi="PENDING",Wo="DISABLED";function Oh(t){return(hl(t)?t.validators:t)||null}function Th(t,n){return(hl(n)?n.asyncValidators:t)||null}function hl(t){return null!=t&&!Array.isArray(t)&&"object"==typeof t}function vb(t,n,e){const r=t.controls;if(!(n?Object.keys(r):r).length)throw new O(1e3,"");if(!r[e])throw new O(1001,"")}function yb(t,n,e){t._forEachChild((r,i)=>{if(void 0===e[i])throw new O(1002,"")})}class fl{constructor(n,e){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(n),this._assignAsyncValidators(e)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get valid(){return this.status===Go}get invalid(){return this.status===dl}get pending(){return this.status==xi}get disabled(){return this.status===Wo}get enabled(){return this.status!==Wo}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(hb(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(hb(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(fb(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(fb(n,this._rawAsyncValidators))}hasValidator(n){return cl(this._rawValidators,n)}hasAsyncValidator(n){return cl(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){this.touched=!0,this._parent&&!n.onlySelf&&this._parent.markAsTouched(n)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(n=>n.markAllAsTouched())}markAsUntouched(n={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(e=>{e.markAsUntouched({onlySelf:!0})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n)}markAsDirty(n={}){this.pristine=!1,this._parent&&!n.onlySelf&&this._parent.markAsDirty(n)}markAsPristine(n={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(e=>{e.markAsPristine({onlySelf:!0})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n)}markAsPending(n={}){this.status=xi,!1!==n.emitEvent&&this.statusChanges.emit(this.status),this._parent&&!n.onlySelf&&this._parent.markAsPending(n)}disable(n={}){const e=this._parentMarkedDirty(n.onlySelf);this.status=Wo,this.errors=null,this._forEachChild(r=>{r.disable({...n,onlySelf:!0})}),this._updateValue(),!1!==n.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...n,skipPristineCheck:e}),this._onDisabledChange.forEach(r=>r(!0))}enable(n={}){const e=this._parentMarkedDirty(n.onlySelf);this.status=Go,this._forEachChild(r=>{r.enable({...n,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors({...n,skipPristineCheck:e}),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Go||this.status===xi)&&this._runAsyncValidator(n.emitEvent)),!1!==n.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(n)}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(e=>e._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Wo:Go}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n){if(this.asyncValidator){this.status=xi,this._hasOwnPendingAsyncValidator=!0;const e=rb(this.asyncValidator(this));this._asyncValidationSubscription=e.subscribe(r=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(r,{emitEvent:n})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(n,e={}){this.errors=n,this._updateControlsErrors(!1!==e.emitEvent)}get(n){let e=n;return null==e||(Array.isArray(e)||(e=e.split(".")),0===e.length)?null:e.reduce((r,i)=>r&&r._find(i),this)}getError(n,e){const r=e?this.get(e):this;return r&&r.errors?r.errors[n]:null}hasError(n,e){return!!this.getError(n,e)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(n)}_initObservables(){this.valueChanges=new B,this.statusChanges=new B}_calculateStatus(){return this._allControlsDisabled()?Wo:this.errors?dl:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(xi)?xi:this._anyControlsHaveStatus(dl)?dl:Go}_anyControlsHaveStatus(n){return this._anyControls(e=>e.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n={}){this.pristine=!this._anyControlsDirty(),this._parent&&!n.onlySelf&&this._parent._updatePristine(n)}_updateTouched(n={}){this.touched=this._anyControlsTouched(),this._parent&&!n.onlySelf&&this._parent._updateTouched(n)}_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){hl(n)&&null!=n.updateOn&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){return!n&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=function cF(t){return Array.isArray(t)?wh(t):t||null}(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=function uF(t){return Array.isArray(t)?Eh(t):t||null}(this._rawAsyncValidators)}}class qo extends fl{constructor(n,e,r){super(Oh(e),Th(r,e)),this.controls=n,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(n,e){return this.controls[n]?this.controls[n]:(this.controls[n]=e,e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange),e)}addControl(n,e,r={}){this.registerControl(n,e),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,e={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}setControl(n,e,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],e&&this.registerControl(n,e),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,e={}){yb(this,0,n),Object.keys(n).forEach(r=>{vb(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(n,e={}){null!=n&&(Object.keys(n).forEach(r=>{const i=this.controls[r];i&&i.patchValue(n[r],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(n={},e={}){this._forEachChild((r,i)=>{r.reset(n?n[i]:null,{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e),this._updateTouched(e),this.updateValueAndValidity(e)}getRawValue(){return this._reduceChildren({},(n,e,r)=>(n[r]=e.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(e,r)=>!!r._syncPendingControls()||e);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(e=>{const r=this.controls[e];r&&n(r,e)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(const[e,r]of Object.entries(this.controls))if(this.contains(e)&&n(r))return!0;return!1}_reduceValue(){return this._reduceChildren({},(e,r,i)=>((r.enabled||this.disabled)&&(e[i]=r.value),e))}_reduceChildren(n,e){let r=n;return this._forEachChild((i,o)=>{r=e(r,i,o)}),r}_allControlsDisabled(){for(const n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}}class Cb extends qo{}const wr=new P("CallSetDisabledState",{providedIn:"root",factory:()=>Zo}),Zo="always";function pl(t,n){return[...n.path,t]}function Ko(t,n,e=Zo){xh(t,n),n.valueAccessor.writeValue(t.value),(t.disabled||"always"===e)&&n.valueAccessor.setDisabledState?.(t.disabled),function hF(t,n){n.valueAccessor.registerOnChange(e=>{t._pendingValue=e,t._pendingChange=!0,t._pendingDirty=!0,"change"===t.updateOn&&Db(t,n)})}(t,n),function pF(t,n){const e=(r,i)=>{n.valueAccessor.writeValue(r),i&&n.viewToModelUpdate(r)};t.registerOnChange(e),n._registerOnDestroy(()=>{t._unregisterOnChange(e)})}(t,n),function fF(t,n){n.valueAccessor.registerOnTouched(()=>{t._pendingTouched=!0,"blur"===t.updateOn&&t._pendingChange&&Db(t,n),"submit"!==t.updateOn&&t.markAsTouched()})}(t,n),function dF(t,n){if(n.valueAccessor.setDisabledState){const e=r=>{n.valueAccessor.setDisabledState(r)};t.registerOnDisabledChange(e),n._registerOnDestroy(()=>{t._unregisterOnDisabledChange(e)})}}(t,n)}function gl(t,n,e=!0){const r=()=>{};n.valueAccessor&&(n.valueAccessor.registerOnChange(r),n.valueAccessor.registerOnTouched(r)),_l(t,n),t&&(n._invokeOnDestroyCallbacks(),t._registerOnCollectionChange(()=>{}))}function ml(t,n){t.forEach(e=>{e.registerOnValidatorChange&&e.registerOnValidatorChange(n)})}function xh(t,n){const e=ub(t);null!==n.validator?t.setValidators(cb(e,n.validator)):"function"==typeof e&&t.setValidators([e]);const r=db(t);null!==n.asyncValidator?t.setAsyncValidators(cb(r,n.asyncValidator)):"function"==typeof r&&t.setAsyncValidators([r]);const i=()=>t.updateValueAndValidity();ml(n._rawValidators,i),ml(n._rawAsyncValidators,i)}function _l(t,n){let e=!1;if(null!==t){if(null!==n.validator){const i=ub(t);if(Array.isArray(i)&&i.length>0){const o=i.filter(s=>s!==n.validator);o.length!==i.length&&(e=!0,t.setValidators(o))}}if(null!==n.asyncValidator){const i=db(t);if(Array.isArray(i)&&i.length>0){const o=i.filter(s=>s!==n.asyncValidator);o.length!==i.length&&(e=!0,t.setAsyncValidators(o))}}}const r=()=>{};return ml(n._rawValidators,r),ml(n._rawAsyncValidators,r),e}function Db(t,n){t._pendingDirty&&t.markAsDirty(),t.setValue(t._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(t._pendingValue),t._pendingChange=!1}function kh(t,n){if(!t.hasOwnProperty("model"))return!1;const e=t.model;return!!e.isFirstChange()||!Object.is(n,e.currentValue)}function Rh(t,n){if(!n)return null;let e,r,i;return Array.isArray(n),n.forEach(o=>{o.constructor===br?e=o:function _F(t){return Object.getPrototypeOf(t.constructor)===Dr}(o)?r=o:i=o}),i||r||e||null}function Eb(t,n){const e=t.indexOf(n);e>-1&&t.splice(e,1)}function Mb(t){return"object"==typeof t&&null!==t&&2===Object.keys(t).length&&"value"in t&&"disabled"in t}const Yo=class extends fl{constructor(n=null,e,r){super(Oh(e),Th(r,e)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(n),this._setUpdateStrategy(e),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),hl(e)&&(e.nonNullable||e.initialValueIsDefault)&&(this.defaultValue=Mb(n)?n.value:n)}setValue(n,e={}){this.value=this._pendingValue=n,this._onChange.length&&!1!==e.emitModelToViewChange&&this._onChange.forEach(r=>r(this.value,!1!==e.emitViewToModelChange)),this.updateValueAndValidity(e)}patchValue(n,e={}){this.setValue(n,e)}reset(n=this.defaultValue,e={}){this._applyFormState(n),this.markAsPristine(e),this.markAsUntouched(e),this.setValue(this.value,e),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){Eb(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){Eb(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(n){Mb(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}},bF={provide:er,useExisting:ye(()=>Xo)},Ob=(()=>Promise.resolve())();let Xo=(()=>{class t extends er{constructor(e,r,i,o,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this.control=new Yo,this._registered=!1,this.name="",this.update=new B,this._parent=e,this._setValidators(r),this._setAsyncValidators(i),this.valueAccessor=Rh(0,o)}ngOnChanges(e){if(this._checkForErrors(),!this._registered||"name"in e){if(this._registered&&(this._checkName(),this.formDirective)){const r=e.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in e&&this._updateDisabled(e),kh(e,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){Ko(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(e){Ob.then(()=>{this.control.setValue(e,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(e){const r=e.isDisabled.currentValue,i=0!==r&&function Fd(t){return"boolean"==typeof t?t:null!=t&&"false"!==t}(r);Ob.then(()=>{i&&!this.control.disabled?this.control.disable():!i&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(e){return this._parent?pl(e,this._parent):[e]}static#e=this.\u0275fac=function(r){return new(r||t)(C(mt,9),C(ot,10),C(Jn,10),C(mn,10),C(Ei,8),C(wr,8))};static#t=this.\u0275dir=H({type:t,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:["disabled","isDisabled"],model:["ngModel","model"],options:["ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[ge([bF]),de,Qe]})}return t})(),Tb=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275dir=H({type:t,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}return t})(),Ab=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({})}return t})();const IF={provide:mn,useExisting:ye(()=>Nh),multi:!0};let Nh=(()=>{class t extends Dr{writeValue(e){this.setProperty("value",parseFloat(e))}registerOnChange(e){this.onChange=r=>{e(""==r?null:parseFloat(r))}}static#e=this.\u0275fac=function(){let e;return function(i){return(e||(e=ke(t)))(i||t)}}();static#t=this.\u0275dir=H({type:t,selectors:[["input","type","range","formControlName",""],["input","type","range","formControl",""],["input","type","range","ngModel",""]],hostBindings:function(r,i){1&r&&V("change",function(s){return i.onChange(s.target.value)})("input",function(s){return i.onChange(s.target.value)})("blur",function(){return i.onTouched()})},features:[ge([IF]),de]})}return t})();const Fh=new P("NgModelWithFormControlWarning"),OF={provide:mt,useExisting:ye(()=>vl)};let vl=(()=>{class t extends mt{constructor(e,r,i){super(),this.callSetDisabledState=i,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new B,this._setValidators(e),this._setAsyncValidators(r)}ngOnChanges(e){this._checkFormPresent(),e.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(_l(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(e){const r=this.form.get(e.path);return Ko(r,e,this.callSetDisabledState),r.updateValueAndValidity({emitEvent:!1}),this.directives.push(e),r}getControl(e){return this.form.get(e.path)}removeControl(e){gl(e.control||null,e,!1),function vF(t,n){const e=t.indexOf(n);e>-1&&t.splice(e,1)}(this.directives,e)}addFormGroup(e){this._setUpFormContainer(e)}removeFormGroup(e){this._cleanUpFormContainer(e)}getFormGroup(e){return this.form.get(e.path)}addFormArray(e){this._setUpFormContainer(e)}removeFormArray(e){this._cleanUpFormContainer(e)}getFormArray(e){return this.form.get(e.path)}updateModel(e,r){this.form.get(e.path).setValue(r)}onSubmit(e){return this.submitted=!0,function wb(t,n){t._syncPendingControls(),n.forEach(e=>{const r=e.control;"submit"===r.updateOn&&r._pendingChange&&(e.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}(this.form,this.directives),this.ngSubmit.emit(e),"dialog"===e?.target?.method}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this.submitted=!1}_updateDomValue(){this.directives.forEach(e=>{const r=e.control,i=this.form.get(e.path);r!==i&&(gl(r||null,e),(t=>t instanceof Yo)(i)&&(Ko(i,e,this.callSetDisabledState),e.control=i))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(e){const r=this.form.get(e.path);(function bb(t,n){xh(t,n)})(r,e),r.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(e){if(this.form){const r=this.form.get(e.path);r&&function gF(t,n){return _l(t,n)}(r,e)&&r.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){xh(this.form,this),this._oldForm&&_l(this._oldForm,this)}_checkFormPresent(){}static#e=this.\u0275fac=function(r){return new(r||t)(C(ot,10),C(Jn,10),C(wr,8))};static#t=this.\u0275dir=H({type:t,selectors:[["","formGroup",""]],hostBindings:function(r,i){1&r&&V("submit",function(s){return i.onSubmit(s)})("reset",function(){return i.onReset()})},inputs:{form:["formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[ge([OF]),de,Qe]})}return t})();const AF={provide:er,useExisting:ye(()=>Bh)};let Bh=(()=>{class t extends er{set isDisabled(e){}static#e=this._ngModelWarningSentOnce=!1;constructor(e,r,i,o,s){super(),this._ngModelWarningConfig=s,this._added=!1,this.name=null,this.update=new B,this._ngModelWarningSent=!1,this._parent=e,this._setValidators(r),this._setAsyncValidators(i),this.valueAccessor=Rh(0,o)}ngOnChanges(e){this._added||this._setUpControl(),kh(e,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}get path(){return pl(null==this.name?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}static#t=this.\u0275fac=function(r){return new(r||t)(C(mt,13),C(ot,10),C(Jn,10),C(mn,10),C(Fh,8))};static#n=this.\u0275dir=H({type:t,selectors:[["","formControlName",""]],inputs:{name:["formControlName","name"],isDisabled:["disabled","isDisabled"],model:["ngModel","model"]},outputs:{update:"ngModelChange"},features:[ge([AF]),de,Qe]})}return t})();const kF={provide:mn,useExisting:ye(()=>yl),multi:!0};function Nb(t,n){return null==t?`${n}`:(n&&"object"==typeof n&&(n="Object"),`${t}: ${n}`.slice(0,50))}let yl=(()=>{class t extends Dr{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(e){this._compareWith=e}writeValue(e){this.value=e;const i=Nb(this._getOptionId(e),e);this.setProperty("value",i)}registerOnChange(e){this.onChange=r=>{this.value=this._getOptionValue(r),e(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(e){for(const r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r),e))return r;return null}_getOptionValue(e){const r=function RF(t){return t.split(":")[0]}(e);return this._optionMap.has(r)?this._optionMap.get(r):e}static#e=this.\u0275fac=function(){let e;return function(i){return(e||(e=ke(t)))(i||t)}}();static#t=this.\u0275dir=H({type:t,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(r,i){1&r&&V("change",function(s){return i.onChange(s.target.value)})("blur",function(){return i.onTouched()})},inputs:{compareWith:"compareWith"},features:[ge([kF]),de]})}return t})(),jh=(()=>{class t{constructor(e,r,i){this._element=e,this._renderer=r,this._select=i,this._select&&(this.id=this._select._registerOption())}set ngValue(e){null!=this._select&&(this._select._optionMap.set(this.id,e),this._setElementValue(Nb(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._setElementValue(e),this._select&&this._select.writeValue(this._select.value)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static#e=this.\u0275fac=function(r){return new(r||t)(C(Xe),C(Un),C(yl,9))};static#t=this.\u0275dir=H({type:t,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}})}return t})();const PF={provide:mn,useExisting:ye(()=>Hh),multi:!0};function Fb(t,n){return null==t?`${n}`:("string"==typeof n&&(n=`'${n}'`),n&&"object"==typeof n&&(n="Object"),`${t}: ${n}`.slice(0,50))}let Hh=(()=>{class t extends Dr{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(e){this._compareWith=e}writeValue(e){let r;if(this.value=e,Array.isArray(e)){const i=e.map(o=>this._getOptionId(o));r=(o,s)=>{o._setSelected(i.indexOf(s.toString())>-1)}}else r=(i,o)=>{i._setSelected(!1)};this._optionMap.forEach(r)}registerOnChange(e){this.onChange=r=>{const i=[],o=r.selectedOptions;if(void 0!==o){const s=o;for(let a=0;a<s.length;a++){const c=this._getOptionValue(s[a].value);i.push(c)}}else{const s=r.options;for(let a=0;a<s.length;a++){const l=s[a];if(l.selected){const c=this._getOptionValue(l.value);i.push(c)}}}this.value=i,e(i)}}_registerOption(e){const r=(this._idCounter++).toString();return this._optionMap.set(r,e),r}_getOptionId(e){for(const r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r)._value,e))return r;return null}_getOptionValue(e){const r=function NF(t){return t.split(":")[0]}(e);return this._optionMap.has(r)?this._optionMap.get(r)._value:e}static#e=this.\u0275fac=function(){let e;return function(i){return(e||(e=ke(t)))(i||t)}}();static#t=this.\u0275dir=H({type:t,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(r,i){1&r&&V("change",function(s){return i.onChange(s.target)})("blur",function(){return i.onTouched()})},inputs:{compareWith:"compareWith"},features:[ge([PF]),de]})}return t})(),zh=(()=>{class t{constructor(e,r,i){this._element=e,this._renderer=r,this._select=i,this._select&&(this.id=this._select._registerOption(this))}set ngValue(e){null!=this._select&&(this._value=e,this._setElementValue(Fb(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._select?(this._value=e,this._setElementValue(Fb(this.id,e)),this._select.writeValue(this._select.value)):this._setElementValue(e)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}_setSelected(e){this._renderer.setProperty(this._element.nativeElement,"selected",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static#e=this.\u0275fac=function(r){return new(r||t)(C(Xe),C(Un),C(Hh,9))};static#t=this.\u0275dir=H({type:t,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}})}return t})(),Er=(()=>{class t{constructor(){this._validator=ll}ngOnChanges(e){if(this.inputName in e){const r=this.normalizeInput(e[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):ll,this._onChange&&this._onChange()}}validate(e){return this._validator(e)}registerOnValidatorChange(e){this._onChange=e}enabled(e){return null!=e}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275dir=H({type:t,features:[Qe]})}return t})();const UF={provide:ot,useExisting:ye(()=>$h),multi:!0};let $h=(()=>{class t extends Er{constructor(){super(...arguments),this.inputName="pattern",this.normalizeInput=e=>e,this.createValidator=e=>tb(e)}static#e=this.\u0275fac=function(){let e;return function(i){return(e||(e=ke(t)))(i||t)}}();static#t=this.\u0275dir=H({type:t,selectors:[["","pattern","","formControlName",""],["","pattern","","formControl",""],["","pattern","","ngModel",""]],hostVars:1,hostBindings:function(r,i){2&r&&fn("pattern",i._enabled?i.pattern:null)},inputs:{pattern:"pattern"},features:[ge([UF]),de]})}return t})(),Gb=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({imports:[Ab]})}return t})();class Wb extends fl{constructor(n,e,r){super(Oh(e),Th(r,e)),this.controls=n,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(n){return this.controls[this._adjustIndex(n)]}push(n,e={}){this.controls.push(n),this._registerControl(n),this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}insert(n,e,r={}){this.controls.splice(n,0,e),this._registerControl(e),this.updateValueAndValidity({emitEvent:r.emitEvent})}removeAt(n,e={}){let r=this._adjustIndex(n);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),this.updateValueAndValidity({emitEvent:e.emitEvent})}setControl(n,e,r={}){let i=this._adjustIndex(n);i<0&&(i=0),this.controls[i]&&this.controls[i]._registerOnCollectionChange(()=>{}),this.controls.splice(i,1),e&&(this.controls.splice(i,0,e),this._registerControl(e)),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(n,e={}){yb(this,0,n),n.forEach((r,i)=>{vb(this,!1,i),this.at(i).setValue(r,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(n,e={}){null!=n&&(n.forEach((r,i)=>{this.at(i)&&this.at(i).patchValue(r,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(n=[],e={}){this._forEachChild((r,i)=>{r.reset(n[i],{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e),this._updateTouched(e),this.updateValueAndValidity(e)}getRawValue(){return this.controls.map(n=>n.getRawValue())}clear(n={}){this.controls.length<1||(this._forEachChild(e=>e._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:n.emitEvent}))}_adjustIndex(n){return n<0?n+this.length:n}_syncPendingControls(){let n=this.controls.reduce((e,r)=>!!r._syncPendingControls()||e,!1);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){this.controls.forEach((e,r)=>{n(e,r)})}_updateValue(){this.value=this.controls.filter(n=>n.enabled||this.disabled).map(n=>n.value)}_anyControls(n){return this.controls.some(e=>e.enabled&&n(e))}_setUpControls(){this._forEachChild(n=>this._registerControl(n))}_allControlsDisabled(){for(const n of this.controls)if(n.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(n){n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)}_find(n){return this.at(n)??null}}function qb(t){return!!t&&(void 0!==t.asyncValidators||void 0!==t.validators||void 0!==t.updateOn)}let $F=(()=>{class t{constructor(){this.useNonNullable=!1}get nonNullable(){const e=new t;return e.useNonNullable=!0,e}group(e,r=null){const i=this._reduceControls(e);let o={};return qb(r)?o=r:null!==r&&(o.validators=r.validator,o.asyncValidators=r.asyncValidator),new qo(i,o)}record(e,r=null){const i=this._reduceControls(e);return new Cb(i,r)}control(e,r,i){let o={};return this.useNonNullable?(qb(r)?o=r:(o.validators=r,o.asyncValidators=i),new Yo(e,{...o,nonNullable:!0})):new Yo(e,r,i)}array(e,r,i){const o=e.map(s=>this._createControl(s));return new Wb(o,r,i)}_reduceControls(e){const r={};return Object.keys(e).forEach(i=>{r[i]=this._createControl(e[i])}),r}_createControl(e){return e instanceof Yo||e instanceof fl?e:Array.isArray(e)?this.control(e[0],e.length>1?e[1]:null,e.length>2?e[2]:null):this.control(e)}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),GF=(()=>{class t{static withConfig(e){return{ngModule:t,providers:[{provide:wr,useValue:e.callSetDisabledState??Zo}]}}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({imports:[Gb]})}return t})(),WF=(()=>{class t{static withConfig(e){return{ngModule:t,providers:[{provide:Fh,useValue:e.warnOnNgModelWithFormControl??"always"},{provide:wr,useValue:e.callSetDisabledState??Zo}]}}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({imports:[Gb]})}return t})();function tr(t){return null!=t&&"false"!=`${t}`}function Mt(t){return t instanceof Xe?t.nativeElement:t}class tL extends $e{constructor(n,e){super()}schedule(n,e=0){return this}}const Cl={setInterval(t,n,...e){const{delegate:r}=Cl;return r?.setInterval?r.setInterval(t,n,...e):setInterval(t,n,...e)},clearInterval(t){const{delegate:n}=Cl;return(n?.clearInterval||clearInterval)(t)},delegate:void 0};class Wh extends tL{constructor(n,e){super(n,e),this.scheduler=n,this.work=e,this.pending=!1}schedule(n,e=0){var r;if(this.closed)return this;this.state=n;const i=this.id,o=this.scheduler;return null!=i&&(this.id=this.recycleAsyncId(o,i,e)),this.pending=!0,this.delay=e,this.id=null!==(r=this.id)&&void 0!==r?r:this.requestAsyncId(o,this.id,e),this}requestAsyncId(n,e,r=0){return Cl.setInterval(n.flush.bind(n,this),r)}recycleAsyncId(n,e,r=0){if(null!=r&&this.delay===r&&!1===this.pending)return e;null!=e&&Cl.clearInterval(e)}execute(n,e){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;const r=this._execute(n,e);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(n,e){let i,r=!1;try{this.work(n)}catch(o){r=!0,i=o||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),i}unsubscribe(){if(!this.closed){const{id:n,scheduler:e}=this,{actions:r}=e;this.work=this.state=this.scheduler=null,this.pending=!1,Sr(r,this),null!=n&&(this.id=this.recycleAsyncId(e,n,null)),this.delay=null,super.unsubscribe()}}}const Jo={schedule(t){let n=requestAnimationFrame,e=cancelAnimationFrame;const{delegate:r}=Jo;r&&(n=r.requestAnimationFrame,e=r.cancelAnimationFrame);const i=n(o=>{e=void 0,t(o)});return new $e(()=>e?.(i))},requestAnimationFrame(...t){const{delegate:n}=Jo;return(n?.requestAnimationFrame||requestAnimationFrame)(...t)},cancelAnimationFrame(...t){const{delegate:n}=Jo;return(n?.cancelAnimationFrame||cancelAnimationFrame)(...t)},delegate:void 0};class es{constructor(n,e=es.now){this.schedulerActionCtor=n,this.now=e}schedule(n,e=0,r){return new this.schedulerActionCtor(this,n).schedule(r,e)}}es.now=gh.now;class qh extends es{constructor(n,e=es.now){super(n,e),this.actions=[],this._active=!1}flush(n){const{actions:e}=this;if(this._active)return void e.push(n);let r;this._active=!0;do{if(r=n.execute(n.state,n.delay))break}while(n=e.shift());if(this._active=!1,r){for(;n=e.shift();)n.unsubscribe();throw r}}}const Qb=new class rL extends qh{flush(n){this._active=!0;const e=this._scheduled;this._scheduled=void 0;const{actions:r}=this;let i;n=n||r.shift();do{if(i=n.execute(n.state,n.delay))break}while((n=r[0])&&n.id===e&&r.shift());if(this._active=!1,i){for(;(n=r[0])&&n.id===e&&r.shift();)n.unsubscribe();throw i}}}(class nL extends Wh{constructor(n,e){super(n,e),this.scheduler=n,this.work=e}requestAsyncId(n,e,r=0){return null!==r&&r>0?super.requestAsyncId(n,e,r):(n.actions.push(this),n._scheduled||(n._scheduled=Jo.requestAnimationFrame(()=>n.flush(void 0))))}recycleAsyncId(n,e,r=0){var i;if(null!=r?r>0:this.delay>0)return super.recycleAsyncId(n,e,r);const{actions:o}=n;null!=e&&(null===(i=o[o.length-1])||void 0===i?void 0:i.id)!==e&&(Jo.cancelAnimationFrame(e),n._scheduled=void 0)}});let Zh,iL=1;const Dl={};function Yb(t){return t in Dl&&(delete Dl[t],!0)}const oL={setImmediate(t){const n=iL++;return Dl[n]=!0,Zh||(Zh=Promise.resolve()),Zh.then(()=>Yb(n)&&t()),n},clearImmediate(t){Yb(t)}},{setImmediate:sL,clearImmediate:aL}=oL,bl={setImmediate(...t){const{delegate:n}=bl;return(n?.setImmediate||sL)(...t)},clearImmediate(t){const{delegate:n}=bl;return(n?.clearImmediate||aL)(t)},delegate:void 0},wl=(new class cL extends qh{flush(n){this._active=!0;const e=this._scheduled;this._scheduled=void 0;const{actions:r}=this;let i;n=n||r.shift();do{if(i=n.execute(n.state,n.delay))break}while((n=r[0])&&n.id===e&&r.shift());if(this._active=!1,i){for(;(n=r[0])&&n.id===e&&r.shift();)n.unsubscribe();throw i}}}(class lL extends Wh{constructor(n,e){super(n,e),this.scheduler=n,this.work=e}requestAsyncId(n,e,r=0){return null!==r&&r>0?super.requestAsyncId(n,e,r):(n.actions.push(this),n._scheduled||(n._scheduled=bl.setImmediate(n.flush.bind(n,void 0))))}recycleAsyncId(n,e,r=0){var i;if(null!=r?r>0:this.delay>0)return super.recycleAsyncId(n,e,r);const{actions:o}=n;null!=e&&(null===(i=o[o.length-1])||void 0===i?void 0:i.id)!==e&&(bl.clearImmediate(e),n._scheduled===e&&(n._scheduled=void 0))}}),new qh(Wh)),dL=wl;function Xb(t=0,n,e=dL){let r=-1;return null!=n&&(Pf(n)?e=n:r=n),new ve(i=>{let o=function fL(t){return t instanceof Date&&!isNaN(t)}(t)?+t-e.now():t;o<0&&(o=0);let s=0;return e.schedule(function(){i.closed||(i.next(s++),0<=r?this.schedule(void 0,r):i.complete())},o)})}let Kh;try{Kh=typeof Intl<"u"&&Intl.v8BreakIterator}catch{Kh=!1}let ts,Qh,ew=(()=>{class t{constructor(e){this._platformId=e,this.isBrowser=this._platformId?BC(this._platformId):"object"==typeof document&&!!document,this.EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent),this.TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent),this.BLINK=this.isBrowser&&!(!window.chrome&&!Kh)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT,this.WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT,this.IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window),this.FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent),this.ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT,this.SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT}static#e=this.\u0275fac=function(r){return new(r||t)(j(Mn))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function ns(t){return function pL(){if(null==ts&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>ts=!0}))}finally{ts=ts||!1}return ts}()?t:!!t.capture}function nw(t){if(function gL(){if(null==Qh){const t=typeof document<"u"?document.head:null;Qh=!(!t||!t.createShadowRoot&&!t.attachShadow)}return Qh}()){const n=t.getRootNode?t.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&n instanceof ShadowRoot)return n}return null}function Ml(t){return t.composedPath?t.composedPath()[0]:t.target}const mL=new P("cdk-dir-doc",{providedIn:"root",factory:function _L(){return Y(Et)}}),vL=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;let rw=(()=>{class t{constructor(e){this.value="ltr",this.change=new B,e&&(this.value=function yL(t){const n=t?.toLowerCase()||"";return"auto"===n&&typeof navigator<"u"&&navigator?.language?vL.test(navigator.language)?"rtl":"ltr":"rtl"===n?"rtl":"ltr"}((e.body?e.body.dir:null)||(e.documentElement?e.documentElement.dir:null)||"ltr"))}ngOnDestroy(){this.change.complete()}static#e=this.\u0275fac=function(r){return new(r||t)(j(mL,8))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),iw=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({})}return t})(),wL=(()=>{class t{constructor(e,r,i){this._platform=e,this._change=new te,this._changeListener=o=>{this._change.next(o)},this._document=i,r.runOutsideAngular(()=>{if(e.isBrowser){const o=this._getWindow();o.addEventListener("resize",this._changeListener),o.addEventListener("orientationchange",this._changeListener)}this.change().subscribe(()=>this._viewportSize=null)})}ngOnDestroy(){if(this._platform.isBrowser){const e=this._getWindow();e.removeEventListener("resize",this._changeListener),e.removeEventListener("orientationchange",this._changeListener)}this._change.complete()}getViewportSize(){this._viewportSize||this._updateViewportSize();const e={width:this._viewportSize.width,height:this._viewportSize.height};return this._platform.isBrowser||(this._viewportSize=null),e}getViewportRect(){const e=this.getViewportScrollPosition(),{width:r,height:i}=this.getViewportSize();return{top:e.top,left:e.left,bottom:e.top+i,right:e.left+r,height:i,width:r}}getViewportScrollPosition(){if(!this._platform.isBrowser)return{top:0,left:0};const e=this._document,r=this._getWindow(),i=e.documentElement,o=i.getBoundingClientRect();return{top:-o.top||e.body.scrollTop||r.scrollY||i.scrollTop||0,left:-o.left||e.body.scrollLeft||r.scrollX||i.scrollLeft||0}}change(e=20){return e>0?this._change.pipe(function Jb(t,n=wl){return function hL(t){return Je((n,e)=>{let r=!1,i=null,o=null,s=!1;const a=()=>{if(o?.unsubscribe(),o=null,r){r=!1;const c=i;i=null,e.next(c)}s&&e.complete()},l=()=>{o=null,s&&e.complete()};n.subscribe(Ge(e,c=>{r=!0,i=c,o||st(t(c)).subscribe(o=Ge(e,a,l))},()=>{s=!0,(!r||!o||o.closed)&&e.complete()}))})}(()=>Xb(t,n))}(e)):this._change}_getWindow(){return this._document.defaultView||window}_updateViewportSize(){const e=this._getWindow();this._viewportSize=this._platform.isBrowser?{width:e.innerWidth,height:e.innerHeight}:{width:0,height:0}}static#e=this.\u0275fac=function(r){return new(r||t)(j(ew),j(U),j(Et,8))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Yh=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({})}return t})(),EL=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({imports:[iw,Yh,iw,Yh]})}return t})();function Xh(...t){const n=Ni(t);return Je((e,r)=>{(n?il(t,e,n):il(t,e)).subscribe(r)})}function Jh(t,n,e){for(let r in n)if(n.hasOwnProperty(r)){const i=n[r];i?t.setProperty(r,i,e?.has(r)?"important":""):t.removeProperty(r)}return t}function ki(t,n){const e=n?"":"none";Jh(t.style,{"touch-action":n?"":"none","-webkit-user-drag":n?"":"none","-webkit-tap-highlight-color":n?"":"transparent","user-select":e,"-ms-user-select":e,"-webkit-user-select":e,"-moz-user-select":e})}function lw(t,n,e){Jh(t.style,{position:n?"":"fixed",top:n?"":"0",opacity:n?"":"0",left:n?"":"-999em"},e)}function Il(t,n){return n&&"none"!=n?t+" "+n:t}function cw(t){const n=t.toLowerCase().indexOf("ms")>-1?1:1e3;return parseFloat(t)*n}function ef(t,n){return t.getPropertyValue(n).split(",").map(r=>r.trim())}function tf(t){const n=t.getBoundingClientRect();return{top:n.top,right:n.right,bottom:n.bottom,left:n.left,width:n.width,height:n.height,x:n.x,y:n.y}}function nf(t,n,e){const{top:r,bottom:i,left:o,right:s}=t;return e>=r&&e<=i&&n>=o&&n<=s}function rs(t,n,e){t.top+=n,t.bottom=t.top+t.height,t.left+=e,t.right=t.left+t.width}function uw(t,n,e,r){const{top:i,right:o,bottom:s,left:a,width:l,height:c}=t,u=l*n,d=c*n;return r>i-d&&r<s+d&&e>a-u&&e<o+u}class dw{constructor(n){this._document=n,this.positions=new Map}clear(){this.positions.clear()}cache(n){this.clear(),this.positions.set(this._document,{scrollPosition:this.getViewportScrollPosition()}),n.forEach(e=>{this.positions.set(e,{scrollPosition:{top:e.scrollTop,left:e.scrollLeft},clientRect:tf(e)})})}handleScroll(n){const e=Ml(n),r=this.positions.get(e);if(!r)return null;const i=r.scrollPosition;let o,s;if(e===this._document){const c=this.getViewportScrollPosition();o=c.top,s=c.left}else o=e.scrollTop,s=e.scrollLeft;const a=i.top-o,l=i.left-s;return this.positions.forEach((c,u)=>{c.clientRect&&e!==u&&e.contains(u)&&rs(c.clientRect,a,l)}),i.top=o,i.left=s,{top:a,left:l}}getViewportScrollPosition(){return{top:window.scrollY,left:window.scrollX}}}function hw(t){const n=t.cloneNode(!0),e=n.querySelectorAll("[id]"),r=t.nodeName.toLowerCase();n.removeAttribute("id");for(let i=0;i<e.length;i++)e[i].removeAttribute("id");return"canvas"===r?gw(t,n):("input"===r||"select"===r||"textarea"===r)&&pw(t,n),fw("canvas",t,n,gw),fw("input, textarea, select",t,n,pw),n}function fw(t,n,e,r){const i=n.querySelectorAll(t);if(i.length){const o=e.querySelectorAll(t);for(let s=0;s<i.length;s++)r(i[s],o[s])}}let UL=0;function pw(t,n){"file"!==n.type&&(n.value=t.value),"radio"===n.type&&n.name&&(n.name=`mat-clone-${n.name}-${UL++}`)}function gw(t,n){const e=n.getContext("2d");if(e)try{e.drawImage(t,0,0)}catch{}}const mw=ns({passive:!0}),Sl=ns({passive:!1}),rf=new Set(["position"]);class GL{get disabled(){return this._disabled||!(!this._dropContainer||!this._dropContainer.disabled)}set disabled(n){const e=tr(n);e!==this._disabled&&(this._disabled=e,this._toggleNativeDragInteractions(),this._handles.forEach(r=>ki(r,e)))}constructor(n,e,r,i,o,s){this._config=e,this._document=r,this._ngZone=i,this._viewportRuler=o,this._dragDropRegistry=s,this._passiveTransform={x:0,y:0},this._activeTransform={x:0,y:0},this._hasStartedDragging=!1,this._moveEvents=new te,this._pointerMoveSubscription=$e.EMPTY,this._pointerUpSubscription=$e.EMPTY,this._scrollSubscription=$e.EMPTY,this._resizeSubscription=$e.EMPTY,this._boundaryElement=null,this._nativeInteractionsEnabled=!0,this._handles=[],this._disabledHandles=new Set,this._direction="ltr",this.dragStartDelay=0,this._disabled=!1,this.beforeStarted=new te,this.started=new te,this.released=new te,this.ended=new te,this.entered=new te,this.exited=new te,this.dropped=new te,this.moved=this._moveEvents,this._pointerDown=a=>{if(this.beforeStarted.next(),this._handles.length){const l=this._getTargetHandle(a);l&&!this._disabledHandles.has(l)&&!this.disabled&&this._initializeDragSequence(l,a)}else this.disabled||this._initializeDragSequence(this._rootElement,a)},this._pointerMove=a=>{const l=this._getPointerPositionOnPage(a);if(!this._hasStartedDragging){if(Math.abs(l.x-this._pickupPositionOnPage.x)+Math.abs(l.y-this._pickupPositionOnPage.y)>=this._config.dragStartThreshold){const f=Date.now()>=this._dragStartTime+this._getDragStartDelay(a),g=this._dropContainer;if(!f)return void this._endDragSequence(a);(!g||!g.isDragging()&&!g.isReceiving())&&(a.preventDefault(),this._hasStartedDragging=!0,this._ngZone.run(()=>this._startDragSequence(a)))}return}a.preventDefault();const c=this._getConstrainedPointerPosition(l);if(this._hasMoved=!0,this._lastKnownPointerPosition=l,this._updatePointerDirectionDelta(c),this._dropContainer)this._updateActiveDropContainer(c,l);else{const u=this.constrainPosition?this._initialClientRect:this._pickupPositionOnPage,d=this._activeTransform;d.x=c.x-u.x+this._passiveTransform.x,d.y=c.y-u.y+this._passiveTransform.y,this._applyRootElementTransform(d.x,d.y)}this._moveEvents.observers.length&&this._ngZone.run(()=>{this._moveEvents.next({source:this,pointerPosition:c,event:a,distance:this._getDragDistance(c),delta:this._pointerDirectionDelta})})},this._pointerUp=a=>{this._endDragSequence(a)},this._nativeDragStart=a=>{if(this._handles.length){const l=this._getTargetHandle(a);l&&!this._disabledHandles.has(l)&&!this.disabled&&a.preventDefault()}else this.disabled||a.preventDefault()},this.withRootElement(n).withParent(e.parentDragRef||null),this._parentPositions=new dw(r),s.registerDragItem(this)}getPlaceholderElement(){return this._placeholder}getRootElement(){return this._rootElement}getVisibleElement(){return this.isDragging()?this.getPlaceholderElement():this.getRootElement()}withHandles(n){this._handles=n.map(r=>Mt(r)),this._handles.forEach(r=>ki(r,this.disabled)),this._toggleNativeDragInteractions();const e=new Set;return this._disabledHandles.forEach(r=>{this._handles.indexOf(r)>-1&&e.add(r)}),this._disabledHandles=e,this}withPreviewTemplate(n){return this._previewTemplate=n,this}withPlaceholderTemplate(n){return this._placeholderTemplate=n,this}withRootElement(n){const e=Mt(n);return e!==this._rootElement&&(this._rootElement&&this._removeRootElementListeners(this._rootElement),this._ngZone.runOutsideAngular(()=>{e.addEventListener("mousedown",this._pointerDown,Sl),e.addEventListener("touchstart",this._pointerDown,mw),e.addEventListener("dragstart",this._nativeDragStart,Sl)}),this._initialTransform=void 0,this._rootElement=e),typeof SVGElement<"u"&&this._rootElement instanceof SVGElement&&(this._ownerSVGElement=this._rootElement.ownerSVGElement),this}withBoundaryElement(n){return this._boundaryElement=n?Mt(n):null,this._resizeSubscription.unsubscribe(),n&&(this._resizeSubscription=this._viewportRuler.change(10).subscribe(()=>this._containInsideBoundaryOnResize())),this}withParent(n){return this._parentDragRef=n,this}dispose(){this._removeRootElementListeners(this._rootElement),this.isDragging()&&this._rootElement?.remove(),this._anchor?.remove(),this._destroyPreview(),this._destroyPlaceholder(),this._dragDropRegistry.removeDragItem(this),this._removeSubscriptions(),this.beforeStarted.complete(),this.started.complete(),this.released.complete(),this.ended.complete(),this.entered.complete(),this.exited.complete(),this.dropped.complete(),this._moveEvents.complete(),this._handles=[],this._disabledHandles.clear(),this._dropContainer=void 0,this._resizeSubscription.unsubscribe(),this._parentPositions.clear(),this._boundaryElement=this._rootElement=this._ownerSVGElement=this._placeholderTemplate=this._previewTemplate=this._anchor=this._parentDragRef=null}isDragging(){return this._hasStartedDragging&&this._dragDropRegistry.isDragging(this)}reset(){this._rootElement.style.transform=this._initialTransform||"",this._activeTransform={x:0,y:0},this._passiveTransform={x:0,y:0}}disableHandle(n){!this._disabledHandles.has(n)&&this._handles.indexOf(n)>-1&&(this._disabledHandles.add(n),ki(n,!0))}enableHandle(n){this._disabledHandles.has(n)&&(this._disabledHandles.delete(n),ki(n,this.disabled))}withDirection(n){return this._direction=n,this}_withDropContainer(n){this._dropContainer=n}getFreeDragPosition(){const n=this.isDragging()?this._activeTransform:this._passiveTransform;return{x:n.x,y:n.y}}setFreeDragPosition(n){return this._activeTransform={x:0,y:0},this._passiveTransform.x=n.x,this._passiveTransform.y=n.y,this._dropContainer||this._applyRootElementTransform(n.x,n.y),this}withPreviewContainer(n){return this._previewContainer=n,this}_sortFromLastPointerPosition(){const n=this._lastKnownPointerPosition;n&&this._dropContainer&&this._updateActiveDropContainer(this._getConstrainedPointerPosition(n),n)}_removeSubscriptions(){this._pointerMoveSubscription.unsubscribe(),this._pointerUpSubscription.unsubscribe(),this._scrollSubscription.unsubscribe()}_destroyPreview(){this._preview?.remove(),this._previewRef?.destroy(),this._preview=this._previewRef=null}_destroyPlaceholder(){this._placeholder?.remove(),this._placeholderRef?.destroy(),this._placeholder=this._placeholderRef=null}_endDragSequence(n){if(this._dragDropRegistry.isDragging(this)&&(this._removeSubscriptions(),this._dragDropRegistry.stopDragging(this),this._toggleNativeDragInteractions(),this._handles&&(this._rootElement.style.webkitTapHighlightColor=this._rootElementTapHighlight),this._hasStartedDragging))if(this.released.next({source:this,event:n}),this._dropContainer)this._dropContainer._stopScrolling(),this._animatePreviewToPlaceholder().then(()=>{this._cleanupDragArtifacts(n),this._cleanupCachedDimensions(),this._dragDropRegistry.stopDragging(this)});else{this._passiveTransform.x=this._activeTransform.x;const e=this._getPointerPositionOnPage(n);this._passiveTransform.y=this._activeTransform.y,this._ngZone.run(()=>{this.ended.next({source:this,distance:this._getDragDistance(e),dropPoint:e,event:n})}),this._cleanupCachedDimensions(),this._dragDropRegistry.stopDragging(this)}}_startDragSequence(n){is(n)&&(this._lastTouchEventTime=Date.now()),this._toggleNativeDragInteractions();const e=this._dropContainer;if(e){const r=this._rootElement,i=r.parentNode,o=this._placeholder=this._createPlaceholderElement(),s=this._anchor=this._anchor||this._document.createComment(""),a=this._getShadowRoot();i.insertBefore(s,r),this._initialTransform=r.style.transform||"",this._preview=this._createPreviewElement(),lw(r,!1,rf),this._document.body.appendChild(i.replaceChild(o,r)),this._getPreviewInsertionPoint(i,a).appendChild(this._preview),this.started.next({source:this,event:n}),e.start(),this._initialContainer=e,this._initialIndex=e.getItemIndex(this)}else this.started.next({source:this,event:n}),this._initialContainer=this._initialIndex=void 0;this._parentPositions.cache(e?e.getScrollableParents():[])}_initializeDragSequence(n,e){this._parentDragRef&&e.stopPropagation();const r=this.isDragging(),i=is(e),o=!i&&0!==e.button,s=this._rootElement,a=Ml(e),l=!i&&this._lastTouchEventTime&&this._lastTouchEventTime+800>Date.now(),c=i?function VL(t){const n=t.touches&&t.touches[0]||t.changedTouches&&t.changedTouches[0];return!(!n||-1!==n.identifier||null!=n.radiusX&&1!==n.radiusX||null!=n.radiusY&&1!==n.radiusY)}(e):function LL(t){return 0===t.buttons||0===t.detail}(e);if(a&&a.draggable&&"mousedown"===e.type&&e.preventDefault(),r||o||l||c)return;if(this._handles.length){const h=s.style;this._rootElementTapHighlight=h.webkitTapHighlightColor||"",h.webkitTapHighlightColor="transparent"}this._hasStartedDragging=this._hasMoved=!1,this._removeSubscriptions(),this._initialClientRect=this._rootElement.getBoundingClientRect(),this._pointerMoveSubscription=this._dragDropRegistry.pointerMove.subscribe(this._pointerMove),this._pointerUpSubscription=this._dragDropRegistry.pointerUp.subscribe(this._pointerUp),this._scrollSubscription=this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(h=>this._updateOnScroll(h)),this._boundaryElement&&(this._boundaryRect=tf(this._boundaryElement));const u=this._previewTemplate;this._pickupPositionInElement=u&&u.template&&!u.matchSize?{x:0,y:0}:this._getPointerPositionInElement(this._initialClientRect,n,e);const d=this._pickupPositionOnPage=this._lastKnownPointerPosition=this._getPointerPositionOnPage(e);this._pointerDirectionDelta={x:0,y:0},this._pointerPositionAtLastDirectionChange={x:d.x,y:d.y},this._dragStartTime=Date.now(),this._dragDropRegistry.startDragging(this,e)}_cleanupDragArtifacts(n){lw(this._rootElement,!0,rf),this._anchor.parentNode.replaceChild(this._rootElement,this._anchor),this._destroyPreview(),this._destroyPlaceholder(),this._initialClientRect=this._boundaryRect=this._previewRect=this._initialTransform=void 0,this._ngZone.run(()=>{const e=this._dropContainer,r=e.getItemIndex(this),i=this._getPointerPositionOnPage(n),o=this._getDragDistance(i),s=e._isOverContainer(i.x,i.y);this.ended.next({source:this,distance:o,dropPoint:i,event:n}),this.dropped.next({item:this,currentIndex:r,previousIndex:this._initialIndex,container:e,previousContainer:this._initialContainer,isPointerOverContainer:s,distance:o,dropPoint:i,event:n}),e.drop(this,r,this._initialIndex,this._initialContainer,s,o,i,n),this._dropContainer=this._initialContainer})}_updateActiveDropContainer({x:n,y:e},{x:r,y:i}){let o=this._initialContainer._getSiblingContainerFromPosition(this,n,e);!o&&this._dropContainer!==this._initialContainer&&this._initialContainer._isOverContainer(n,e)&&(o=this._initialContainer),o&&o!==this._dropContainer&&this._ngZone.run(()=>{this.exited.next({item:this,container:this._dropContainer}),this._dropContainer.exit(this),this._dropContainer=o,this._dropContainer.enter(this,n,e,o===this._initialContainer&&o.sortingDisabled?this._initialIndex:void 0),this.entered.next({item:this,container:o,currentIndex:o.getItemIndex(this)})}),this.isDragging()&&(this._dropContainer._startScrollingIfNecessary(r,i),this._dropContainer._sortItem(this,n,e,this._pointerDirectionDelta),this.constrainPosition?this._applyPreviewTransform(n,e):this._applyPreviewTransform(n-this._pickupPositionInElement.x,e-this._pickupPositionInElement.y))}_createPreviewElement(){const n=this._previewTemplate,e=this.previewClass,r=n?n.template:null;let i;if(r&&n){const o=n.matchSize?this._initialClientRect:null,s=n.viewContainer.createEmbeddedView(r,n.context);s.detectChanges(),i=vw(s,this._document),this._previewRef=s,n.matchSize?yw(i,o):i.style.transform=Ol(this._pickupPositionOnPage.x,this._pickupPositionOnPage.y)}else i=hw(this._rootElement),yw(i,this._initialClientRect),this._initialTransform&&(i.style.transform=this._initialTransform);return Jh(i.style,{"pointer-events":"none",margin:"0",position:"fixed",top:"0",left:"0","z-index":`${this._config.zIndex||1e3}`},rf),ki(i,!1),i.classList.add("cdk-drag-preview"),i.setAttribute("dir",this._direction),e&&(Array.isArray(e)?e.forEach(o=>i.classList.add(o)):i.classList.add(e)),i}_animatePreviewToPlaceholder(){if(!this._hasMoved)return Promise.resolve();const n=this._placeholder.getBoundingClientRect();this._preview.classList.add("cdk-drag-animating"),this._applyPreviewTransform(n.left,n.top);const e=function zL(t){const n=getComputedStyle(t),e=ef(n,"transition-property"),r=e.find(a=>"transform"===a||"all"===a);if(!r)return 0;const i=e.indexOf(r),o=ef(n,"transition-duration"),s=ef(n,"transition-delay");return cw(o[i])+cw(s[i])}(this._preview);return 0===e?Promise.resolve():this._ngZone.runOutsideAngular(()=>new Promise(r=>{const i=s=>{(!s||Ml(s)===this._preview&&"transform"===s.propertyName)&&(this._preview?.removeEventListener("transitionend",i),r(),clearTimeout(o))},o=setTimeout(i,1.5*e);this._preview.addEventListener("transitionend",i)}))}_createPlaceholderElement(){const n=this._placeholderTemplate,e=n?n.template:null;let r;return e?(this._placeholderRef=n.viewContainer.createEmbeddedView(e,n.context),this._placeholderRef.detectChanges(),r=vw(this._placeholderRef,this._document)):r=hw(this._rootElement),r.style.pointerEvents="none",r.classList.add("cdk-drag-placeholder"),r}_getPointerPositionInElement(n,e,r){const i=e===this._rootElement?null:e,o=i?i.getBoundingClientRect():n,s=is(r)?r.targetTouches[0]:r,a=this._getViewportScrollPosition();return{x:o.left-n.left+(s.pageX-o.left-a.left),y:o.top-n.top+(s.pageY-o.top-a.top)}}_getPointerPositionOnPage(n){const e=this._getViewportScrollPosition(),r=is(n)?n.touches[0]||n.changedTouches[0]||{pageX:0,pageY:0}:n,i=r.pageX-e.left,o=r.pageY-e.top;if(this._ownerSVGElement){const s=this._ownerSVGElement.getScreenCTM();if(s){const a=this._ownerSVGElement.createSVGPoint();return a.x=i,a.y=o,a.matrixTransform(s.inverse())}}return{x:i,y:o}}_getConstrainedPointerPosition(n){const e=this._dropContainer?this._dropContainer.lockAxis:null;let{x:r,y:i}=this.constrainPosition?this.constrainPosition(n,this,this._initialClientRect,this._pickupPositionInElement):n;if("x"===this.lockAxis||"x"===e?i=this._pickupPositionOnPage.y-(this.constrainPosition?this._pickupPositionInElement.y:0):("y"===this.lockAxis||"y"===e)&&(r=this._pickupPositionOnPage.x-(this.constrainPosition?this._pickupPositionInElement.x:0)),this._boundaryRect){const{x:o,y:s}=this.constrainPosition?{x:0,y:0}:this._pickupPositionInElement,a=this._boundaryRect,{width:l,height:c}=this._getPreviewRect(),u=a.top+s,d=a.bottom-(c-s);r=_w(r,a.left+o,a.right-(l-o)),i=_w(i,u,d)}return{x:r,y:i}}_updatePointerDirectionDelta(n){const{x:e,y:r}=n,i=this._pointerDirectionDelta,o=this._pointerPositionAtLastDirectionChange,s=Math.abs(e-o.x),a=Math.abs(r-o.y);return s>this._config.pointerDirectionChangeThreshold&&(i.x=e>o.x?1:-1,o.x=e),a>this._config.pointerDirectionChangeThreshold&&(i.y=r>o.y?1:-1,o.y=r),i}_toggleNativeDragInteractions(){if(!this._rootElement||!this._handles)return;const n=this._handles.length>0||!this.isDragging();n!==this._nativeInteractionsEnabled&&(this._nativeInteractionsEnabled=n,ki(this._rootElement,n))}_removeRootElementListeners(n){n.removeEventListener("mousedown",this._pointerDown,Sl),n.removeEventListener("touchstart",this._pointerDown,mw),n.removeEventListener("dragstart",this._nativeDragStart,Sl)}_applyRootElementTransform(n,e){const r=Ol(n,e),i=this._rootElement.style;null==this._initialTransform&&(this._initialTransform=i.transform&&"none"!=i.transform?i.transform:""),i.transform=Il(r,this._initialTransform)}_applyPreviewTransform(n,e){const r=this._previewTemplate?.template?void 0:this._initialTransform,i=Ol(n,e);this._preview.style.transform=Il(i,r)}_getDragDistance(n){const e=this._pickupPositionOnPage;return e?{x:n.x-e.x,y:n.y-e.y}:{x:0,y:0}}_cleanupCachedDimensions(){this._boundaryRect=this._previewRect=void 0,this._parentPositions.clear()}_containInsideBoundaryOnResize(){let{x:n,y:e}=this._passiveTransform;if(0===n&&0===e||this.isDragging()||!this._boundaryElement)return;const r=this._rootElement.getBoundingClientRect(),i=this._boundaryElement.getBoundingClientRect();if(0===i.width&&0===i.height||0===r.width&&0===r.height)return;const o=i.left-r.left,s=r.right-i.right,a=i.top-r.top,l=r.bottom-i.bottom;i.width>r.width?(o>0&&(n+=o),s>0&&(n-=s)):n=0,i.height>r.height?(a>0&&(e+=a),l>0&&(e-=l)):e=0,(n!==this._passiveTransform.x||e!==this._passiveTransform.y)&&this.setFreeDragPosition({y:e,x:n})}_getDragStartDelay(n){const e=this.dragStartDelay;return"number"==typeof e?e:is(n)?e.touch:e?e.mouse:0}_updateOnScroll(n){const e=this._parentPositions.handleScroll(n);if(e){const r=Ml(n);this._boundaryRect&&r!==this._boundaryElement&&r.contains(this._boundaryElement)&&rs(this._boundaryRect,e.top,e.left),this._pickupPositionOnPage.x+=e.left,this._pickupPositionOnPage.y+=e.top,this._dropContainer||(this._activeTransform.x-=e.left,this._activeTransform.y-=e.top,this._applyRootElementTransform(this._activeTransform.x,this._activeTransform.y))}}_getViewportScrollPosition(){return this._parentPositions.positions.get(this._document)?.scrollPosition||this._parentPositions.getViewportScrollPosition()}_getShadowRoot(){return void 0===this._cachedShadowRoot&&(this._cachedShadowRoot=nw(this._rootElement)),this._cachedShadowRoot}_getPreviewInsertionPoint(n,e){const r=this._previewContainer||"global";if("parent"===r)return n;if("global"===r){const i=this._document;return e||i.fullscreenElement||i.webkitFullscreenElement||i.mozFullScreenElement||i.msFullscreenElement||i.body}return Mt(r)}_getPreviewRect(){return(!this._previewRect||!this._previewRect.width&&!this._previewRect.height)&&(this._previewRect=this._preview?this._preview.getBoundingClientRect():this._initialClientRect),this._previewRect}_getTargetHandle(n){return this._handles.find(e=>n.target&&(n.target===e||e.contains(n.target)))}}function Ol(t,n){return`translate3d(${Math.round(t)}px, ${Math.round(n)}px, 0)`}function _w(t,n,e){return Math.max(n,Math.min(e,t))}function is(t){return"t"===t.type[0]}function vw(t,n){const e=t.rootNodes;if(1===e.length&&e[0].nodeType===n.ELEMENT_NODE)return e[0];const r=n.createElement("div");return e.forEach(i=>r.appendChild(i)),r}function yw(t,n){t.style.width=`${n.width}px`,t.style.height=`${n.height}px`,t.style.transform=Ol(n.left,n.top)}function os(t,n){return Math.max(0,Math.min(n,t))}class qL{constructor(n,e){this._element=n,this._dragDropRegistry=e,this._itemPositions=[],this.orientation="vertical",this._previousSwap={drag:null,delta:0,overlaps:!1}}start(n){this.withItems(n)}sort(n,e,r,i){const o=this._itemPositions,s=this._getItemIndexFromPointerPosition(n,e,r,i);if(-1===s&&o.length>0)return null;const a="horizontal"===this.orientation,l=o.findIndex(D=>D.drag===n),c=o[s],d=c.clientRect,h=l>s?1:-1,f=this._getItemOffsetPx(o[l].clientRect,d,h),g=this._getSiblingOffsetPx(l,o,h),v=o.slice();return function WL(t,n,e){const r=os(n,t.length-1),i=os(e,t.length-1);if(r===i)return;const o=t[r],s=i<r?-1:1;for(let a=r;a!==i;a+=s)t[a]=t[a+s];t[i]=o}(o,l,s),o.forEach((D,I)=>{if(v[I]===D)return;const w=D.drag===n,N=w?f:g,z=w?n.getPlaceholderElement():D.drag.getRootElement();D.offset+=N,a?(z.style.transform=Il(`translate3d(${Math.round(D.offset)}px, 0, 0)`,D.initialTransform),rs(D.clientRect,0,N)):(z.style.transform=Il(`translate3d(0, ${Math.round(D.offset)}px, 0)`,D.initialTransform),rs(D.clientRect,N,0))}),this._previousSwap.overlaps=nf(d,e,r),this._previousSwap.drag=c.drag,this._previousSwap.delta=a?i.x:i.y,{previousIndex:l,currentIndex:s}}enter(n,e,r,i){const o=null==i||i<0?this._getItemIndexFromPointerPosition(n,e,r):i,s=this._activeDraggables,a=s.indexOf(n),l=n.getPlaceholderElement();let c=s[o];if(c===n&&(c=s[o+1]),!c&&(null==o||-1===o||o<s.length-1)&&this._shouldEnterAsFirstChild(e,r)&&(c=s[0]),a>-1&&s.splice(a,1),c&&!this._dragDropRegistry.isDragging(c)){const u=c.getRootElement();u.parentElement.insertBefore(l,u),s.splice(o,0,n)}else Mt(this._element).appendChild(l),s.push(n);l.style.transform="",this._cacheItemPositions()}withItems(n){this._activeDraggables=n.slice(),this._cacheItemPositions()}withSortPredicate(n){this._sortPredicate=n}reset(){this._activeDraggables.forEach(n=>{const e=n.getRootElement();if(e){const r=this._itemPositions.find(i=>i.drag===n)?.initialTransform;e.style.transform=r||""}}),this._itemPositions=[],this._activeDraggables=[],this._previousSwap.drag=null,this._previousSwap.delta=0,this._previousSwap.overlaps=!1}getActiveItemsSnapshot(){return this._activeDraggables}getItemIndex(n){return("horizontal"===this.orientation&&"rtl"===this.direction?this._itemPositions.slice().reverse():this._itemPositions).findIndex(r=>r.drag===n)}updateOnScroll(n,e){this._itemPositions.forEach(({clientRect:r})=>{rs(r,n,e)}),this._itemPositions.forEach(({drag:r})=>{this._dragDropRegistry.isDragging(r)&&r._sortFromLastPointerPosition()})}_cacheItemPositions(){const n="horizontal"===this.orientation;this._itemPositions=this._activeDraggables.map(e=>{const r=e.getVisibleElement();return{drag:e,offset:0,initialTransform:r.style.transform||"",clientRect:tf(r)}}).sort((e,r)=>n?e.clientRect.left-r.clientRect.left:e.clientRect.top-r.clientRect.top)}_getItemOffsetPx(n,e,r){const i="horizontal"===this.orientation;let o=i?e.left-n.left:e.top-n.top;return-1===r&&(o+=i?e.width-n.width:e.height-n.height),o}_getSiblingOffsetPx(n,e,r){const i="horizontal"===this.orientation,o=e[n].clientRect,s=e[n+-1*r];let a=o[i?"width":"height"]*r;if(s){const l=i?"left":"top",c=i?"right":"bottom";-1===r?a-=s.clientRect[l]-o[c]:a+=o[l]-s.clientRect[c]}return a}_shouldEnterAsFirstChild(n,e){if(!this._activeDraggables.length)return!1;const r=this._itemPositions,i="horizontal"===this.orientation;if(r[0].drag!==this._activeDraggables[0]){const s=r[r.length-1].clientRect;return i?n>=s.right:e>=s.bottom}{const s=r[0].clientRect;return i?n<=s.left:e<=s.top}}_getItemIndexFromPointerPosition(n,e,r,i){const o="horizontal"===this.orientation,s=this._itemPositions.findIndex(({drag:a,clientRect:l})=>a!==n&&((!i||a!==this._previousSwap.drag||!this._previousSwap.overlaps||(o?i.x:i.y)!==this._previousSwap.delta)&&(o?e>=Math.floor(l.left)&&e<Math.floor(l.right):r>=Math.floor(l.top)&&r<Math.floor(l.bottom))));return-1!==s&&this._sortPredicate(s,n)?s:-1}}class ZL{constructor(n,e,r,i,o){this._dragDropRegistry=e,this._ngZone=i,this._viewportRuler=o,this.disabled=!1,this.sortingDisabled=!1,this.autoScrollDisabled=!1,this.autoScrollStep=2,this.enterPredicate=()=>!0,this.sortPredicate=()=>!0,this.beforeStarted=new te,this.entered=new te,this.exited=new te,this.dropped=new te,this.sorted=new te,this.receivingStarted=new te,this.receivingStopped=new te,this._isDragging=!1,this._draggables=[],this._siblings=[],this._activeSiblings=new Set,this._viewportScrollSubscription=$e.EMPTY,this._verticalScrollDirection=0,this._horizontalScrollDirection=0,this._stopScrollTimers=new te,this._cachedShadowRoot=null,this._startScrollInterval=()=>{this._stopScrolling(),function jL(t=0,n=wl){return t<0&&(t=0),Xb(t,t,n)}(0,Qb).pipe(Se(this._stopScrollTimers)).subscribe(()=>{const s=this._scrollNode,a=this.autoScrollStep;1===this._verticalScrollDirection?s.scrollBy(0,-a):2===this._verticalScrollDirection&&s.scrollBy(0,a),1===this._horizontalScrollDirection?s.scrollBy(-a,0):2===this._horizontalScrollDirection&&s.scrollBy(a,0)})},this.element=Mt(n),this._document=r,this.withScrollableParents([this.element]),e.registerDropContainer(this),this._parentPositions=new dw(r),this._sortStrategy=new qL(this.element,e),this._sortStrategy.withSortPredicate((s,a)=>this.sortPredicate(s,a,this))}dispose(){this._stopScrolling(),this._stopScrollTimers.complete(),this._viewportScrollSubscription.unsubscribe(),this.beforeStarted.complete(),this.entered.complete(),this.exited.complete(),this.dropped.complete(),this.sorted.complete(),this.receivingStarted.complete(),this.receivingStopped.complete(),this._activeSiblings.clear(),this._scrollNode=null,this._parentPositions.clear(),this._dragDropRegistry.removeDropContainer(this)}isDragging(){return this._isDragging}start(){this._draggingStarted(),this._notifyReceivingSiblings()}enter(n,e,r,i){this._draggingStarted(),null==i&&this.sortingDisabled&&(i=this._draggables.indexOf(n)),this._sortStrategy.enter(n,e,r,i),this._cacheParentPositions(),this._notifyReceivingSiblings(),this.entered.next({item:n,container:this,currentIndex:this.getItemIndex(n)})}exit(n){this._reset(),this.exited.next({item:n,container:this})}drop(n,e,r,i,o,s,a,l={}){this._reset(),this.dropped.next({item:n,currentIndex:e,previousIndex:r,container:this,previousContainer:i,isPointerOverContainer:o,distance:s,dropPoint:a,event:l})}withItems(n){const e=this._draggables;return this._draggables=n,n.forEach(r=>r._withDropContainer(this)),this.isDragging()&&(e.filter(i=>i.isDragging()).every(i=>-1===n.indexOf(i))?this._reset():this._sortStrategy.withItems(this._draggables)),this}withDirection(n){return this._sortStrategy.direction=n,this}connectedTo(n){return this._siblings=n.slice(),this}withOrientation(n){return this._sortStrategy.orientation=n,this}withScrollableParents(n){const e=Mt(this.element);return this._scrollableElements=-1===n.indexOf(e)?[e,...n]:n.slice(),this}getScrollableParents(){return this._scrollableElements}getItemIndex(n){return this._isDragging?this._sortStrategy.getItemIndex(n):this._draggables.indexOf(n)}isReceiving(){return this._activeSiblings.size>0}_sortItem(n,e,r,i){if(this.sortingDisabled||!this._clientRect||!uw(this._clientRect,.05,e,r))return;const o=this._sortStrategy.sort(n,e,r,i);o&&this.sorted.next({previousIndex:o.previousIndex,currentIndex:o.currentIndex,container:this,item:n})}_startScrollingIfNecessary(n,e){if(this.autoScrollDisabled)return;let r,i=0,o=0;if(this._parentPositions.positions.forEach((s,a)=>{a===this._document||!s.clientRect||r||uw(s.clientRect,.05,n,e)&&([i,o]=function KL(t,n,e,r){const i=bw(n,r),o=ww(n,e);let s=0,a=0;if(i){const l=t.scrollTop;1===i?l>0&&(s=1):t.scrollHeight-l>t.clientHeight&&(s=2)}if(o){const l=t.scrollLeft;1===o?l>0&&(a=1):t.scrollWidth-l>t.clientWidth&&(a=2)}return[s,a]}(a,s.clientRect,n,e),(i||o)&&(r=a))}),!i&&!o){const{width:s,height:a}=this._viewportRuler.getViewportSize(),l={width:s,height:a,top:0,right:s,bottom:a,left:0};i=bw(l,e),o=ww(l,n),r=window}r&&(i!==this._verticalScrollDirection||o!==this._horizontalScrollDirection||r!==this._scrollNode)&&(this._verticalScrollDirection=i,this._horizontalScrollDirection=o,this._scrollNode=r,(i||o)&&r?this._ngZone.runOutsideAngular(this._startScrollInterval):this._stopScrolling())}_stopScrolling(){this._stopScrollTimers.next()}_draggingStarted(){const n=Mt(this.element).style;this.beforeStarted.next(),this._isDragging=!0,this._initialScrollSnap=n.msScrollSnapType||n.scrollSnapType||"",n.scrollSnapType=n.msScrollSnapType="none",this._sortStrategy.start(this._draggables),this._cacheParentPositions(),this._viewportScrollSubscription.unsubscribe(),this._listenToScrollEvents()}_cacheParentPositions(){const n=Mt(this.element);this._parentPositions.cache(this._scrollableElements),this._clientRect=this._parentPositions.positions.get(n).clientRect}_reset(){this._isDragging=!1;const n=Mt(this.element).style;n.scrollSnapType=n.msScrollSnapType=this._initialScrollSnap,this._siblings.forEach(e=>e._stopReceiving(this)),this._sortStrategy.reset(),this._stopScrolling(),this._viewportScrollSubscription.unsubscribe(),this._parentPositions.clear()}_isOverContainer(n,e){return null!=this._clientRect&&nf(this._clientRect,n,e)}_getSiblingContainerFromPosition(n,e,r){return this._siblings.find(i=>i._canReceive(n,e,r))}_canReceive(n,e,r){if(!this._clientRect||!nf(this._clientRect,e,r)||!this.enterPredicate(n,this))return!1;const i=this._getShadowRoot().elementFromPoint(e,r);if(!i)return!1;const o=Mt(this.element);return i===o||o.contains(i)}_startReceiving(n,e){const r=this._activeSiblings;!r.has(n)&&e.every(i=>this.enterPredicate(i,this)||this._draggables.indexOf(i)>-1)&&(r.add(n),this._cacheParentPositions(),this._listenToScrollEvents(),this.receivingStarted.next({initiator:n,receiver:this,items:e}))}_stopReceiving(n){this._activeSiblings.delete(n),this._viewportScrollSubscription.unsubscribe(),this.receivingStopped.next({initiator:n,receiver:this})}_listenToScrollEvents(){this._viewportScrollSubscription=this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(n=>{if(this.isDragging()){const e=this._parentPositions.handleScroll(n);e&&this._sortStrategy.updateOnScroll(e.top,e.left)}else this.isReceiving()&&this._cacheParentPositions()})}_getShadowRoot(){if(!this._cachedShadowRoot){const n=nw(Mt(this.element));this._cachedShadowRoot=n||this._document}return this._cachedShadowRoot}_notifyReceivingSiblings(){const n=this._sortStrategy.getActiveItemsSnapshot().filter(e=>e.isDragging());this._siblings.forEach(e=>e._startReceiving(this,n))}}function bw(t,n){const{top:e,bottom:r,height:i}=t,o=.05*i;return n>=e-o&&n<=e+o?1:n>=r-o&&n<=r+o?2:0}function ww(t,n){const{left:e,right:r,width:i}=t,o=.05*i;return n>=e-o&&n<=e+o?1:n>=r-o&&n<=r+o?2:0}const Tl=ns({passive:!1,capture:!0});let QL=(()=>{class t{constructor(e,r){this._ngZone=e,this._dropInstances=new Set,this._dragInstances=new Set,this._activeDragInstances=[],this._globalListeners=new Map,this._draggingPredicate=i=>i.isDragging(),this.pointerMove=new te,this.pointerUp=new te,this.scroll=new te,this._preventDefaultWhileDragging=i=>{this._activeDragInstances.length>0&&i.preventDefault()},this._persistentTouchmoveListener=i=>{this._activeDragInstances.length>0&&(this._activeDragInstances.some(this._draggingPredicate)&&i.preventDefault(),this.pointerMove.next(i))},this._document=r}registerDropContainer(e){this._dropInstances.has(e)||this._dropInstances.add(e)}registerDragItem(e){this._dragInstances.add(e),1===this._dragInstances.size&&this._ngZone.runOutsideAngular(()=>{this._document.addEventListener("touchmove",this._persistentTouchmoveListener,Tl)})}removeDropContainer(e){this._dropInstances.delete(e)}removeDragItem(e){this._dragInstances.delete(e),this.stopDragging(e),0===this._dragInstances.size&&this._document.removeEventListener("touchmove",this._persistentTouchmoveListener,Tl)}startDragging(e,r){if(!(this._activeDragInstances.indexOf(e)>-1)&&(this._activeDragInstances.push(e),1===this._activeDragInstances.length)){const i=r.type.startsWith("touch");this._globalListeners.set(i?"touchend":"mouseup",{handler:o=>this.pointerUp.next(o),options:!0}).set("scroll",{handler:o=>this.scroll.next(o),options:!0}).set("selectstart",{handler:this._preventDefaultWhileDragging,options:Tl}),i||this._globalListeners.set("mousemove",{handler:o=>this.pointerMove.next(o),options:Tl}),this._ngZone.runOutsideAngular(()=>{this._globalListeners.forEach((o,s)=>{this._document.addEventListener(s,o.handler,o.options)})})}}stopDragging(e){const r=this._activeDragInstances.indexOf(e);r>-1&&(this._activeDragInstances.splice(r,1),0===this._activeDragInstances.length&&this._clearGlobalListeners())}isDragging(e){return this._activeDragInstances.indexOf(e)>-1}scrolled(e){const r=[this.scroll];return e&&e!==this._document&&r.push(new ve(i=>this._ngZone.runOutsideAngular(()=>{const s=a=>{this._activeDragInstances.length&&i.next(a)};return e.addEventListener("scroll",s,!0),()=>{e.removeEventListener("scroll",s,!0)}}))),fs(...r)}ngOnDestroy(){this._dragInstances.forEach(e=>this.removeDragItem(e)),this._dropInstances.forEach(e=>this.removeDropContainer(e)),this._clearGlobalListeners(),this.pointerMove.complete(),this.pointerUp.complete()}_clearGlobalListeners(){this._globalListeners.forEach((e,r)=>{this._document.removeEventListener(r,e.handler,e.options)}),this._globalListeners.clear()}static#e=this.\u0275fac=function(r){return new(r||t)(j(U),j(Et))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();const YL={dragStartThreshold:5,pointerDirectionChangeThreshold:5};let sf=(()=>{class t{constructor(e,r,i,o){this._document=e,this._ngZone=r,this._viewportRuler=i,this._dragDropRegistry=o}createDrag(e,r=YL){return new GL(e,r,this._document,this._ngZone,this._viewportRuler,this._dragDropRegistry)}createDropList(e){return new ZL(e,this._dragDropRegistry,this._document,this._ngZone,this._viewportRuler)}static#e=this.\u0275fac=function(r){return new(r||t)(j(Et),j(U),j(wL),j(QL))};static#t=this.\u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();const af=new P("CDK_DRAG_PARENT"),lf=new P("CdkDragHandle"),Ew=new P("CdkDragPlaceholder"),Mw=new P("CdkDragPreview"),Iw=new P("CDK_DRAG_CONFIG"),Sw=new P("CdkDropList");let Ow=(()=>{class t{static#e=this._dragInstances=[];get disabled(){return this._disabled||this.dropContainer&&this.dropContainer.disabled}set disabled(e){this._disabled=tr(e),this._dragRef.disabled=this._disabled}constructor(e,r,i,o,s,a,l,c,u,d,h){this.element=e,this.dropContainer=r,this._ngZone=o,this._viewContainerRef=s,this._dir=l,this._changeDetectorRef=u,this._selfHandle=d,this._parentDrag=h,this._destroyed=new te,this.started=new B,this.released=new B,this.ended=new B,this.entered=new B,this.exited=new B,this.dropped=new B,this.moved=new ve(f=>{const g=this._dragRef.moved.pipe(ze(v=>({source:this,pointerPosition:v.pointerPosition,event:v.event,delta:v.delta,distance:v.distance}))).subscribe(f);return()=>{g.unsubscribe()}}),this._dragRef=c.createDrag(e,{dragStartThreshold:a&&null!=a.dragStartThreshold?a.dragStartThreshold:5,pointerDirectionChangeThreshold:a&&null!=a.pointerDirectionChangeThreshold?a.pointerDirectionChangeThreshold:5,zIndex:a?.zIndex}),this._dragRef.data=this,t._dragInstances.push(this),a&&this._assignDefaults(a),r&&(this._dragRef._withDropContainer(r._dropListRef),r.addItem(this)),this._syncInputs(this._dragRef),this._handleEvents(this._dragRef)}getPlaceholderElement(){return this._dragRef.getPlaceholderElement()}getRootElement(){return this._dragRef.getRootElement()}reset(){this._dragRef.reset()}getFreeDragPosition(){return this._dragRef.getFreeDragPosition()}setFreeDragPosition(e){this._dragRef.setFreeDragPosition(e)}ngAfterViewInit(){this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.pipe(Rt(1),Se(this._destroyed)).subscribe(()=>{this._updateRootElement(),this._setupHandlesListener(),this.freeDragPosition&&this._dragRef.setFreeDragPosition(this.freeDragPosition)})})}ngOnChanges(e){const r=e.rootElementSelector,i=e.freeDragPosition;r&&!r.firstChange&&this._updateRootElement(),i&&!i.firstChange&&this.freeDragPosition&&this._dragRef.setFreeDragPosition(this.freeDragPosition)}ngOnDestroy(){this.dropContainer&&this.dropContainer.removeItem(this);const e=t._dragInstances.indexOf(this);e>-1&&t._dragInstances.splice(e,1),this._ngZone.runOutsideAngular(()=>{this._destroyed.next(),this._destroyed.complete(),this._dragRef.dispose()})}_updateRootElement(){const e=this.element.nativeElement;let r=e;this.rootElementSelector&&(r=void 0!==e.closest?e.closest(this.rootElementSelector):e.parentElement?.closest(this.rootElementSelector)),this._dragRef.withRootElement(r||e)}_getBoundaryElement(){const e=this.boundaryElement;return e?"string"==typeof e?this.element.nativeElement.closest(e):Mt(e):null}_syncInputs(e){e.beforeStarted.subscribe(()=>{if(!e.isDragging()){const r=this._dir,i=this.dragStartDelay,o=this._placeholderTemplate?{template:this._placeholderTemplate.templateRef,context:this._placeholderTemplate.data,viewContainer:this._viewContainerRef}:null,s=this._previewTemplate?{template:this._previewTemplate.templateRef,context:this._previewTemplate.data,matchSize:this._previewTemplate.matchSize,viewContainer:this._viewContainerRef}:null;e.disabled=this.disabled,e.lockAxis=this.lockAxis,e.dragStartDelay="object"==typeof i&&i?i:function Zb(t,n=0){return function qF(t){return!isNaN(parseFloat(t))&&!isNaN(Number(t))}(t)?Number(t):n}(i),e.constrainPosition=this.constrainPosition,e.previewClass=this.previewClass,e.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(o).withPreviewTemplate(s).withPreviewContainer(this.previewContainer||"global"),r&&e.withDirection(r.value)}}),e.beforeStarted.pipe(Rt(1)).subscribe(()=>{if(this._parentDrag)return void e.withParent(this._parentDrag._dragRef);let r=this.element.nativeElement.parentElement;for(;r;){if(r.classList.contains("cdk-drag")){e.withParent(t._dragInstances.find(i=>i.element.nativeElement===r)?._dragRef||null);break}r=r.parentElement}})}_handleEvents(e){e.started.subscribe(r=>{this.started.emit({source:this,event:r.event}),this._changeDetectorRef.markForCheck()}),e.released.subscribe(r=>{this.released.emit({source:this,event:r.event})}),e.ended.subscribe(r=>{this.ended.emit({source:this,distance:r.distance,dropPoint:r.dropPoint,event:r.event}),this._changeDetectorRef.markForCheck()}),e.entered.subscribe(r=>{this.entered.emit({container:r.container.data,item:this,currentIndex:r.currentIndex})}),e.exited.subscribe(r=>{this.exited.emit({container:r.container.data,item:this})}),e.dropped.subscribe(r=>{this.dropped.emit({previousIndex:r.previousIndex,currentIndex:r.currentIndex,previousContainer:r.previousContainer.data,container:r.container.data,isPointerOverContainer:r.isPointerOverContainer,item:this,distance:r.distance,dropPoint:r.dropPoint,event:r.event})})}_assignDefaults(e){const{lockAxis:r,dragStartDelay:i,constrainPosition:o,previewClass:s,boundaryElement:a,draggingDisabled:l,rootElementSelector:c,previewContainer:u}=e;this.disabled=l??!1,this.dragStartDelay=i||0,r&&(this.lockAxis=r),o&&(this.constrainPosition=o),s&&(this.previewClass=s),a&&(this.boundaryElement=a),c&&(this.rootElementSelector=c),u&&(this.previewContainer=u)}_setupHandlesListener(){this._handles.changes.pipe(Xh(this._handles),function HL(t,n,e){const r=se(t)||n||e?{next:t,error:n,complete:e}:t;return r?Je((i,o)=>{var s;null===(s=r.subscribe)||void 0===s||s.call(r);let a=!0;i.subscribe(Ge(o,l=>{var c;null===(c=r.next)||void 0===c||c.call(r,l),o.next(l)},()=>{var l;a=!1,null===(l=r.complete)||void 0===l||l.call(r),o.complete()},l=>{var c;a=!1,null===(c=r.error)||void 0===c||c.call(r,l),o.error(l)},()=>{var l,c;a&&(null===(l=r.unsubscribe)||void 0===l||l.call(r)),null===(c=r.finalize)||void 0===c||c.call(r)}))}):Or}(e=>{const r=e.filter(i=>i._parentDrag===this).map(i=>i.element);this._selfHandle&&this.rootElementSelector&&r.push(this.element),this._dragRef.withHandles(r)}),Tr(e=>fs(...e.map(r=>r._stateChanges.pipe(Xh(r))))),Se(this._destroyed)).subscribe(e=>{const r=this._dragRef,i=e.element.nativeElement;e.disabled?r.disableHandle(i):r.enableHandle(i)})}static#t=this.\u0275fac=function(r){return new(r||t)(C(Xe),C(Sw,12),C(Et),C(U),C(Xt),C(Iw,8),C(rw,8),C(sf),C(Ei),C(lf,10),C(af,12))};static#n=this.\u0275dir=H({type:t,selectors:[["","cdkDrag",""]],contentQueries:function(r,i,o){if(1&r&&(xo(o,Mw,5),xo(o,Ew,5),xo(o,lf,5)),2&r){let s;Ci(s=Di())&&(i._previewTemplate=s.first),Ci(s=Di())&&(i._placeholderTemplate=s.first),Ci(s=Di())&&(i._handles=s)}},hostAttrs:[1,"cdk-drag"],hostVars:4,hostBindings:function(r,i){2&r&&Gn("cdk-drag-disabled",i.disabled)("cdk-drag-dragging",i._dragRef.isDragging())},inputs:{data:["cdkDragData","data"],lockAxis:["cdkDragLockAxis","lockAxis"],rootElementSelector:["cdkDragRootElement","rootElementSelector"],boundaryElement:["cdkDragBoundary","boundaryElement"],dragStartDelay:["cdkDragStartDelay","dragStartDelay"],freeDragPosition:["cdkDragFreeDragPosition","freeDragPosition"],disabled:["cdkDragDisabled","disabled"],constrainPosition:["cdkDragConstrainPosition","constrainPosition"],previewClass:["cdkDragPreviewClass","previewClass"],previewContainer:["cdkDragPreviewContainer","previewContainer"]},outputs:{started:"cdkDragStarted",released:"cdkDragReleased",ended:"cdkDragEnded",entered:"cdkDragEntered",exited:"cdkDragExited",dropped:"cdkDragDropped",moved:"cdkDragMoved"},exportAs:["cdkDrag"],standalone:!0,features:[ge([{provide:af,useExisting:t}]),Qe]})}return t})(),o2=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t});static#n=this.\u0275inj=We({providers:[sf],imports:[Yh]})}return t})();function a2(t,n){if(1&t&&(Be(0),m(1,"option",15),M(2),_(),je()),2&t){const e=n.$implicit;p(1),b("value",e.name),p(1),L(e.title)}}const l2=function(t){return{color:t}};function c2(t,n){if(1&t){const e=oe();Be(0),m(1,"input",11),V("change",function(){T(e);const i=y(2);return x(i.isChooseStatusEnabled=!i.isChooseStatusEnabled)}),_(),m(2,"span",12),M(3),k(4,"translate"),_(),m(5,"div")(6,"select",13),V("ngModelChange",function(i){return T(e),x(y(2).selectedState=i)}),S(7,a2,3,2,"ng-container",14),_()(),je()}if(2&t){const e=y(2);p(1),b("checked",e.isChooseStatusEnabled),p(1),b("ngStyle",Ne(8,l2,e.isChooseStatusEnabled?"black":"grey")),p(1),L(R(4,6,"SELECT_STATUS")),p(3),b("disabled",!e.isChooseStatusEnabled)("ngModel",e.selectedState),p(1),b("ngForOf",e.nextStatusList)}}function u2(t,n){if(1&t){const e=oe();m(0,"div",7)(1,"h4"),M(2),k(3,"translate"),_(),S(4,c2,8,10,"ng-container",8),m(5,"button",9),V("click",function(){return T(e),x(y().close())}),W(6,"img",10),_()()}if(2&t){const e=y();p(2),L(R(3,2,"SELECT_DRIVER")),p(2),b("ngIf",!e.hideStatusSelector)}}function d2(t,n){1&t&&(m(0,"div",16),M(1),k(2,"translate"),_()),2&t&&(p(1),L(R(2,1,"LOADING")))}const h2=function(t,n){return{"border-color":t,color:n}},f2=function(t){return{background:t}};function p2(t,n){if(1&t){const e=oe();m(0,"div",17)(1,"div",18),W(2,"img",19),m(3,"div",20)(4,"div",21)(5,"span"),M(6),_()(),m(7,"div")(8,"span",20),M(9),_()()(),m(10,"div",22),M(11),k(12,"translate"),_(),m(13,"div"),M(14),k(15,"translate"),_()(),m(16,"div",23)(17,"button",24),V("click",function(){const o=T(e).$implicit;return x(y().showAgentOnMap(o))}),M(18),k(19,"translate"),_(),m(20,"button",25),V("click",function(){const o=T(e).$implicit,s=y();return x(s.selectDriverFromList((!s.currentOrder||s.currentOrder.assign_to_agent[0]!=o.id)&&o.id))}),M(21),k(22,"translate"),_()()()}if(2&t){const e=n.$implicit,r=y();p(2),Bt("alt",e.commercial_name),b("src",r.getImageUrl(e.id),xt),p(4),L(e.commercial_name),p(3),L(e.mobile_number),p(2),Dt(" ",e.nearest_driver_orders.length," ",R(12,14,"ORDERS")," "),p(2),ed(e.online?"online-btn":"offline-btn"),p(1),be(" ",R(15,16,e.online?"ONLINE":"OFFLINE")," "),p(3),b("ngStyle",iy(22,h2,r.activeAgent&&r.activeAgent.id==e.id?"red":"#3ab600",r.activeAgent&&r.activeAgent.id==e.id?"red":"#3ab600")),p(1),L(R(19,18,r.activeAgent&&r.activeAgent.id==e.id?"DISMISS":"SHOW_ON_MAP")),p(2),b("disabled",r.loading)("ngStyle",Ne(25,f2,r.currentOrder&&r.currentOrder.assign_to_agent[0]==e.id&&!r.loading?"red":r.loading?"grey":"#3ab600")),p(1),L(r.currentOrder&&r.currentOrder.assign_to_agent[0]==e.id?"DESELECT":R(22,20,"SELECT"))}}let Tw=(()=>{class t{constructor(){this.driversToSelect=[],this.onClickDriver=new B,this.onClose=new B,this.onClickShowOnMap=new B,this.nextStatusList=[],this.hideStatusSelector=!1,this.searchQuery="",this.filteredDrivers=[]}ngOnInit(){}ngOnChanges(e){e.driversToSelect&&this.driversToSelect&&(this.filteredDrivers=[...this.driversToSelect],this.filterDrivers())}getImageUrl(e){return`${window.location.origin}/web/image/rb_delivery.user/${e}/user_image/50x50`}selectDriverFromList(e){this.onClickDriver.emit(this.isChooseStatusEnabled?{assign_to_agent:e,state:this.selectedState}:{assign_to_agent:e})}close(){this.onClose.emit(),this.activeAgent&&this.onClickShowOnMap.emit({agent:this.activeAgent})}showAgentOnMap(e){this.onClickShowOnMap.emit({agent:e})}filterDrivers(){this.filteredDrivers=this.driversToSelect.filter(e=>{const i=e.mobile_number||"";return(e.commercial_name||"").toLowerCase().includes(this.searchQuery.toLowerCase())||i.toLowerCase().includes(this.searchQuery.toLowerCase())})}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275cmp=$t({type:t,selectors:[["app-driver-list"]],inputs:{driversToSelect:"driversToSelect",currentOrder:"currentOrder",loading:"loading",activeAgent:"activeAgent",currentStatus:"currentStatus",nextStatusList:"nextStatusList",hideStatusSelector:"hideStatusSelector"},outputs:{onClickDriver:"onClickDriver",onClose:"onClose",onClickShowOnMap:"onClickShowOnMap"},features:[Qe],decls:8,vars:7,consts:[["class","fixed-to-top","style","justify-content: space-between; background-color: white; padding-block: 15px;",4,"ngIf"],[2,"padding","10px"],[1,"search-container"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/search.svg",1,"icon"],["type","text",3,"placeholder","ngModel","ngModelChange","input"],["class","fixed-to-top loading",4,"ngIf"],["class","list-item",4,"ngFor","ngForOf"],[1,"fixed-to-top",2,"justify-content","space-between","background-color","white","padding-block","15px"],[4,"ngIf"],[3,"click"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/close.svg",1,"icon"],["type","checkbox",3,"checked","change"],[3,"ngStyle"],[3,"disabled","ngModel","ngModelChange"],[4,"ngFor","ngForOf"],[3,"value"],[1,"fixed-to-top","loading"],[1,"list-item"],[1,"driver-container"],[2,"border-radius","50%","box-shadow","0 0 10px #ccc","width","45px","margin-inline-end","15px",3,"src","alt"],[2,"width","45vw"],[2,"display","flex"],[1,"offline-btn"],[2,"background-color","white","display","flex","flex-direction","row","padding","5px","gap","5px","border-radius","0 0 10px 10px"],[1,"select-btn-outline",3,"ngStyle","click"],[1,"select-btn",3,"disabled","ngStyle","click"]],template:function(r,i){1&r&&(S(0,u2,7,4,"div",0),m(1,"div",1)(2,"div",2),W(3,"img",3),m(4,"input",4),V("ngModelChange",function(s){return i.searchQuery=s})("input",function(){return i.filterDrivers()}),k(5,"translate"),_()(),S(6,d2,3,3,"div",5),S(7,p2,23,27,"div",6),_()),2&r&&(b("ngIf",!i.loading),p(4),Bt("placeholder",R(5,5,"SEARCH_BY_NAME_OR_MOBILE_NUMBER")),b("ngModel",i.searchQuery),p(2),b("ngIf",i.loading),p(1),b("ngForOf",i.filteredDrivers))},dependencies:[Ii,Rn,_r,jh,zh,br,yl,$o,Xo,Yn],styles:[".driver-container[_ngcontent-%COMP%]{display:flex;align-items:center;margin:10px 0;padding:10px;background-color:#fff;height:6vh}.online-btn[_ngcontent-%COMP%]{color:#fff;width:-moz-fit-content;width:fit-content;white-space:nowrap;background-color:#25d366;border-radius:15px;margin:0 6px;padding:5px 10px;zoom:.7}.offline-btn[_ngcontent-%COMP%]{color:#fff;width:-moz-fit-content;width:fit-content;white-space:nowrap;background-color:red;border-radius:15px;margin:0 6px;padding:5px 10px;zoom:.7}.select-btn[_ngcontent-%COMP%]{width:100%;height:20px;border-radius:10px;background-color:#3ab600;border:none;cursor:pointer;color:#fff}.select-btn-outline[_ngcontent-%COMP%]{width:100%;height:20px;border-radius:10px;background-color:#fff;border:1px solid #3ab600;cursor:pointer;color:#3ab600}.fixed-to-top[_ngcontent-%COMP%]{position:sticky;left:0;top:0;align-items:center;display:flex;box-shadow:0 2px 4px -1px #0003,0 4px 5px #00000024,0 1px 10px #0000001f}.fixed-to-top[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{all:unset;margin-inline:16px;margin-top:3px;cursor:pointer}.fixed-to-top[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{all:unset;margin-inline-start:16px}.loading[_ngcontent-%COMP%]{background-color:#000;justify-content:center;color:#fff}.search-container[_ngcontent-%COMP%]{display:flex;height:48px;border-radius:10px;width:100%;padding:5px;border:1px solid #E3E3E3}.search-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{all:unset;height:100%;width:-webkit-fill-available}.search-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:20px;margin-inline:12px}.list-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;border-radius:10px;border:1px solid #E3E3E3;margin:10px 0;background-color:#fff}.icon[_ngcontent-%COMP%]{width:20px}"]})}return t})();function g2(t,n){if(1&t&&(m(0,"div",12)(1,"div",33),M(2),_(),M(3),k(4,"translate"),_()),2&t){const e=y(2);p(2),L(e.nearestDriverTimeOut),p(1),L(R(4,2,"TIME_TO_ACCEPT"))}}function m2(t,n){if(1&t&&(m(0,"div",11)(1,"div",12),W(2,"img",13),m(3,"span",14),M(4),_()(),m(5,"div",12),W(6,"img",15),m(7,"a",16),M(8),_()(),m(9,"div",12),W(10,"img",17),m(11,"a",18),M(12),_()(),S(13,g2,5,4,"div",29),_()),2&t){const e=y();p(2),b("src",e.getImageUrl(e.order.assign_to_agent[0]),xt),p(2),L(e.order.assign_to_agent[1]),p(3),Tn("href","tel:",e.getAgentMobile(e.order),"",xt),p(1),L(e.getAgentMobile(e.order)),p(3),Tn("href","https://wa.me/",e.order.business_whatsapp_mobile,"",xt),p(1),L(e.order.agent_whatsapp_mobile),p(1),b("ngIf",e.nearestDriverTimeOut>0||e.order.notification_timer&&e.order.notification_timer>0&&e.startNearestDriverTimer(e.order.notification_timer))}}function _2(t,n){1&t&&(m(0,"strong",27),M(1),k(2,"translate"),_()),2&t&&(p(1),L(R(2,1,"ETA_FOR_DELIVERED")))}function v2(t,n){1&t&&(m(0,"strong",27),M(1),k(2,"translate"),_()),2&t&&(p(1),L(R(2,1,"ETA_FOR_PICKED_UP")))}function y2(t,n){if(1&t&&(m(0,"span",12),M(1),_()),2&t){const e=y();p(1),L(e.order.eta_for_delivered)}}function C2(t,n){if(1&t&&(m(0,"span",12),M(1),_()),2&t){const e=y();p(1),L(e.order.eta_for_picked_up)}}function D2(t,n){if(1&t){const e=oe();m(0,"button",34),V("click",function(){T(e);const i=y();return x(i.showDriverList.emit(i.order))}),M(1),k(2,"translate"),_()}2&t&&(p(1),L(R(2,1,"ASSIGN_AGENT")))}function b2(t,n){if(1&t){const e=oe();m(0,"button",34),V("click",function(){T(e);const i=y();return i.showDriversInRange.emit(i.order),x(i.showOrderOnMap.emit(i.order))}),M(1),k(2,"translate"),_()}2&t&&(p(1),be(" ",R(2,1,"SHOW_DRIVERS_IN_RANGE")," "))}const w2=function(t){return{transform:t}},E2=function(t){return{background:t}};let M2=(()=>{class t{constructor(e){this.translate=e,this.nearestDriverTimeOut=0,this.timeDurationConfig=0,this.checked=!1,this.checkedOrderIds=[],this.showStatusList=new B,this.showDriverList=new B,this.showOrderOnMap=new B,this.showDriversInRange=new B,this.selectedGroupOrders=new B}getImageUrl(e){return`${window.location.origin}/web/image/rb_delivery.user/${e}/user_image/50x50`}getAgentMobile(e){return e.agent_commercial_number?e.agent_commercial_number:!!e.agent_mobile_number&&e.agent_mobile_number}getLocalDateTime(e){const r=new Date(e),i=r.getTimezoneOffset();return new Date(r.getTime()-6e4*i).toLocaleString(void 0,{month:"2-digit",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1})}startNearestDriverTimer(e){if(this.isTimerStarted||!e||0==e)return!1;this.nearestDriverTimeOut=this.timeDurationConfig-e;let r=setInterval(()=>{this.nearestDriverTimeOut>0?(this.isTimerStarted=!0,this.nearestDriverTimeOut-=1):(this.isTimerStarted=!1,clearInterval(r))},1e3);return!0}toggleOrderSelection(e){this.checked=e.target.checked,this.checkedOrderIds=this.checked?this.checkedOrderIds.concat([this.order.id]):this.checkedOrderIds.filter(r=>this.order.id!=r),this.selectedGroupOrders.emit(this.checkedOrderIds)}getChecked(){return[this.order.id].every(e=>this.checkedOrderIds.includes(e))}static#e=this.\u0275fac=function(r){return new(r||t)(C(Cr))};static#t=this.\u0275cmp=$t({type:t,selectors:[["app-order-item"]],inputs:{order:"order",aciveOrder:"aciveOrder",userRole:"userRole",nearestDriverTimeOut:"nearestDriverTimeOut",timeDurationConfig:"timeDurationConfig",checked:"checked",checkedOrderIds:"checkedOrderIds"},outputs:{showStatusList:"showStatusList",showDriverList:"showDriverList",showOrderOnMap:"showOrderOnMap",showDriversInRange:"showDriversInRange",selectedGroupOrders:"selectedGroupOrders"},decls:67,vars:38,consts:[[1,"list-item",2,"width","95%","margin","15px auto",3,"id"],[1,"order-item-header"],[1,"row-col",2,"color","white","font-weight","bold"],[1,"custom-checkbox",3,"click"],["type","checkbox",3,"checked","click","change"],["checkbox",""],[1,"checkmark"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/barcode.svg",1,"icon"],[1,"item-row",2,"cursor","pointer","font-size","15px","font-weight","bolder","border","1px solid white","border-radius","15px","background","white","padding-inline","5px",3,"click"],[1,"order-item-content"],[1,"item-row"],[1,"item-col",2,"height","50%"],[1,"row-col"],[2,"border-radius","50%","box-shadow","0 0 10px #ccc","width","40px",3,"src"],[2,"width","150px","overflow","hidden","white-space","nowrap","text-overflow","ellipsis"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/phone.webp",1,"icon"],[2,"color","#1c8adb",3,"href"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/whatsapp.png",1,"icon"],["target","_blank",2,"color","#3ab600",3,"href"],[1,"item-row",2,"justify-content","center"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/long-arrow.png",2,"width","50%",3,"ngStyle"],[1,"item-row",2,"justify-content","flex-end"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/customer.png",2,"border-radius","50%","box-shadow","0 0 10px #ccc","width","40px"],[2,"width","70px","overflow","hidden","white-space","nowrap","text-overflow","ellipsis"],["class","item-col","style","height: 50%;",4,"ngIf"],[1,"item-row",2,"justify-content","flex-end","padding-inline-end","5px"],[1,"item-col"],[1,"row-col",2,"font-size","10px","justify-content","end"],["class","row-col","style","font-size: 10px; justify-content: end;",4,"ngIf"],["class","row-col",4,"ngIf"],[2,"background-color","white","display","flex","flex-direction","row","padding","5px","gap","5px"],["class","select-btn",3,"click",4,"ngIf"],[1,"select-btn",3,"ngStyle","click"],[2,"background-color","red","color","white","border-radius","50%","height","27px","width","27px","display","flex","align-items","center","justify-content","center"],[1,"select-btn",3,"click"]],template:function(r,i){1&r&&(m(0,"div",0)(1,"div",1)(2,"div",2)(3,"label",3),V("click",function(s){return s.stopPropagation()}),m(4,"input",4,5),V("click",function(s){return s.stopPropagation()})("change",function(s){return i.toggleOrderSelection(s)}),_(),W(6,"span",6),_(),W(7,"img",7),m(8,"span"),M(9),_()(),m(10,"button",8),V("click",function(){return i.showStatusList.emit(i.order)}),m(11,"strong"),M(12),_()()(),m(13,"div",9)(14,"div",10)(15,"div",11)(16,"div",12),W(17,"img",13),m(18,"span",14),M(19),_()(),m(20,"div",12),W(21,"img",15),m(22,"a",16),M(23),_()(),m(24,"div",12),W(25,"img",17),m(26,"a",18),M(27),_()()()(),m(28,"div",19),W(29,"img",20),_(),m(30,"div",21)(31,"div",11)(32,"div",12),W(33,"img",22),m(34,"span",23),M(35),W(36,"br"),M(37),_()(),m(38,"div",12),W(39,"img",15),m(40,"a",16),M(41),_()(),m(42,"div",12),W(43,"img",17),m(44,"a",18),M(45),_()()()(),m(46,"div",10),S(47,m2,14,7,"div",24),_(),m(48,"div",25)(49,"div",26)(50,"strong",27),M(51),k(52,"translate"),_(),S(53,_2,3,3,"strong",28),S(54,v2,3,3,"strong",28),_()(),m(55,"div",10)(56,"div",26)(57,"span",12),M(58),_(),S(59,y2,2,1,"span",29),S(60,C2,2,1,"span",29),_()()(),m(61,"div",30),S(62,D2,3,3,"button",31),m(63,"button",32),V("click",function(){return i.showOrderOnMap.emit(i.order)}),M(64),k(65,"translate"),_(),S(66,b2,3,3,"button",31),_()()),2&r&&(Gn("fade_animation",i.order.add_fade_animation),b("id",i.order.id),p(4),b("checked",i.getChecked()),p(5),L(i.order.sequence),p(3),L(i.order.state_id[1]),p(5),b("src",i.getImageUrl(i.order.assign_to_business[0]),xt),p(2),L(i.order.assign_to_business[1]),p(3),Tn("href","tel:",i.order.business_mobile_number,"",xt),p(1),L(i.order.business_mobile_number),p(3),Tn("href","https://wa.me/",i.order.business_whatsapp_mobile,"",xt),p(1),L(i.order.business_whatsapp_mobile),p(2),b("ngStyle",Ne(34,w2,"ar"==i.translate.currentLang?"rotateY(180deg)":"none")),p(6),L(i.order.customer_name),p(2),L(i.order.customer_sub_area?i.order.customer_sub_area[1]:""),p(3),Tn("href","tel:",i.order.customer_mobile,"",xt),p(1),L(i.order.customer_mobile),p(3),Tn("href","https://wa.me/",i.order.cus_whatsapp_mobile,"",xt),p(1),L(i.order.cus_whatsapp_mobile),p(2),b("ngIf",i.order.assign_to_agent),p(4),L(R(52,30,"CREATE_DATE")),p(2),b("ngIf","picked_up"==i.order.state&&i.order.eta_for_delivered),p(1),b("ngIf","picking_up"==i.order.state&&i.order.eta_for_picked_up),p(4),L(i.getLocalDateTime(i.order.create_date)),p(1),b("ngIf","picked_up"==i.order.state&&i.order.eta_for_delivered),p(1),b("ngIf","picking_up"==i.order.state&&i.order.eta_for_picked_up),p(2),b("ngIf","rb_delivery.role_business"!=i.userRole),p(1),b("ngStyle",Ne(36,E2,i.aciveOrder&&i.aciveOrder.id==i.order.id?"red":"#3ab600")),p(1),be(" ",R(65,32,i.aciveOrder&&i.aciveOrder.id==i.order.id?"DISMISS":"SHOW_ON_MAP")," "),p(2),b("ngIf",(!i.aciveOrder||i.aciveOrder.id!=i.order.id)&&i.order.longitude&&i.order.latitude&&"rb_delivery.role_business"!=i.userRole))},dependencies:[Rn,_r,Yn],styles:['.container[_ngcontent-%COMP%]{display:flex;flex-direction:column;background-color:#fcefe7;border-radius:10px;padding:10px}.flex-space-between[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:10px}.button-base[_ngcontent-%COMP%]{flex:1;margin:0 5px;height:50px!important;border-radius:10px;border:none;cursor:pointer;background-color:#fcefe7;height:4rem}.button-orange[_ngcontent-%COMP%]{background-color:#e55604;color:#fff}.dropdown-container[_ngcontent-%COMP%]{margin:10px 0}.dropdown-btn[_ngcontent-%COMP%]{width:100%;height:44px;border-radius:10px;padding-inline:10px;background-color:#fff;border:none;cursor:pointer}.input-filter[_ngcontent-%COMP%]{margin-top:20px;padding:5px;width:100%;box-sizing:border-box}.user-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin:10px 0;padding:10px}.order-item-content[_ngcontent-%COMP%]{display:grid;padding:10px;background-color:#fff;overflow:scroll;grid-template-columns:35% 30% 35%}.order-item-header[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;padding-inline:10px;background:#e55604}.courier-img[_ngcontent-%COMP%]{width:570px;height:70px;border-radius:10px}.select-btn[_ngcontent-%COMP%]{width:100%;height:20px;border-radius:10px;font-size:13px;background-color:#3ab600;border:none;cursor:pointer;color:#fff}.button-base.active[_ngcontent-%COMP%]{background-color:#fff;color:#e55604}.side-menu.container[_ngcontent-%COMP%]{position:absolute;height:100vh;width:570px;left:0;overflow:scroll}.online-btn[_ngcontent-%COMP%]{color:#fff;background-color:green;width:14vw;border-radius:15px;margin:0 6px;padding-inline:5px}.busy-btn[_ngcontent-%COMP%]{color:#fff;background-color:orange;width:14vw;border-radius:15px;margin:0 6px;padding-inline:5px}.offline-btn[_ngcontent-%COMP%]{color:#fff;background-color:red;width:14vw;border-radius:15px;margin:0 6px;padding-inline:5px}.list-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;box-shadow:0 0 10px #ccc;border-radius:10px;overflow:hidden;margin:10px 0;background-color:#fff}.dropdown-container[_ngcontent-%COMP%]{position:relative}.dropdown-btn[_ngcontent-%COMP%]{width:100%;padding-inline-end:15px}.filter-dropdown[_ngcontent-%COMP%]{position:absolute;inset-inline-end:10px;top:100%;list-style:none;margin:0;z-index:1;width:40%;background-color:#fff;border-radius:10px;box-shadow:0 1px 3px #0000001f,0 1px 2px #0000003d,0 2px 4px #0000002e;padding:10px}.filter-dropdown[_ngcontent-%COMP%]:before{content:"";position:absolute;top:-10px;right:20px;width:0;height:0;border-left:10px solid transparent;border-right:10px solid transparent;border-bottom:10px solid white}.filter-dropdown[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{padding:5px;cursor:pointer;background-color:#fff;text-align:start;margin:5px 0;border-radius:5px}.filter-dropdown[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover{background:#f3f3f3}.applied-filter[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%]{color:red}.clear-filter-btn[_ngcontent-%COMP%]{position:absolute;background:none;border:none;cursor:pointer;font-weight:700;color:#fff;inset-inline-end:52px}.input-wrapper[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;padding:5px}.dropdown-btn[_ngcontent-%COMP%]{flex-grow:1;border:none;outline:none;padding-inline-end:50px}.filter-filter[_ngcontent-%COMP%]{position:absolute;inset-inline-end:15px;background:none;border:none;cursor:pointer}.x-button[_ngcontent-%COMP%]{cursor:pointer;background:red;display:inline-block;border-radius:50%;width:15px;height:15px}.applied-filter[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:hue-rotate(0deg) brightness(50%)}.item-row[_ngcontent-%COMP%]{display:flex;font-size:14px;margin-block:5px;align-self:center}.row-col[_ngcontent-%COMP%]{display:flex;font-size:14px;margin-block:5px;gap:5px;align-items:center}.item-col[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between;font-size:14px}.icon[_ngcontent-%COMP%]{width:20px}.fade_animation[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeOut .5s forwards}@keyframes _ngcontent-%COMP%_fadeOut{0%{opacity:100;transform:translate(0)}to{opacity:0;transform:translate(400px)}}.custom-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:20px;width:20px;background-color:transparent;border:2px solid #D97348;border-radius:3px}.custom-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after{content:"";position:absolute;left:4px;top:0;width:7px;height:14px;border:solid #D97348;border-width:0 3px 3px 0;transform:rotate(45deg);display:none}.custom-checkbox[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{display:none}.custom-checkbox[_ngcontent-%COMP%]{display:inline-block;position:relative;padding-left:30px;cursor:pointer;font-size:22px;-webkit-user-select:none;user-select:none}.custom-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]{position:absolute;top:-10px;left:0;height:20px;width:20px;background-color:transparent;border:2px solid white;border-radius:3px}.custom-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked + .checkmark[_ngcontent-%COMP%]:after{display:block}.custom-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after{content:"";position:absolute;top:3.5px;left:5.5px;width:2px;height:6px;border:solid white;border-width:0 3px 3px 0;transform:rotate(45deg);display:none}']})}return t})();function I2(t,n){if(1&t&&(m(0,"span",17),M(1),k(2,"translate"),_()),2&t){const e=y(2).$implicit,r=y();p(1),Dt("",r.getCheckedOrdersLength(e)," ",R(2,2,"SELECTED"),"")}}function S2(t,n){1&t&&W(0,"img",18)}function O2(t,n){1&t&&W(0,"img",19)}const T2=function(t){return[t]};function x2(t,n){if(1&t){const e=oe();Be(0),m(1,"app-group-container",20),V("showStatusList",function(i){return T(e),x(y(3).showStatusList.emit(i))})("showDriverList",function(i){return T(e),x(y(3).showDriverList.emit(i))})("showOrderOnMap",function(i){return T(e),x(y(3).showOrderOnMap.emit(i))})("showDriversInRange",function(i){return T(e),x(y(3).showDriversInRange.emit(i))})("selectedGroupOrders",function(i){T(e);const o=y(3);return o.selectedGroupOrders.emit(i),x(o.checkedOrderIds=i)}),_(),je()}if(2&t){const e=y(2),r=e.index,i=e.$implicit,o=y();p(1),b("groupSet",o.groupingRecordsSet[o.level+1])("level",o.level+1)("parentIndexes",o.getParentIndexes(Ne(11,T2,r).concat(o.parentIndexes)))("parentGroup",i)("checked",o.checked)("checkedOrderIds",o.checkedOrderIds)("parentOrders",o.getOrdersByField(i.id,i.field,o.parentOrders))("groupingRecordsSet",o.groupingRecordsSet)("aciveOrder",o.aciveOrder)("userRole",o.userRole)("nearestDriverTimeOut",o.nearestDriverTimeOut)}}function A2(t,n){if(1&t){const e=oe();m(0,"app-order-item",23),V("showStatusList",function(i){return T(e),x(y(4).showStatusList.emit(i))})("showDriverList",function(i){return T(e),x(y(4).showDriverList.emit(i))})("showOrderOnMap",function(i){return T(e),x(y(4).showOrderOnMap.emit(i))})("showDriversInRange",function(i){T(e);const o=y(3).$implicit,s=y();return s.showDriversInRange.emit(i),x(s.getChecked(o))})("selectedGroupOrders",function(i){T(e);const o=y(4);return o.selectedGroupOrders.emit(i),x(o.checkedOrderIds=i)}),_()}if(2&t){const e=n.$implicit,r=y(4);b("order",e)("aciveOrder",r.aciveOrder)("userRole",r.userRole)("nearestDriverTimeOut",r.nearestDriverTimeOut)("checked",r.checked)("checkedOrderIds",r.checkedOrderIds)}}const k2=function(){return{"background-color":"#00000014","border-radius":"10px","margin-block":"15px","padding-block":"1px"}},R2=function(){return{}};function P2(t,n){if(1&t&&(m(0,"div",21),S(1,A2,1,6,"app-order-item",22),_()),2&t){const e=y(2).$implicit,r=y();b("ngStyle",0==r.level?Qt(2,k2):Qt(3,R2)),p(1),b("ngForOf",r.getOrdersByField(e.id,e.field,r.parentOrders))}}function N2(t,n){if(1&t){const e=oe();Be(0),m(1,"button",4),V("click",function(){T(e);const i=y().$implicit,o=y();return x(o.selectedGroup=o.selectedGroup===i.id?0:i.id)}),m(2,"div",5)(3,"label",6),V("click",function(i){return i.stopPropagation()}),m(4,"input",7,8),V("click",function(i){return i.stopPropagation()})("change",function(i){T(e);const o=y().$implicit;return x(y().toggleGroupSelection(o,i))}),_(),W(6,"span",9),_(),m(7,"span",10),M(8),k(9,"translate"),_()(),m(10,"div",11),S(11,I2,3,4,"span",12),m(12,"span",13),M(13),k(14,"translate"),_(),S(15,S2,1,0,"img",14),S(16,O2,1,0,"img",15),_()(),S(17,x2,2,13,"ng-container",3),S(18,P2,2,4,"div",16),je()}if(2&t){const e=y().$implicit,r=y();p(4),b("indeterminate",r.getCheckedOrdersLength(e)>0&&r.getCheckedOrdersLength(e)!=r.getNumberOfOrdersByField(e.id,e.field,r.parentOrders))("checked",r.getChecked(e)),p(4),Dt(" ",R(9,11,e.label?e.label:"UNDEFINED")," ",e.label?"":e.field[2]," "),p(3),b("ngIf",r.getCheckedOrdersLength(e)>0),p(2),Dt(" ",r.getNumberOfOrdersByField(e.id,e.field,r.parentOrders)," ",R(14,13,"ORDERS")," "),p(2),b("ngIf",r.selectedGroup===e.id),p(1),b("ngIf",r.selectedGroup!==e.id),p(1),b("ngIf",r.selectedGroup===e.id&&r.level<r.groupingRecordsSet.length-1),p(1),b("ngIf",r.selectedGroup===e.id&&r.level===r.groupingRecordsSet.length-1)}}function F2(t,n){if(1&t&&(m(0,"div",2),S(1,N2,19,15,"ng-container",3),_()),2&t){const e=n.$implicit,r=y();p(1),b("ngIf",r.getNumberOfOrdersByField(e.id,e.field,r.parentOrders)>0)}}const L2=function(){return{"background-color":"#00000014","border-radius":"10px","margin-block":"15px"}},V2=function(){return{overflow:"scroll",height:"calc(100vh - 156px)"}};let B2=(()=>{class t{constructor(){this.groupSet=[],this.level=0,this.parentIndexes=[],this.parentGroup=!1,this.parentOrders=[],this.groupingRecordsSet=[],this.nearestDriverTimeOut=0,this.timeDurationConfig=0,this.checked=!1,this.checkedOrderIds=[],this.showStatusList=new B,this.showDriverList=new B,this.showOrderOnMap=new B,this.showDriversInRange=new B,this.selectedGroupOrders=new B,this.onParentToggled=new B,this.selectedGroup=0}toggleGroupSelection(e,r){this.checked=r.target.checked,this.checkedOrderIds=this.checked?this.checkedOrderIds.concat(this.getOrdersByField(e.id,e.field,this.parentOrders).map(i=>i.id)):this.checkedOrderIds.filter(i=>!this.getOrdersByField(e.id,e.field,this.parentOrders).map(o=>o.id).includes(i)),this.selectedGroupOrders.emit(this.checkedOrderIds)}getParentIndexes(e){return this.parentIndexes.concat(e)}getNumberOfOrdersByField(e,r,i){return i.filter(o=>("many2one"==r[1]?o[r[0]][0]||!1:"date"==r[1]||"datetime"==r[1]?o[r[0]].split(" ")[0]:o[r[0]])==e).length}getOrdersByField(e,r,i){return i.filter(o=>("many2one"==r[1]?o[r[0]][0]||!1:"date"==r[1]||"datetime"==r[1]?o[r[0]].split(" ")[0]:o[r[0]])==e)}getChecked(e){return this.getOrdersByField(e.id,e.field,this.parentOrders).map(r=>r.id).every(r=>this.checkedOrderIds.includes(r))}getCheckedOrdersLength(e){return this.getOrdersByField(e.id,e.field,this.parentOrders).map(r=>r.id).filter(r=>this.checkedOrderIds.includes(r)).length}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275cmp=$t({type:t,selectors:[["app-group-container"]],inputs:{groupSet:"groupSet",level:"level",parentIndexes:"parentIndexes",parentGroup:"parentGroup",parentOrders:"parentOrders",groupingRecordsSet:"groupingRecordsSet",order:"order",aciveOrder:"aciveOrder",userRole:"userRole",nearestDriverTimeOut:"nearestDriverTimeOut",timeDurationConfig:"timeDurationConfig",checked:"checked",checkedOrderIds:"checkedOrderIds"},outputs:{showStatusList:"showStatusList",showDriverList:"showDriverList",showOrderOnMap:"showOrderOnMap",showDriversInRange:"showDriversInRange",selectedGroupOrders:"selectedGroupOrders",onParentToggled:"onParentToggled"},decls:2,vars:5,consts:[[2,"padding-block","1px","background","#F3E8E2",3,"id","ngStyle"],["style","width: 95%; margin: 15px auto;",4,"ngFor","ngForOf"],[2,"width","95%","margin","15px auto"],[4,"ngIf"],[1,"group-button",3,"click"],[2,"overflow","hidden","white-space","nowrap","text-overflow","ellipsis"],[1,"custom-checkbox",3,"click"],["type","checkbox",3,"indeterminate","checked","click","change"],["checkbox",""],[1,"checkmark"],[2,"font-weight","bold","font-size","16px"],[2,"display","flex","flex-direction","row","gap","5px"],["style","font-weight: bold; font-size: 12px; align-self: center; color: white; background: #D97348; border-radius: 10px; padding: 2px 5px;white-space: nowrap;",4,"ngIf"],[2,"font-weight","bold","font-size","16px","align-self","center","white-space","nowrap"],["style","width: 25px;","src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/down.png",4,"ngIf"],["style","width: 25px;","src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/right.png",4,"ngIf"],[3,"ngStyle",4,"ngIf"],[2,"font-weight","bold","font-size","12px","align-self","center","color","white","background","#D97348","border-radius","10px","padding","2px 5px","white-space","nowrap"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/down.png",2,"width","25px"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/right.png",2,"width","25px"],[3,"groupSet","level","parentIndexes","parentGroup","checked","checkedOrderIds","parentOrders","groupingRecordsSet","aciveOrder","userRole","nearestDriverTimeOut","showStatusList","showDriverList","showOrderOnMap","showDriversInRange","selectedGroupOrders"],[3,"ngStyle"],[3,"order","aciveOrder","userRole","nearestDriverTimeOut","checked","checkedOrderIds","showStatusList","showDriverList","showOrderOnMap","showDriversInRange","selectedGroupOrders",4,"ngFor","ngForOf"],[3,"order","aciveOrder","userRole","nearestDriverTimeOut","checked","checkedOrderIds","showStatusList","showDriverList","showOrderOnMap","showDriversInRange","selectedGroupOrders"]],template:function(r,i){1&r&&(m(0,"div",0),S(1,F2,2,1,"div",1),_()),2&r&&(b("id",0==i.level&&"groupContainer")("ngStyle",0!=i.level?Qt(3,L2):Qt(4,V2)),p(1),b("ngForOf",i.groupSet))},dependencies:[Ii,Rn,_r,M2,t,Yn],styles:['.group-button[_ngcontent-%COMP%]{width:100%;height:50px;flex-shrink:0;border-radius:10px;background:#FFF;box-shadow:0 2px 48px #0000000a;display:flex;justify-content:space-between;align-items:center;padding:0 15px;border-width:0;cursor:pointer}.checkbox-icon[_ngcontent-%COMP%]{display:inline-block;width:16px;height:16px;margin-right:.5em;border:2px solid #007bff;border-radius:4px;position:relative}.custom-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:20px;width:20px;background-color:transparent;border:2px solid #D97348;border-radius:3px}.custom-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after{content:"";position:absolute;left:4px;top:0;width:7px;height:14px;border:solid #D97348;border-width:0 3px 3px 0;transform:rotate(45deg);display:none}.custom-checkbox[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{display:none}.custom-checkbox[_ngcontent-%COMP%]{display:inline-block;position:relative;padding-left:30px;cursor:pointer;font-size:22px;-webkit-user-select:none;user-select:none}.custom-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]{position:absolute;top:-15px;left:0;height:20px;width:20px;background-color:transparent;border:2px solid #D97348;border-radius:3px}.custom-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked + .checkmark[_ngcontent-%COMP%]:after{display:block}.custom-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after{content:"";position:absolute;top:3.5px;left:5.5px;width:2px;height:6px;border:solid #D97348;border-width:0 3px 3px 0;transform:rotate(45deg);display:none}.custom-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:indeterminate + .checkmark[_ngcontent-%COMP%]:before{content:"";position:absolute;top:7.5px;left:4.5px;width:7px;height:2px;background-color:#d97348;display:block}.custom-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:indeterminate + .checkmark[_ngcontent-%COMP%]:after{display:none}']})}return t})();function j2(t,n){if(1&t){const e=oe();m(0,"li",21),V("click",function(){return T(e),x(y(2).showContent("senders"))}),M(1),k(2,"translate"),_()}2&t&&(p(1),L(R(2,1,"BUSINESSES")))}function H2(t,n){if(1&t){const e=oe();m(0,"li",21),V("click",function(){return T(e),x(y(2).showContent("routes"))}),M(1),k(2,"translate"),_()}2&t&&(p(1),L(R(2,1,"ROUTES")))}const z2=function(){return["admin","rb_delivery.role_super_manager","rb_delivery.role_manager"]};function U2(t,n){if(1&t){const e=oe();m(0,"ul",20)(1,"li",21),V("click",function(){return T(e),x(y().showContent("orders"))}),M(2),k(3,"translate"),_(),S(4,j2,3,3,"li",22),m(5,"li",21),V("click",function(){return T(e),x(y().showContent("drivers"))}),M(6),k(7,"translate"),_(),S(8,H2,3,3,"li",22),m(9,"li",21),V("click",function(){return T(e),x(y().showContent("all"))}),M(10),k(11,"translate"),_()()}if(2&t){const e=y();p(2),L(R(3,5,"ORDERS")),p(2),b("ngIf","rb_delivery.role_business"!=e.userRole),p(2),L(R(7,7,"DRIVERS")),p(2),b("ngIf",Qt(11,z2).includes(e.userRole)),p(2),L(R(11,9,"ALL"))}}function $2(t,n){if(1&t&&(m(0,"span",25),M(1),k(2,"translate"),_()),2&t){const e=y(2);p(1),Dt("",e.checkedOrders.length," ",R(2,2,"SELECTED"),"")}}function G2(t,n){if(1&t){const e=oe();m(0,"button",23),V("click",function(){T(e);const i=y();return i.showContentSelector=!1,i.showGroupSelector=!1,x(i.showActionSelector=!i.showActionSelector)}),S(1,$2,3,4,"span",24),m(2,"span"),M(3),k(4,"translate"),_(),m(5,"span",5),W(6,"img",6),_()()}if(2&t){const e=y();Gn("disabled",0==e.checkedOrders.length),b("disabled",0==e.checkedOrders.length),p(1),b("ngIf",e.checkedOrders.length>0),p(2),L(R(4,5,"ACTION"))}}function W2(t,n){if(1&t){const e=oe();m(0,"ul",26)(1,"li",21),V("click",function(){return T(e),x(y().showAssignAgent())}),M(2),k(3,"translate"),_()()}2&t&&(p(2),L(R(3,1,"ASSIGN_TO_AGENT")))}const q2=function(){return[]},Z2=function(t){return{assign_to_agent:t}},K2=function(){return{top:"80px",position:"fixed",left:"534px","z-index":"1000"}};function Q2(t,n){if(1&t){const e=oe();m(0,"app-driver-list",27),V("onClickDriver",function(i){return T(e),x(y().assignAgentAction(i))})("onClickShowOnMap",function(i){return T(e),x(y().showAgentOnMap(i.agent))})("onClose",function(){return T(e),x(y().closeDriverList())}),_()}2&t&&b("driversToSelect",y().driversToSelect)("currentOrder",Ne(6,Z2,Qt(5,q2)))("hideStatusSelector",!0)("loading",!1)("ngStyle",Qt(8,K2))}function Y2(t,n){if(1&t){const e=oe();m(0,"button",28),V("click",function(){T(e);const i=y();return i.showActionSelector=!1,i.showContentSelector=!1,x(i.showGroupSelector=!i.showGroupSelector)}),m(1,"span",29),W(2,"img",30),_()()}}function X2(t,n){1&t&&(m(0,"span"),M(1,"\u2714"),_())}function J2(t,n){if(1&t&&(m(0,"span",35),M(1),_()),2&t){const e=y().$implicit,r=y(2);p(1),L(r.activeGroupByFields.indexOf(e)+1)}}function eV(t,n){if(1&t){const e=oe();m(0,"li",21),V("click",function(){const o=T(e).$implicit;return x(y(2).toggleGroupOption(o))}),S(1,X2,2,0,"span",33),M(2),S(3,J2,2,1,"span",34),_()}if(2&t){const e=n.$implicit,r=y(2);p(1),b("ngIf",r.activeGroupByFields.includes(e)),p(1),be(" ",e[2]," "),p(1),b("ngIf",r.activeGroupByFields.includes(e))}}function tV(t,n){if(1&t&&(m(0,"ul",31),S(1,eV,4,3,"li",32),_()),2&t){const e=y();p(1),b("ngForOf",e.groupByFields)}}function nV(t,n){if(1&t){const e=oe();m(0,"button",41),V("click",function(){T(e);const i=y(2);return x(i.showFilterSelector=!i.showFilterSelector)}),W(1,"img",42),_()}}function rV(t,n){1&t&&(m(0,"span"),M(1,"\u2714"),_())}function iV(t,n){if(1&t&&(m(0,"span",35),M(1),_()),2&t){const e=y().$implicit,r=y(3);p(1),L(r.activeFilters.indexOf(e)+1)}}function oV(t,n){if(1&t){const e=oe();m(0,"li",21),V("click",function(){const o=T(e).$implicit;return x(y(3).toggleFilterOption(o))}),S(1,rV,2,0,"span",33),M(2),S(3,iV,2,1,"span",34),_()}if(2&t){const e=n.$implicit,r=y(3);p(1),b("ngIf",r.activeFilters.includes(e)),p(1),be(" ",e," "),p(1),b("ngIf",r.activeFilters.includes(e))}}function sV(t,n){if(1&t&&(Be(0),m(1,"ul",43),S(2,oV,4,3,"li",32),_(),je()),2&t){const e=y(2);p(2),b("ngForOf",e.filterTypes)}}const ss=function(t){return{border:t}};function aV(t,n){if(1&t){const e=oe();m(0,"div",44)(1,"div",45),V("click",function(){T(e);const i=y(2);return x(i.filterDrivers("OFFLINE"==i.selectedDriverFilter?"ALL":"OFFLINE"))}),M(2),k(3,"translate"),_(),m(4,"div",46),V("click",function(){T(e);const i=y(2);return x(i.filterDrivers("ONLINE"==i.selectedDriverFilter?"ALL":"ONLINE"))}),M(5),k(6,"translate"),_(),m(7,"div",47),V("click",function(){T(e);const i=y(2);return x(i.filterDrivers("BUSY"==i.selectedDriverFilter?"ALL":"BUSY"))}),M(8),k(9,"translate"),_()()}if(2&t){const e=y(2);p(1),b("ngStyle",Ne(15,ss,"OFFLINE"==e.selectedDriverFilter?"1px solid red":"unset")),p(1),Dt("",R(3,9,"OFFLINE"),"(",e.offlineDriversLength,")"),p(2),b("ngStyle",Ne(17,ss,"ONLINE"==e.selectedDriverFilter?"1px solid green":"unset")),p(1),Dt("",R(6,11,"ONLINE"),"(",e.onlineDriversLength,")"),p(2),b("ngStyle",Ne(19,ss,"BUSY"==e.selectedDriverFilter?"1px solid orange":"unset")),p(1),Dt("",R(9,13,"BUSY"),"(",e.busyDriversLength,")")}}function lV(t,n){if(1&t){const e=oe();m(0,"div",44)(1,"div",46),V("click",function(){return T(e),x(y(2).selectedRouteFilter="DONE")}),M(2),k(3,"translate"),_(),m(4,"div",47),V("click",function(){return T(e),x(y(2).selectedRouteFilter="ACTIVE")}),M(5),k(6,"translate"),_()()}if(2&t){const e=y(2);p(1),b("ngStyle",Ne(10,ss,"DONE"==e.selectedRouteFilter?"1px solid green":"unset")),p(1),Dt("",R(3,6,"DONE_ROUTES"),"(",e.doneRoutes.length,")"),p(2),b("ngStyle",Ne(12,ss,"ACTIVE"==e.selectedRouteFilter?"1px solid orange":"unset")),p(1),Dt("",R(6,8,"ACTIVE_ROUTES"),"(",e.routes.length,")")}}function cV(t,n){if(1&t){const e=oe();m(0,"div",36)(1,"div",37)(2,"input",38),V("ngModelChange",function(i){return T(e),x(y().searchValue=i)})("input",function(i){return T(e),x(y().search(i))}),k(3,"translate"),_(),S(4,nV,2,0,"button",39),S(5,sV,3,1,"ng-container",33),_(),S(6,aV,10,21,"div",40),S(7,lV,7,14,"div",40),_()}if(2&t){const e=y();p(2),Bt("placeholder",R(3,6,"SEARCH")),b("ngModel",e.searchValue),p(2),b("ngIf","orders"===e.activeContent),p(1),b("ngIf",e.showFilterSelector),p(1),b("ngIf","drivers"===e.activeContent),p(1),b("ngIf","routes"===e.activeContent)}}function uV(t,n){1&t&&W(0,"span")}function dV(t,n){1&t&&W(0,"span")}function hV(t,n){if(1&t&&(Be(0),S(1,uV,1,0,"span",33),S(2,dV,1,0,"span",33),je()),2&t){const e=n.$implicit,r=n.index,i=y(2);p(1),b("ngIf",0==r&&i.updateDriversLength()&&i.filterDrivers(i.selectedDriverFilter)),p(1),b("ngIf","rb_delivery.role_driver"===e.role_code)}}function fV(t,n){if(1&t&&(Be(0),m(1,"div"),M(2),k(3,"translate"),k(4,"translate"),_(),je()),2&t){const e=y().$implicit,r=y(2);p(1),ed(e.online&&r.getNumberOfOrders(e,"agent")>=r.maxNumberOfShipments?"busy-btn":e.online?"online-btn":"offline-btn"),p(1),be(" ",e.online&&r.getNumberOfOrders(e,"agent")>=r.maxNumberOfShipments?R(3,3,"BUSY"):R(4,5,e.online?"ONLINE":"OFFLINE")," ")}}const uf=function(t){return{background:t}};function pV(t,n){if(1&t){const e=oe();m(0,"div",51)(1,"div",52),W(2,"img",53),m(3,"span",54),M(4),_(),m(5,"span",54),M(6),_(),S(7,fV,5,7,"ng-container",33),_(),m(8,"div",55)(9,"button",56),V("click",function(){const o=T(e).$implicit;return x(y(2).showAgentOnMap(o,!0))}),M(10),k(11,"translate"),_(),m(12,"button",57),V("click",function(){const o=T(e).$implicit;return x(y(2).editUser(o))}),M(13),k(14,"translate"),_()()()}if(2&t){const e=n.$implicit,r=y(2);p(2),Bt("alt",e.commercial_name),b("src",r.getImageUrl(e.id),xt),p(2),L(e.commercial_name),p(2),L(r.getNumberOfOrders(e,"agent")),p(1),b("ngIf","rb_delivery.role_business"!=r.userRole||r.showDriverStatusInSideMenuForBusiness&&"rb_delivery.role_business"==r.userRole),p(2),b("ngStyle",Ne(12,uf,r.activeAgent&&r.activeAgent.id==e.id?"red":"#3ab600")),p(1),L(R(11,8,r.activeAgent&&r.activeAgent.id==e.id?"DISMISS":"SHOW_ON_MAP")),p(3),L(R(14,10,"EDIT_USER"))}}function gV(t,n){if(1&t&&(m(0,"div",48),S(1,hV,3,2,"ng-container",49),S(2,pV,15,14,"div",50),_()),2&t){const e=y();p(1),b("ngForOf",e.users),p(1),b("ngForOf",e.drivers)}}function mV(t,n){if(1&t){const e=oe();m(0,"div",51)(1,"div",52)(2,"span",54),M(3),_(),m(4,"span",54),M(5),k(6,"translate"),_(),m(7,"span",54),M(8),_()(),m(9,"div",55)(10,"button",56),V("click",function(){const o=T(e).$implicit;return x(y(2).showRouteOnMap(o,!0))}),M(11),k(12,"translate"),_()()()}if(2&t){const e=n.$implicit,r=y(2);p(3),L(r.getLocalDateTime(e.create_date)),p(2),Dt(""," /"+r.getNumberOfOrders(e,"route")," ",R(6,6,"ORDERS"),""),p(3),L(e.driver_id[1]),p(2),b("ngStyle",Ne(10,uf,r.activeRoute&&r.activeRoute.id==e.id?"red":"#3ab600")),p(1),L(R(12,8,r.activeRoute&&r.activeRoute.id==e.id?"DISMISS":"SHOW_ON_MAP"))}}function _V(t,n){if(1&t&&(m(0,"div",48),S(1,mV,13,12,"div",50),_()),2&t){const e=y();p(1),b("ngForOf","ACTIVE"==e.selectedRouteFilter?e.routes:e.doneRoutes)}}function vV(t,n){if(1&t){const e=oe();m(0,"button",57),V("click",function(){T(e);const i=y(2).$implicit,o=y(2);return o.showDriversInRange(i),x(o.showSenderOnMap(i))}),M(1),k(2,"translate"),_()}2&t&&(p(1),L(R(2,1,"SHOW_DRIVERS_IN_RANGE")))}function yV(t,n){if(1&t){const e=oe();m(0,"div",51)(1,"div",52),W(2,"img",53),m(3,"span",54),M(4),_(),m(5,"span",54),M(6),_()(),m(7,"div",55)(8,"button",56),V("click",function(){T(e);const i=y().$implicit;return x(y(2).showSenderOnMap(i,!0))}),M(9),k(10,"translate"),_(),S(11,vV,3,3,"button",59),m(12,"button",57),V("click",function(){T(e);const i=y().$implicit;return x(y(2).editUser(i))}),M(13),k(14,"translate"),_()()()}if(2&t){const e=y().$implicit,r=y(2);p(2),Bt("alt",e.commercial_name),b("src",r.getImageUrl(e.id),xt),p(2),L(e.commercial_name),p(2),L(r.getNumberOfOrders(e,"business")),p(2),b("ngStyle",Ne(12,uf,r.activeSender&&r.activeSender.id==e.id?"red":"#3ab600")),p(1),L(R(10,8,r.activeSender&&r.activeSender.id==e.id?"DISMISS":"SHOW_ON_MAP")),p(2),b("ngIf",(!r.activeSender||r.activeSender.id!=e.id)&&e.longitude&&e.latitude&&"rb_delivery.role_business"!=r.userRole),p(2),L(R(14,10,"EDIT_USER"))}}function CV(t,n){if(1&t&&(Be(0),S(1,yV,15,14,"div",58),je()),2&t){const e=n.$implicit;p(1),b("ngIf","rb_delivery.role_business"===e.role_code)}}function DV(t,n){if(1&t&&(m(0,"div",48),Be(1),S(2,CV,2,1,"ng-container",49),je(),_()),2&t){const e=y();p(2),b("ngForOf",e.users)}}function bV(t,n){1&t&&(m(0,"div",60)(1,"div"),M(2),k(3,"translate"),_()()),2&t&&(p(2),L(R(3,1,"PLEASE_CHOOSE_A_CATOGARY_TO_SEE_FILTERS_HERE")))}function wV(t,n){if(1&t){const e=oe();m(0,"app-group-container",61),V("showStatusList",function(i){return T(e),x(y().showStatusList(i))})("showDriverList",function(i){return T(e),x(y().showDriverList(i))})("showOrderOnMap",function(i){return T(e),x(y().showOrderOnMap(i,!0))})("showDriversInRange",function(i){return T(e),x(y().showDriversInRange(i))})("selectedGroupOrders",function(i){return T(e),x(y().handleSelectedGroupOrders(i))}),_()}if(2&t){const e=y();b("groupSet",e.groupingRecordsSet[0])("level",0)("parentOrders",e.orders)("groupingRecordsSet",e.groupingRecordsSet)("aciveOrder",e.aciveOrder)("userRole",e.userRole)("nearestDriverTimeOut",e.nearestDriverTimeOut)("checkedOrderIds",e.checkedOrders)}}function EV(t,n){1&t&&(m(0,"div",62),M(1),k(2,"translate"),_()),2&t&&(p(1),be(" ",R(2,1,"LOADING")," "))}let MV=(()=>{class t{constructor(e){this.translate=e,this.selectedSubArea=null,this.users=[],this.routes=[],this.doneRoutes=[],this.orders=[],this.statuses=[],this.subAreas=[],this.groupByFields=[],this.activeGroupByFields=[],this.activeFilters=[],this.timeDurationConfig=0,this.maxNumberOfShipments=0,this.filterTexts=[""],this.onClickAgent=new B,this.onClickAssignDriver=new B,this.onClickChooseStatus=new B,this.onClickShowOnMap=new B,this.onClickShowDriversInRange=new B,this.onSearch=new B,this.onActiveContentChange=new B,this.onAction=new B,this.onShowToast=new B,this.searchDebouncer=new te,this.onQuickOrderDismiss=new B,this.onClickFilter=new B,this.onGroupOrdersChecked=new B,this.dispatcherOrderFilters={},this.orderDefaultFilters={},this.showFilters=!1,this.showContentSelector=!1,this.showActionSelector=!1,this.showGroupSelector=!1,this.showFilterSelector=!1,this.selectedFilters=["DEFAULT"],this.selectedDriverFilter="ONLINE",this.selectedRouteFilter="ACTIVE",this.filterTypes=[],this.activeContent="orders",this.isFilteredOnOrders=!1,this.loading=!1,this.driversToSelect=[],this.groupingRecordsSet=[],this.searchValue="",this.nearestDriverTimeOut=0,this.offlineDriversLength=0,this.onlineDriversLength=0,this.busyDriversLength=0,this.drivers=[],this.checkedOrders=[],this.showDriverStatusInSideMenuForBusiness=!1}ngOnChanges(e){e.orders&&this.orders&&(0==this.activeGroupByFields.length&&(this.activeGroupByFields=this.groupByFields.filter(r=>r[3]),0==this.activeGroupByFields.length&&(this.activeGroupByFields=[this.groupByFields[0]])),this.groupRecords())}ngOnInit(){this.activeFilters=Object.keys(this.orderDefaultFilters),this.showContent("rb_delivery.role_business"==this.userRole?"senders":"orders"),this.searchListener()}toggleGroupOption(e){this.activeGroupByFields.length>1&&this.activeGroupByFields.includes(e)?this.activeGroupByFields=this.activeGroupByFields.filter(r=>e!=r):this.activeGroupByFields.includes(e)?this.onShowToast.emit({text:this.translate.instant("MUST_KEEP_AT_LEAST_ONE_GROUPING_OPTION"),type:"fail"}):this.activeGroupByFields.push(e),this.groupRecords()}toggleFilterOption(e){this.showFilterSelector=!0,this.activeFilters.includes(e)?(this.activeFilters=this.activeFilters.filter(r=>e!=r),this.selectFilter(this.activeFilters)):this.activeFilters.includes(e)||(this.activeFilters.push(e),this.selectFilter(this.activeFilters))}groupRecords(){this.groupingRecordsSet=[];for(let e of this.activeGroupByFields){let r=[],i=[],o=[];this.orders.forEach(s=>{"many2one"!=e[1]||i.includes(s[e[0]][0])?"many2one"==e[1]||"date"==e[1]||"datetime"==e[1]||i.includes(s[e[0]])?("date"==e[1]||"datetime"==e[1]&&!i.includes(s[e[0]].split(" ")[0]))&&(r.push(s[e[0]].split(" ")[0]),i.push(s[e[0]].split(" ")[0])):(r.push(s[e[0]]),i.push(s[e[0]])):(r.push(s[e[0]]),i.push(s[e[0]][0]))});for(let s of r)o.push({id:"object"==typeof s?s[0]:s,label:"object"==typeof s?s[1]:s,field:e});this.groupingRecordsSet.push(o)}}handleSelectedGroupOrders(e){this.showContentSelector=!1,this.showGroupSelector=!1,this.showActionSelector=!1,this.checkedOrders=e,this.onGroupOrdersChecked.emit(this.checkedOrders)}assignAgentAction(e){this.onAction.emit({action:"assign_agent",values:{assign_to_agent:e.assign_to_agent}}),this.checkedOrders=[],this.handleSelectedGroupOrders([]),this.showAgentList=!1}showAssignAgent(){this.onClickAssignDriver.emit(),this.showAgentList=!0}closeDriverList(){this.showAgentList=!1}searchListener(){this.searchDebouncer.pipe(function s2(t,n=wl){return Je((e,r)=>{let i=null,o=null,s=null;const a=()=>{if(i){i.unsubscribe(),i=null;const c=o;o=null,r.next(c)}};function l(){const c=s+t,u=n.now();if(u<c)return i=this.schedule(void 0,c-u),void r.add(i);a()}e.subscribe(Ge(r,c=>{o=c,s=n.now(),i||(i=n.schedule(l,t),r.add(i))},()=>{a(),r.complete()},void 0,()=>{o=i=null}))})}(1e3)).subscribe(e=>{this.onSearch.emit(e),this.drivers=[]})}search(e){this.searchDebouncer.next(e.target?.value?e.target?.value:"")}getImageUrl(e){return`${window.location.origin}/web/image/rb_delivery.user/${e}/user_image/50x50`}showContent(e){if(this.showContentSelector=!1,"senders"==e&&"BUSINESSES"!=this.activeContentLabel)this.filterTypes=["DEFAULT","ALL","ONLINE_BUSINESSES","OFFLINE_BUSINESSES"],this.activeContentLabel="BUSINESSES";else if("orders"==e&&"ORDERS"!=this.activeContentLabel)this.filterTypes=["TODAY_ORDERS",...Object.keys(this.dispatcherOrderFilters)],this.activeContentLabel="ORDERS";else if("drivers"==e&&"DRIVERS"!=this.activeContentLabel)this.activeContentLabel="DRIVERS",this.filterTypes=[];else if("routes"==e&&"ROUTES"!=this.activeContentLabel)this.activeContentLabel="ROUTES",this.filterTypes=[];else{if("all"!=e||"ALL"==this.activeContentLabel)return;this.activeContentLabel="ALL",this.filterTypes=[]}this.activeContent=e,this.filterTexts=[""],this.onActiveContentChange.emit(e),this.checkedOrders=[],this.searchValue="",this.selectedFilters=["DEFAULT"]}getKeys(e){return Object.keys(e)}showAgentOnMap(e,r){this.onClickShowOnMap.emit({agent:e,showOnlyRelatedMarkers:r})}showRouteOnMap(e,r){this.onClickShowOnMap.emit({route:e,showOnlyRelatedMarkers:r})}showSenderOnMap(e,r){this.onClickShowOnMap.emit({sender:e,showOnlyRelatedMarkers:r})}showDriverList(e){this.onClickAssignDriver.emit(e)}showStatusList(e){this.onClickChooseStatus.emit(e)}showOrderOnMap(e,r){this.onClickShowOnMap.emit({order:e,showOnlyRelatedMarkers:r})}toggleQuickOrder(){this.onQuickOrderDismiss.emit(!0)}startNearestDriverTimer(e){if(this.isTimerStarted||!e||0==e)return!1;this.nearestDriverTimeOut=this.timeDurationConfig-e;let r=setInterval(()=>{this.nearestDriverTimeOut>0?(this.isTimerStarted=!0,this.nearestDriverTimeOut-=1):(this.isTimerStarted=!1,clearInterval(r))},1e3);return!0}getOrdersByField(e,r,i=this.orders){return i.filter(o=>o[r][0]===e)}getNumberOfOrdersByField(e,r,i=this.orders){return i.filter(o=>o[r][0]===e).length}editUser(e){window.open("http://"+window.location.hostname+"/web#id="+e.id+"&model=rb_delivery.user&view_type=form")}showDriversInRange(e){this.onClickShowDriversInRange.emit(new google.maps.LatLng({lat:Number(e.latitude),lng:Number(e.longitude)}))}toggleFilterDropdown(){this.showFilters=!this.showFilters}selectFilter(e){this.selectedFilters=e,this.filterTexts=e,this.showFilters=!1,this.onClickFilter.emit(e)}clearFilter(){this.selectedFilters=["DEFAULT"],this.onClickFilter.emit(["DEFAULT"])}updateDriversLength(){return this.filterDrivers("OFFLINE",!1),this.filterDrivers("ONLINE",!1),this.filterDrivers("BUSY",!1),!0}clearOrdersFilter(){this.onActiveContentChange.emit("orders"),this.checkedOrders=[]}filterDrivers(e,r=!0){return r&&(this.selectedDriverFilter=e),"OFFLINE"==e?(this.offlineDriversLength=0,this.drivers=this.users.filter(i=>"rb_delivery.role_driver"==i.role_code&&!i.online&&(this.offlineDriversLength+=1,!0))):"ONLINE"==e?(this.onlineDriversLength=0,this.drivers=this.users.filter(i=>!!("rb_delivery.role_driver"==i.role_code&&i.online&&this.getNumberOfOrders(i,"agent")<this.maxNumberOfShipments)&&(this.onlineDriversLength+=1,!0))):"BUSY"==e?(this.busyDriversLength=0,this.drivers=this.users.filter(i=>!!("rb_delivery.role_driver"==i.role_code&&i.online&&this.getNumberOfOrders(i,"agent")>=this.maxNumberOfShipments)&&(this.busyDriversLength+=1,!0))):this.drivers=this.users.filter(i=>"rb_delivery.role_driver"==i.role_code),!0}getAgentMobile(e){return e.agent_commercial_number?e.agent_commercial_number:!!e.agent_mobile_number&&e.agent_mobile_number}getNumberOfOrders(e,r){return"business"==r?e.dispatcher_business_orders.length:"agent"==r?e.nearest_driver_orders.length:"status"==r?this.orders.filter(i=>i.state_id[0]==e).length:"route"==r?e.order_ids.length?e.order_ids.length:"string"==typeof e.done_locations?JSON.parse(e.done_locations).length:e.done_locations.length:"subArea"==r?this.orders.filter(i=>i.customer_sub_area[0]==e).length:0}getLocalDateTime(e){const r=new Date(e),i=r.getTimezoneOffset();return new Date(r.getTime()-6e4*i).toLocaleString(void 0,{month:"2-digit",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1})}static#e=this.\u0275fac=function(r){return new(r||t)(C(Cr))};static#t=this.\u0275cmp=$t({type:t,selectors:[["app-side-menu-component"]],inputs:{activeAgent:"activeAgent",activeRoute:"activeRoute",activeSender:"activeSender",aciveOrder:"aciveOrder",users:"users",routes:"routes",doneRoutes:"doneRoutes",orders:"orders",statuses:"statuses",subAreas:"subAreas",groupByFields:"groupByFields",sessionId:"sessionId",timeDurationConfig:"timeDurationConfig",maxNumberOfShipments:"maxNumberOfShipments",userRole:"userRole",currentOrder:"currentOrder",filterTexts:"filterTexts",dispatcherOrderFilters:"dispatcherOrderFilters",orderDefaultFilters:"orderDefaultFilters",activeContent:"activeContent",isFilteredOnOrders:"isFilteredOnOrders",loading:"loading",driversToSelect:"driversToSelect",showDriverStatusInSideMenuForBusiness:"showDriverStatusInSideMenuForBusiness"},outputs:{onClickAgent:"onClickAgent",onClickAssignDriver:"onClickAssignDriver",onClickChooseStatus:"onClickChooseStatus",onClickShowOnMap:"onClickShowOnMap",onClickShowDriversInRange:"onClickShowDriversInRange",onSearch:"onSearch",onActiveContentChange:"onActiveContentChange",onAction:"onAction",onShowToast:"onShowToast",onQuickOrderDismiss:"onQuickOrderDismiss",onClickFilter:"onClickFilter",onGroupOrdersChecked:"onGroupOrdersChecked"},features:[Qe],decls:27,vars:19,consts:[["id","sideMenu",1,"side-menu","container"],[1,"flex-space-between"],[1,"button-select","button-dropdown","active",3,"click"],[1,"icon"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/orders.svg","alt","icon",1,"button-icon"],[1,"icon",2,"zoom",".5"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/down-select.svg","alt","icon",1,"button-icon"],["class","active-content-dropdown",4,"ngIf"],[1,"separator"],[1,"button-base","button-orange",3,"click"],["class","button-select button-dropdown active","style","margin-inline-start: 15px;",3,"disabled","click",4,"ngIf"],["class","active-action-dropdown",4,"ngIf"],["class","app-driver-container",3,"driversToSelect","currentOrder","hideStatusSelector","loading","ngStyle","onClickDriver","onClickShowOnMap","onClose",4,"ngIf"],["class","button-select button-dropdown active","style","margin-inline-start: 15px; min-width: fit-content !important;",3,"click",4,"ngIf"],["class","active-group-dropdown",4,"ngIf"],["class","dropdown-container",4,"ngIf"],["style","padding: 15px; background: #F3E8E2;overflow: scroll; height: calc(100vh - 156px)",4,"ngIf"],["style","padding: 15px; background: transparent;overflow: scroll; height: calc(100vh - 156px);display: flex; flex-direction: column; gap: 10px;align-items: center;justify-content: center;",4,"ngIf"],[3,"groupSet","level","parentOrders","groupingRecordsSet","aciveOrder","userRole","nearestDriverTimeOut","checkedOrderIds","showStatusList","showDriverList","showOrderOnMap","showDriversInRange","selectedGroupOrders",4,"ngIf"],["style","text-align: center;",4,"ngIf"],[1,"active-content-dropdown"],[3,"click"],[3,"click",4,"ngIf"],[1,"button-select","button-dropdown","active",2,"margin-inline-start","15px",3,"disabled","click"],["style","position: absolute ; top: -8px;font-weight: bold; font-size: 12px; align-self: center; color: white; background: #D97348; border-radius: 10px; padding: 2px 5px;",4,"ngIf"],[2,"position","absolute","top","-8px","font-weight","bold","font-size","12px","align-self","center","color","white","background","#D97348","border-radius","10px","padding","2px 5px"],[1,"active-action-dropdown"],[1,"app-driver-container",3,"driversToSelect","currentOrder","hideStatusSelector","loading","ngStyle","onClickDriver","onClickShowOnMap","onClose"],[1,"button-select","button-dropdown","active",2,"margin-inline-start","15px","min-width","fit-content !important",3,"click"],[1,"icon",2,"margin-inline","14px !important"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/group-by.svg","alt","icon",1,"button-icon"],[1,"active-group-dropdown"],[3,"click",4,"ngFor","ngForOf"],[4,"ngIf"],["class","tag-right",4,"ngIf"],[1,"tag-right"],[1,"dropdown-container"],[1,"input-wrapper"],["type","text",1,"dropdown-btn","input-with-filter",2,"border","1px solid #E3E3E3",3,"ngModel","placeholder","ngModelChange","input"],["class","filter-btn-inside-input",3,"click",4,"ngIf"],["class","flex-space-between","style","margin-inline: 10px;",4,"ngIf"],[1,"filter-btn-inside-input",3,"click"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/filter.svg","alt","filter",1,"button-icon"],[1,"active-filter-dropdown"],[1,"flex-space-between",2,"margin-inline","10px"],[2,"color","red","padding","5px","border-radius","5px","cursor","pointer",3,"ngStyle","click"],[2,"color","green","padding","5px","border-radius","5px","cursor","pointer",3,"ngStyle","click"],[2,"color","orange","padding","5px","border-radius","5px","cursor","pointer",3,"ngStyle","click"],[2,"padding","15px","background","#F3E8E2","overflow","scroll","height","calc(100vh - 156px)"],[4,"ngFor","ngForOf"],["class","list-item",4,"ngFor","ngForOf"],[1,"list-item"],[1,"user-container"],[3,"src","alt"],[2,"width","45vw"],[2,"background-color","white","display","flex","flex-direction","row","padding","5px","gap","5px"],[1,"select-btn",3,"ngStyle","click"],[1,"select-btn",3,"click"],["class","list-item",4,"ngIf"],["class","select-btn",3,"click",4,"ngIf"],[2,"padding","15px","background","transparent","overflow","scroll","height","calc(100vh - 156px)","display","flex","flex-direction","column","gap","10px","align-items","center","justify-content","center"],[3,"groupSet","level","parentOrders","groupingRecordsSet","aciveOrder","userRole","nearestDriverTimeOut","checkedOrderIds","showStatusList","showDriverList","showOrderOnMap","showDriversInRange","selectedGroupOrders"],[2,"text-align","center"]],template:function(r,i){1&r&&(m(0,"div",0)(1,"div",1)(2,"button",2),V("click",function(){return i.showGroupSelector=!1,i.showActionSelector=!1,i.showContentSelector=!i.showContentSelector}),m(3,"span",3),W(4,"img",4),_(),m(5,"span"),M(6),k(7,"translate"),_(),m(8,"span",5),W(9,"img",6),_()(),S(10,U2,12,12,"ul",7),W(11,"div",8),m(12,"button",9),V("click",function(){return i.toggleQuickOrder()}),M(13),k(14,"translate"),_(),S(15,G2,7,7,"button",10),S(16,W2,4,3,"ul",11),S(17,Q2,1,9,"app-driver-list",12),S(18,Y2,3,0,"button",13),S(19,tV,2,1,"ul",14),_(),S(20,cV,8,8,"div",15),S(21,gV,3,2,"div",16),S(22,_V,2,1,"div",16),S(23,DV,3,1,"div",16),S(24,bV,4,3,"div",17),S(25,wV,1,8,"app-group-container",18),S(26,EV,3,3,"div",19),_()),2&r&&(p(6),L(R(7,15,i.activeContentLabel)),p(4),b("ngIf",i.showContentSelector),p(3),L(R(14,17,"QUICK_ORDER")),p(2),b("ngIf","orders"===i.activeContent),p(1),b("ngIf",i.showActionSelector),p(1),b("ngIf",i.showAgentList&&i.showActionSelector&&i.checkedOrders.length>0&&!i.currentOrder),p(1),b("ngIf","orders"===i.activeContent),p(1),b("ngIf",i.showGroupSelector),p(1),b("ngIf","all"!=i.activeContent),p(1),b("ngIf","drivers"===i.activeContent),p(1),b("ngIf","routes"===i.activeContent),p(1),b("ngIf","senders"===i.activeContent),p(1),b("ngIf","all"===i.activeContent),p(1),b("ngIf","orders"===i.activeContent&&i.orders.length>0),p(1),b("ngIf",i.loading))},dependencies:[Ii,Rn,_r,br,$o,Xo,Tw,B2,Yn],styles:['.container[_ngcontent-%COMP%]{display:flex;flex-direction:column;background-color:#fcefe7;border-radius:10px}.flex-space-between[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:10px;padding-inline:23px;padding-top:22px}.app-driver-container[_ngcontent-%COMP%]{flex-direction:column;display:flex;position:absolute;left:570px;background:white;border-radius:10px;box-shadow:0 0 10px #ccc;width:20vw;overflow:scroll;max-height:40vh;top:247px}.button-base[_ngcontent-%COMP%]{flex:1;height:50px!important;border-radius:10px;border:none;cursor:pointer;background-color:#fcefe7;height:4rem}.button-orange[_ngcontent-%COMP%]{background-color:#e55604;color:#fff}.dropdown-container[_ngcontent-%COMP%]{margin:10px 0;position:relative}.filter-btn-inside-input[_ngcontent-%COMP%]{position:absolute;right:28px;top:50%;transform:translateY(-50%);background:transparent;border:none;cursor:pointer}.dropdown-btn[_ngcontent-%COMP%]{width:100%;height:44px;border-radius:10px;padding-inline:10px;background-color:#fff;border:none;cursor:pointer}.input-filter[_ngcontent-%COMP%]{margin-top:20px;padding:5px;width:100%;box-sizing:border-box}.user-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin:10px 0;padding:10px}.order-item-content[_ngcontent-%COMP%]{display:grid;padding:10px;background-color:#fff;grid-template-columns:35% 30% 35%}.order-item-header[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;padding-inline:10px;background:#e55604}.courier-img[_ngcontent-%COMP%]{width:570px;height:70px;border-radius:10px}.select-btn[_ngcontent-%COMP%]{width:100%;height:20px;border-radius:10px;font-size:13px;background-color:#3ab600;border:none;cursor:pointer;color:#fff}.button-base.active[_ngcontent-%COMP%]{background-color:#fff;color:#e55604}.side-menu.container[_ngcontent-%COMP%]{position:absolute;height:100vh;width:570px;left:0;direction:ltr}.online-btn[_ngcontent-%COMP%]{color:#fff;background-color:green;width:14vw;border-radius:15px;margin:0 6px;padding-inline:5px}.busy-btn[_ngcontent-%COMP%]{color:#fff;background-color:orange;width:14vw;border-radius:15px;margin:0 6px;padding-inline:5px}.offline-btn[_ngcontent-%COMP%]{color:#fff;background-color:red;width:14vw;border-radius:15px;margin:0 6px;padding-inline:5px}.list-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;box-shadow:0 0 10px #ccc;border-radius:10px;overflow:hidden;margin:10px 0;background-color:#fff}.dropdown-container[_ngcontent-%COMP%]{position:relative}.dropdown-btn[_ngcontent-%COMP%]{width:100%;padding-inline-end:15px}.filter-dropdown[_ngcontent-%COMP%]{position:absolute;inset-inline-end:10px;top:100%;list-style:none;margin:0;z-index:1;width:40%;background-color:#fff;border-radius:10px;box-shadow:0 1px 3px #0000001f,0 1px 2px #0000003d,0 2px 4px #0000002e;padding:10px}.filter-dropdown[_ngcontent-%COMP%]:before{content:"";position:absolute;top:-10px;right:20px;width:0;height:0;border-left:10px solid transparent;border-right:10px solid transparent;border-bottom:10px solid white}.filter-dropdown[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{padding:5px;cursor:pointer;background-color:#fff;text-align:start;margin:5px 0;border-radius:5px}.filter-dropdown[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover{background:#f3f3f3}.applied-filter[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%]{color:red}.clear-filter-btn[_ngcontent-%COMP%]{position:absolute;background:none;border:none;cursor:pointer;font-weight:700;color:#fff;inset-inline-end:52px}.input-wrapper[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;padding-inline:23px;margin-bottom:10px}.input-with-filter[_ngcontent-%COMP%]{width:100%;padding-right:50px}.dropdown-btn[_ngcontent-%COMP%]{flex-grow:1;border:none;outline:none;padding-inline-end:50px}.filter-filter[_ngcontent-%COMP%]{position:absolute;inset-inline-end:30px;background:none;border:none;cursor:pointer}.x-button[_ngcontent-%COMP%]{cursor:pointer;background:red;display:inline-block;border-radius:50%;width:15px;height:15px}.applied-filter[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:hue-rotate(0deg) brightness(50%)}.item-row[_ngcontent-%COMP%]{display:flex;font-size:14px;margin-block:5px;align-self:center}.row-col[_ngcontent-%COMP%]{display:flex;font-size:14px;margin-block:5px;gap:5px;align-items:center}.item-col[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between;font-size:14px}.icon[_ngcontent-%COMP%]{width:24px}.fade_animation[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeOut .5s forwards}@keyframes _ngcontent-%COMP%_fadeOut{0%{opacity:100;transform:translate(0)}to{opacity:0;transform:translate(400px)}}.active-content-dropdown[_ngcontent-%COMP%]{background-color:#fff;list-style:none;padding:10px;margin:0;border-radius:10px;border:1px solid #E3E3E3;box-shadow:0 4px 6px #0000001a;position:absolute;top:75px;left:23px;z-index:1000;width:200px}.active-action-dropdown[_ngcontent-%COMP%]{background-color:#fff;list-style:none;padding:10px;margin:0;border-radius:10px;border:1px solid #E3E3E3;box-shadow:0 4px 6px #0000001a;position:absolute;top:75px;right:42px;z-index:1000;width:200px}.active-group-dropdown[_ngcontent-%COMP%]{background-color:#fff;list-style:none;padding:10px;margin:0;border-radius:10px;border:1px solid #E3E3E3;box-shadow:0 4px 6px #0000001a;position:absolute;top:75px;right:23px;z-index:1000;width:200px}.active-filter-dropdown[_ngcontent-%COMP%]{background-color:#fff;list-style:none;padding:10px;margin:0;border-radius:10px;border:1px solid #E3E3E3;box-shadow:0 4px 6px #0000001a;position:absolute;top:33px;right:0;z-index:1000;width:200px}li[_ngcontent-%COMP%]{padding:8px 12px;cursor:pointer;transition:background-color .3s ease}li[_ngcontent-%COMP%]:hover{background-color:#f0f0f0}.button-select[_ngcontent-%COMP%]{display:flex;align-items:center;border-radius:10px;background-color:#fff;color:#000;border:1px solid #E3E3E3;font-size:16px;cursor:pointer;position:relative;transition:box-shadow .3s ease}.button-select[_ngcontent-%COMP%]:hover{box-shadow:0 4px 6px #0000001a}.button-dropdown[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-around;min-width:150px;padding:0!important}.button-icon[_ngcontent-%COMP%]{width:24px;height:24px}.dropdown-arrow[_ngcontent-%COMP%]{margin-left:8px;font-size:14px;color:#000}.separator[_ngcontent-%COMP%]{width:1px;height:80%;background-color:#e0e0e0;margin-inline:15px;margin-block:auto}.tag-right[_ngcontent-%COMP%]{position:absolute;right:10px;border-radius:50%;background-color:coral;width:18px;height:18px;text-align:center;color:#fff}.disabled[_ngcontent-%COMP%]{background-color:#ccc;color:#999;cursor:not-allowed;opacity:.7}'],changeDetection:0})}return t})();function IV(t,n){if(1&t){const e=oe();m(0,"div",2),V("click",function(){return T(e),x(y().emitPickupOverdueClick())}),m(1,"p",3),M(2),k(3,"translate"),m(4,"span",4),M(5),_()()()}if(2&t){const e=y();p(2),be(" ",R(3,2,"PICKUP_OVERDUE")," "),p(3),L(e.pickupOverdueOrders.length)}}function SV(t,n){if(1&t){const e=oe();m(0,"div",2),V("click",function(){return T(e),x(y().emitDeliveryOverdueClick())}),m(1,"p",3),M(2),k(3,"translate"),m(4,"span",4),M(5),_()()()}if(2&t){const e=y();p(2),be(" ",R(3,2,"DELIVERY_OVERDUE")," "),p(3),L(e.deliveryOverdueOrders.length)}}let OV=(()=>{class t{constructor(){this.onClickDeliveryOverdueOrders=new B,this.onClickPickupOverdueOrders=new B,this.deliveryOverdueOrdersLength=0,this.pickupOverdueOrdersLength=0}ngOnInit(){}emitPickupOverdueClick(){this.pickupOverdueOrdersLength=this.pickupOverdueOrders.length,this.onClickPickupOverdueOrders.emit(this.pickupOverdueOrders[this.pickupOverdueOrders.length-1])}emitDeliveryOverdueClick(){this.deliveryOverdueOrdersLength=this.deliveryOverdueOrders.length,this.onClickDeliveryOverdueOrders.emit(this.deliveryOverdueOrders[this.deliveryOverdueOrders.length-1])}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275cmp=$t({type:t,selectors:[["app-map-dashboard"]],inputs:{deliveryOverdueOrders:"deliveryOverdueOrders",pickupOverdueOrders:"pickupOverdueOrders"},outputs:{onClickDeliveryOverdueOrders:"onClickDeliveryOverdueOrders",onClickPickupOverdueOrders:"onClickPickupOverdueOrders"},decls:3,vars:2,consts:[[1,"dahboard-item-container"],["class","dahboard-item-box",3,"click",4,"ngIf"],[1,"dahboard-item-box",3,"click"],[1,"dahboard-item-title-danger"],[2,"font-weight","bolder"]],template:function(r,i){1&r&&(m(0,"div",0),S(1,IV,6,4,"div",1),S(2,SV,6,4,"div",1),_()),2&r&&(p(1),b("ngIf",i.pickupOverdueOrders),p(1),b("ngIf",i.deliveryOverdueOrders))},dependencies:[Rn,Yn],styles:[".dahboard-item-container[_ngcontent-%COMP%]{position:absolute;left:590px;top:15px;display:flex;flex-direction:row;gap:10px}.dahboard-item-box[_ngcontent-%COMP%]{box-shadow:0 0 10px #ccc;padding:10px;background:white;border-radius:10px;display:flex;flex-direction:row;gap:10px}.dahboard-item-title-danger[_ngcontent-%COMP%]{color:red;margin:0;cursor:pointer}.dahboard-item-new-badge[_ngcontent-%COMP%]{color:#fff;background-color:red;border-radius:10px;margin:0;padding-inline:5px}"]})}return t})();function TV(t,n){1&t&&W(0,"option",22),2&t&&b("value",y().$implicit.username)}function xV(t,n){if(1&t&&(Be(0),S(1,TV,1,1,"option",21),je()),2&t){const e=n.$implicit;p(1),b("ngIf","rb_delivery.role_business"==e.role_code)}}function AV(t,n){if(1&t&&(m(0,"div",23),M(1),k(2,"translate"),_()),2&t){const e=y();p(1),be(" ",R(2,1,e.errorMessageBusiness)," ")}}function kV(t,n){1&t&&W(0,"option",22),2&t&&b("value",y().$implicit.username)}function RV(t,n){if(1&t&&(Be(0),S(1,kV,1,1,"option",21),je()),2&t){const e=n.$implicit;p(1),b("ngIf","rb_delivery.role_driver"==e.role_code)}}function PV(t,n){if(1&t&&(m(0,"div",23),M(1),k(2,"translate"),_()),2&t){const e=y(2);p(1),be(" ",R(2,1,e.errorMessageDriver)," ")}}function NV(t,n){if(1&t&&(m(0,"div",1)(1,"label"),M(2),k(3,"translate"),_(),m(4,"div",1),W(5,"input",24),m(6,"datalist",25),S(7,RV,2,1,"ng-container",4),_()(),S(8,PV,3,3,"div",5),_()),2&t){const e=y();p(2),be("",R(3,3,"SELECT_DRIVER"),":"),p(5),b("ngForOf",e.users),p(1),b("ngIf",e.errorMessageDriver)}}function FV(t,n){if(1&t&&(Be(0),W(1,"option",22),k(2,"translate"),je()),2&t){const e=n.$implicit;p(1),b("value",R(2,1,e[1]))}}function LV(t,n){if(1&t&&(m(0,"div",23),M(1),k(2,"translate"),_()),2&t){const e=y();p(1),be(" ",R(2,1,e.errorMessageVehicleTypes)," ")}}function VV(t,n){1&t&&(m(0,"div",23),M(1),k(2,"translate"),_()),2&t&&(p(1),be(" ",R(2,1,"CUSTOMER_NAME_REQUIRED")," "))}function BV(t,n){1&t&&(m(0,"div",23),M(1),k(2,"translate"),_()),2&t&&(p(1),be(" ",R(2,1,"CUSTOMER_MOBILE_NUMBER_INVALID")," "))}function jV(t,n){if(1&t&&(Be(0),m(1,"div",1)(2,"h3"),M(3),k(4,"translate"),_(),W(5,"input",26),k(6,"translate"),S(7,VV,3,3,"div",5),W(8,"input",27),k(9,"translate"),S(10,BV,3,3,"div",5),_(),je()),2&t){const e=y();let r,i;p(3),L(R(4,5,"CUSTOMER_DETAILS")),p(2),Bt("placeholder",R(6,7,"CUSTOMER_NAME")),p(2),b("ngIf",(null==(r=e.quickOrderForm.get("customer_name"))?null:r.invalid)&&(null==(r=e.quickOrderForm.get("customer_name"))?null:r.touched)),p(1),Bt("placeholder",R(9,9,"CUSTOMER_MOBILE")),p(2),b("ngIf",(null==(i=e.quickOrderForm.get("customer_mobile"))?null:i.invalid)&&(null==(i=e.quickOrderForm.get("customer_mobile"))?null:i.touched))}}function HV(t,n){1&t&&W(0,"option",22),2&t&&b("value",n.$implicit.name)}function zV(t,n){if(1&t&&(m(0,"div",23),M(1),k(2,"translate"),_()),2&t){const e=y();p(1),be(" ",R(2,1,e.errorMessageArea)," ")}}function UV(t,n){1&t&&W(0,"option",22),2&t&&b("value",n.$implicit.name)}function $V(t,n){if(1&t&&(m(0,"div",23),M(1),k(2,"translate"),_()),2&t){const e=y();p(1),be(" ",R(2,1,e.errorMessageSubArea)," ")}}function GV(t,n){1&t&&(m(0,"div",23),M(1),k(2,"translate"),_()),2&t&&(p(1),be(" ",R(2,1,"PAYMENTS_INVALID")," "))}function WV(t,n){1&t&&(m(0,"div",23),M(1),k(2,"translate"),_()),2&t&&(p(1),be(" ",R(2,1,"EXTRA_COST_INVALID")," "))}function qV(t,n){if(1&t&&(Be(0),W(1,"input",29),k(2,"translate"),S(3,WV,3,3,"div",5),je()),2&t){const e=y(2);let r;p(1),Bt("placeholder",R(2,2,"EXTRA_COST")),p(2),b("ngIf",(null==(r=e.quickOrderForm.get("extra_cost"))?null:r.invalid)&&(null==(r=e.quickOrderForm.get("extra_cost"))?null:r.touched))}}function ZV(t,n){if(1&t&&(Be(0),m(1,"div",1)(2,"h3"),M(3),k(4,"translate"),_(),W(5,"input",28),k(6,"translate"),S(7,GV,3,3,"div",5),S(8,qV,4,4,"ng-container",9),_(),je()),2&t){const e=y();let r;p(3),L(R(4,4,"PAYMENTS")),p(2),Bt("placeholder",R(6,6,"COST")),p(2),b("ngIf",(null==(r=e.quickOrderForm.get("cost"))?null:r.invalid)&&(null==(r=e.quickOrderForm.get("cost"))?null:r.touched)),p(1),b("ngIf",e.showExtraCost)}}function KV(t,n){1&t&&(Be(0),m(1,"div",1)(2,"h3"),M(3),k(4,"translate"),_(),W(5,"textarea",30),k(6,"translate"),_(),je()),2&t&&(p(3),L(R(4,2,"NOTES")),p(2),Bt("placeholder",R(6,4,"NOTE")))}let QV=(()=>{class t{constructor(e,r){this.fb=e,this.translate=r,this.destroyed$=new te,this.areas=[],this.allSubAreas=[],this.subAreas=[],this.users=[],this.vehicleTypes=[["car","CAR"],["motorcycle","MOTORCYCLE"]],this.formSubmitted=new B,this.closeOrder=new B,this.fetchDeliveryCost=new B,this.deliveryCost=0,this.showCustomerDetails=!1,this.showPaymentSection=!1,this.showNotesSection=!1,this.showExtraCost=!1,this.errorMessageBusiness="",this.errorMessageDriver="",this.errorMessageVehicleTypes="",this.errorMessageArea="",this.errorMessageSubArea=""}ngOnInit(){let e=null;this.areas.length>0&&(e=this.areas.filter(i=>i.is_default),e&&e[0]&&e[0].name?(e=e[0].name,this.filterSubAreas(!1,e)):e=null);let r=null;"rb_delivery.role_business"==this.userRole&&this.currentUserId&&(r=this.users.filter(i=>i.id==this.currentUserId)[0].username),this.quickOrderForm=this.fb.group({customer_area:[e],customer_sub_area:[null],assign_to_business:[r],assign_to_agent:[null],vehicle_type:[null],customer_name:[""],customer_mobile:[""],cost:[""],extra_cost:[""],reference_id:[""],note:[""]}),this.quickOrderForm.valueChanges.pipe(Se(this.destroyed$)).subscribe(i=>{let o=0|this.users.filter(l=>l.username==i.assign_to_business)[0]?.id,s=0|this.areas.filter(l=>l.name==i.customer_area)[0]?.id,a=0|this.subAreas.filter(l=>l.name==i.customer_sub_area)[0]?.id;i&&this.computeDeliveryCost({sender_id:o,to_area_id:s,sub_area_id:a})})}submit(){if(this.errorMessageBusiness="",this.errorMessageDriver="",this.errorMessageVehicleTypes="",this.errorMessageArea="",this.errorMessageSubArea="",this.quickOrderForm.valid){let e,r,i,o,s;if(this.quickOrderForm.value&&this.quickOrderForm.value.assign_to_business){let l=this.users.filter(c=>"rb_delivery.role_business"==c.role_code&&c.username==this.quickOrderForm.value.assign_to_business);l&&l[0]&&l[0].id?e=l[0].id:this.errorMessageBusiness="INVALID_BUSINESS_SELECTION"}if(this.quickOrderForm.value&&this.quickOrderForm.value.assign_to_agent&&"rb_delivery.role_business"!=this.userRole){let l=this.users.filter(c=>"rb_delivery.role_driver"==c.role_code&&c.username==this.quickOrderForm.value.assign_to_agent);l&&l[0]&&l[0].id?r=l[0].id:this.errorMessageDriver="INVALID_DRIVER_SELECTION"}if(this.quickOrderForm.value&&this.quickOrderForm.value.vehicle_type){let l=this.vehicleTypes.filter(c=>this.quickOrderForm.value.vehicle_type==this.translate.instant(c[1]));l&&l[0]?i=l[0][0]:this.errorMessageVehicleTypes="INVALID_VEHICLE_TYPE_SELECTION"}if(this.quickOrderForm.value&&this.quickOrderForm.value.customer_area){let l=this.areas.filter(c=>c.name==this.quickOrderForm.value.customer_area);l&&l[0]&&l[0].id?o=l[0].id:this.errorMessageArea="INVALID_AREA_SELECTION"}if(this.quickOrderForm.value&&this.quickOrderForm.value.customer_sub_area){let l=this.subAreas.filter(c=>c.name==this.quickOrderForm.value.customer_sub_area);l&&l[0]&&l[0].id?s=l[0].id:this.errorMessageSubArea="INVALID_SUB_AREA_SELECTION"}if(this.errorMessageBusiness||this.errorMessageDriver||this.errorMessageArea||this.errorMessageSubArea||this.errorMessageVehicleTypes)return;let a={...this.quickOrderForm.value};"rb_delivery.role_business"==this.userRole&&delete a.assign_to_agent,e&&(a.assign_to_business=e),r&&(a.assign_to_agent=r),i&&(a.vehicle_type=i),o&&(a.customer_area=o),s&&(a.customer_sub_area=s),this.formSubmitted.emit(a)}}filterSubAreas(e,r){let i=r||(e&&e.target?e.target.value:"");this.subAreas=i?this.allSubAreas.filter(o=>o.parent_id[1]==i):[]}computeDeliveryCost(e){this.fetchDeliveryCost.emit(e)}close(){this.closeOrder.emit()}resetForm(){this.quickOrderForm.reset()}static#e=this.\u0275fac=function(r){return new(r||t)(C($F),C(Cr))};static#t=this.\u0275cmp=$t({type:t,selectors:[["app-quick-order"]],inputs:{areas:"areas",allSubAreas:"allSubAreas",users:"users",userRole:"userRole",currentUserId:"currentUserId",deliveryCost:"deliveryCost",showCustomerDetails:"showCustomerDetails",showPaymentSection:"showPaymentSection",showNotesSection:"showNotesSection",showExtraCost:"showExtraCost"},outputs:{formSubmitted:"formSubmitted",closeOrder:"closeOrder",fetchDeliveryCost:"fetchDeliveryCost"},decls:54,vars:35,consts:[[1,"quick-order-form",3,"formGroup","ngSubmit"],[1,"section"],["list","senders","formControlName","assign_to_business"],["id","senders"],[4,"ngFor","ngForOf"],["class","error-message",4,"ngIf"],["class","section",4,"ngIf"],["list","vehicleTypes","formControlName","vehicle_type"],["id","vehicleTypes"],[4,"ngIf"],["list","areas","formControlName","customer_area",3,"change"],["id","areas"],[3,"value",4,"ngFor","ngForOf"],["list","sub_areas","formControlName","customer_sub_area"],["id","sub_areas"],[1,"button-group",2,"margin-bottom","10px"],[2,"align-content","center"],[2,"font-size","18px","font-family","Arial, sans-serif","color","white","background-color","#4CAF50","padding","10px 20px","margin","20px","border","1px solid #ddd","border-radius","5px","text-align","center"],[1,"button-group"],["type","submit"],[2,"background-color","#e7453d",3,"click"],[3,"value",4,"ngIf"],[3,"value"],[1,"error-message"],["list","drivers","formControlName","assign_to_agent"],["id","drivers"],["type","text","formControlName","customer_name",3,"placeholder"],["type","text","pattern","^[\\u0621-\\u064A\\u0660-\\u06690-9 ]+$","formControlName","customer_mobile",3,"placeholder"],["type","text","pattern","^[\\u0621-\\u064A\\u0660-\\u06690-9 ]+(\\.[0-9]+)?$","formControlName","cost",3,"placeholder"],["type","text","pattern","^[\\u0621-\\u064A\\u0660-\\u06690-9 ]+(\\.[0-9]+)?$","formControlName","extra_cost",3,"placeholder"],["formControlName","note",3,"placeholder"]],template:function(r,i){1&r&&(m(0,"form",0),V("ngSubmit",function(){return i.submit()}),m(1,"div",1)(2,"label"),M(3),k(4,"translate"),_(),m(5,"div",1),W(6,"input",2),m(7,"datalist",3),S(8,xV,2,1,"ng-container",4),_()(),S(9,AV,3,3,"div",5),_(),S(10,NV,9,5,"div",6),m(11,"div",1)(12,"label"),M(13),k(14,"translate"),_(),m(15,"div",1),W(16,"input",7),m(17,"datalist",8),S(18,FV,3,3,"ng-container",4),_()(),S(19,LV,3,3,"div",5),_(),S(20,jV,11,11,"ng-container",9),m(21,"div",1)(22,"label"),M(23),k(24,"translate"),_(),m(25,"input",10),V("change",function(s){return i.filterSubAreas(s)}),_(),m(26,"datalist",11),S(27,HV,1,1,"option",12),_()(),S(28,zV,3,3,"div",5),m(29,"div",1)(30,"label"),M(31),k(32,"translate"),_(),W(33,"input",13),m(34,"datalist",14),S(35,UV,1,1,"option",12),_()(),S(36,$V,3,3,"div",5),S(37,ZV,9,8,"ng-container",9),S(38,KV,7,6,"ng-container",9),m(39,"div",15)(40,"div",16)(41,"h3"),M(42),k(43,"translate"),_()(),m(44,"div")(45,"h4",17),M(46),_()()(),m(47,"div",18)(48,"button",19),M(49),k(50,"translate"),_(),m(51,"button",20),V("click",function(){return i.close()}),M(52),k(53,"translate"),_()()()),2&r&&(b("formGroup",i.quickOrderForm),p(3),be("",R(4,21,"SELECT_SENDER"),":"),p(5),b("ngForOf",i.users),p(1),b("ngIf",i.errorMessageBusiness),p(1),b("ngIf","rb_delivery.role_business"!=i.userRole),p(3),be("",R(14,23,"SELECT_VEHICLE_TYPE"),":"),p(5),b("ngForOf",i.vehicleTypes),p(1),b("ngIf",i.errorMessageVehicleTypes),p(1),b("ngIf",i.showCustomerDetails),p(3),L(R(24,25,"SELECT_AREA")),p(4),b("ngForOf",i.areas),p(1),b("ngIf",i.errorMessageArea),p(3),L(R(32,27,"SELECT_SUB_AREA")),p(4),b("ngForOf",i.subAreas),p(1),b("ngIf",i.errorMessageSubArea),p(1),b("ngIf",i.showPaymentSection),p(1),b("ngIf",i.showNotesSection),p(4),be("",R(43,29,"DELIVERY_COST")," :"),p(4),L(i.deliveryCost),p(3),L(R(50,31,"SUBMIT")),p(3),L(R(53,33,"CLOSE")))},dependencies:[Ii,Rn,Tb,jh,zh,br,$o,mb,$h,vl,Bh,Yn],styles:[".quick-order-form[_ngcontent-%COMP%]{max-width:570px;margin:10px auto;padding:10px;border:1px solid #ccc;border-radius:5px}.quick-order-form[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]{margin-bottom:8px}.quick-order-form[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:8px;font-weight:700}.quick-order-form[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], .quick-order-form[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .quick-order-form[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:8px;margin-bottom:8px;border:1px solid #ccc;border-radius:4px;box-sizing:border-box}.is-invalid[_ngcontent-%COMP%]{border-color:red}.error-message[_ngcontent-%COMP%]{color:red;font-size:.8em;margin-top:5px;min-height:10px;margin-bottom:5px}.fixed-to-top[_ngcontent-%COMP%]{position:fixed;left:inherit;top:inherit;width:inherit;height:inherit;justify-content:center;align-items:center;display:flex;background-color:#00000050}.quick-order-form[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:10px 0;padding:0}.button-group[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:20px}button[_ngcontent-%COMP%]{flex:1;padding:10px;background-color:#007bff;color:#fff;border:none;border-radius:4px;cursor:pointer;transition:background-color .3s ease}button[_ngcontent-%COMP%]:hover{background-color:#0056b3}"]})}return t})();function YV(t,n){if(1&t){const e=oe();m(0,"div",5)(1,"h4"),M(2),k(3,"translate"),_(),m(4,"button",6),V("click",function(){return T(e),x(y().close())}),W(5,"img",7),_()()}2&t&&(p(2),L(R(3,1,"SELECT_STATUS")))}function XV(t,n){1&t&&(m(0,"div",8),M(1),k(2,"translate"),_()),2&t&&(p(1),L(R(2,1,"LOADING")))}const JV=function(t){return{background:t}};function eB(t,n){if(1&t){const e=oe();m(0,"div",9)(1,"div",10)(2,"span",11),M(3),_()(),m(4,"div",12)(5,"button",13),V("click",function(){const o=T(e).$implicit;return x(y().selectStatusFromList(o.name))}),M(6),k(7,"translate"),_()()()}if(2&t){const e=n.$implicit,r=y();p(3),L(e.title),p(2),b("disabled",r.loading)("ngStyle",Ne(6,JV,r.currentOrder.state!=e.name||r.loading?r.loading?"grey":"#3ab600":"red")),p(1),L(r.currentOrder.state==e.name?"DESELECT":R(7,4,"SELECT"))}}function tB(t,n){1&t&&(m(0,"div",14),M(1),k(2,"translate"),_()),2&t&&(p(1),be(" ",R(2,1,"YOU_CANT_CHANGE_THE_STATUS")," "))}let nB=(()=>{class t{constructor(){this.onClickStatus=new B,this.onClose=new B,this.nextStatusList=[]}ngOnInit(){}selectStatusFromList(e){this.onClickStatus.emit({state:e})}close(){this.onClose.emit()}static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275cmp=$t({type:t,selectors:[["app-status-list"]],inputs:{driversToSelect:"driversToSelect",currentOrder:"currentOrder",loading:"loading",currentStatus:"currentStatus",nextStatusList:"nextStatusList"},outputs:{onClickStatus:"onClickStatus",onClose:"onClose"},decls:5,vars:4,consts:[["class","fixed-to-top","style","justify-content: space-between; background-color: white; padding-block: 15px;",4,"ngIf"],["class","fixed-to-top loading",4,"ngIf"],[2,"padding","10px"],["class","list-item",4,"ngFor","ngForOf"],["class","fixed-to-top","style","font-size: 12px;",4,"ngIf"],[1,"fixed-to-top",2,"justify-content","space-between","background-color","white","padding-block","15px"],[3,"click"],["src","/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/close.svg",1,"icon"],[1,"fixed-to-top","loading"],[1,"list-item"],[1,"status-container"],[2,"width","45vw"],[2,"background-color","white","display","flex","flex-direction","row","padding","5px","gap","5px","border-radius","0 0 10px 10px"],[1,"select-btn",3,"disabled","ngStyle","click"],[1,"fixed-to-top",2,"font-size","12px"]],template:function(r,i){1&r&&(S(0,YV,6,3,"div",0),S(1,XV,3,3,"div",1),m(2,"div",2),S(3,eB,8,8,"div",3),S(4,tB,3,3,"div",4),_()),2&r&&(b("ngIf",!i.loading),p(1),b("ngIf",i.loading),p(2),b("ngForOf",i.nextStatusList),p(1),b("ngIf",0==i.nextStatusList.length))},dependencies:[Ii,Rn,_r,Yn],styles:[".status-container[_ngcontent-%COMP%]{padding:10px}.select-btn[_ngcontent-%COMP%]{width:100px;height:20px;border-radius:10px;background-color:#3ab600;border:none;cursor:pointer;color:#fff}.fixed-to-top[_ngcontent-%COMP%]{position:sticky;left:0;top:0;align-items:center;display:flex;box-shadow:0 2px 4px -1px #0003,0 4px 5px #00000024,0 1px 10px #0000001f}.fixed-to-top[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{all:unset;margin-inline:16px;margin-top:3px;cursor:pointer}.fixed-to-top[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{all:unset;margin-inline-start:16px}.loading[_ngcontent-%COMP%]{background-color:#000;justify-content:center;color:#fff}.list-item[_ngcontent-%COMP%]{display:flex;flex-direction:row;border-bottom:1px solid #E3E3E3;justify-content:space-between;align-items:center}.icon[_ngcontent-%COMP%]{width:20px}"]})}return t})();function rB(t,n){1&t&&W(0,"google-map",10),2&t&&b("options",y(2).mapOptions)}const iB=function(){return["state_id","many2one","Status",!0]},oB=function(t){return[t]},xw=function(){return[]};function sB(t,n){if(1&t){const e=oe();m(0,"app-side-menu-component",11),V("onClickAgent",function(i){return T(e),x(y(2).showAgentOnMap(i))})("onClickAssignDriver",function(i){return T(e),x(y(2).fetchAgentsSorted(i))})("onClickChooseStatus",function(i){return T(e),x(y(2).activateChangeStatus(i))})("onClickShowOnMap",function(i){T(e);const o=y(2);return x(i.agent?o.showAgentOnMap(i.agent,i.showOnlyRelatedMarkers):i.sender?o.showSenderOnMap(i.sender,i.showOnlyRelatedMarkers):i.route?o.showRouteOnMap(i.route,i.showOnlyRelatedMarkers):o.showOrderOnMap(i.order,i.showOnlyRelatedMarkers))})("onClickShowDriversInRange",function(i){T(e);const o=y(2);return o.filteredOnRange=i,x(o.filterOnRange())})("onSearch",function(i){return T(e),x(y(2).search(i))})("onActiveContentChange",function(i){return T(e),x(y(2).changeActiveContent(i))})("onQuickOrderDismiss",function(i){return T(e),x(y(2).showQuickOrder=i)})("onClickFilter",function(i){return T(e),x(y(2).updateDomain(i))})("onGroupOrdersChecked",function(i){return T(e),x(y(2).setCheckedOrderIds(i))})("onAction",function(i){return T(e),x(y(2).handleActionOnSelectedOrders(i))})("onShowToast",function(i){return T(e),x(y(2).showToast(i.text,i.type))}),k(1,"async"),_()}if(2&t){const e=y(2);let r;b("activeContent",e.activeContent)("groupByFields",e.groupByFields.length>0?e.groupByFields:Ne(27,oB,Qt(26,iB)))("isFilteredOnOrders",e.isFilteredOnOrders)("filterTexts",e.filterTexts)("loading",e.loading)("sessionId",e.sessionId)("orders",e.allOrders)("users",e.filteredUsers)("routes",e.routes)("doneRoutes",e.doneRoutes)("currentOrder",e.currentOrder)("statuses",e.visibleStatuses)("subAreas",e.allSubAreas)("activeAgent",e.filteredOnAgent)("activeRoute",e.filteredOnRoute)("activeSender",e.filteredOnSender)("aciveOrder",e.filteredOnOrder)("timeDurationConfig",e.timeDurationConfig)("userRole",e.userRole)("maxNumberOfShipments",e.maxNumberOfShipments)("driversToSelect",null!==(r=R(1,24,e.driversToSelect$))&&void 0!==r?r:Qt(29,xw))("dispatcherOrderFilters",e.dispatcherOrderFilters)("orderDefaultFilters",e.orderDefaultFilters)("showDriverStatusInSideMenuForBusiness",e.showDriverStatusInSideMenuForBusiness)}}const Aw=function(t){return{top:t}};function aB(t,n){if(1&t){const e=oe();m(0,"app-driver-list",12),V("onClickDriver",function(i){return T(e),x(y(2).updateCurrentOrder(i))})("onClickShowOnMap",function(i){return T(e),x(y(2).showAgentOnMap(i.agent))})("onClose",function(){return T(e),x(y(2).closeDriverList())}),k(1,"async"),_()}if(2&t){const e=y(2);let r;b("currentOrder",e.currentOrder)("activeAgent",e.filteredOnAgent)("driversToSelect",null!==(r=R(1,6,e.driversToSelect$))&&void 0!==r?r:Qt(8,xw))("loading",e.loading)("ngStyle",Ne(9,Aw,e.getTopOffset(e.currentOrder)))("nextStatusList",e.getNextStatus(e.currentOrder))}}function lB(t,n){if(1&t){const e=oe();m(0,"app-status-list",13),V("onClickStatus",function(i){return T(e),x(y(2).updateCurrentOrder(i))})("onClose",function(){return T(e),x(y(2).closeStatusList())}),_()}if(2&t){const e=y(2);b("currentOrder",e.currentOrder)("loading",e.loading)("ngStyle",Ne(4,Aw,e.getTopOffset(e.currentOrder)))("nextStatusList",e.getNextStatus(e.currentOrder))}}function cB(t,n){if(1&t){const e=oe();m(0,"div",14)(1,"div",15)(2,"label"),M(3),k(4,"translate"),m(5,"span",16),M(6),_()(),m(7,"div")(8,"label"),M(9),k(10,"translate"),_(),m(11,"input",17),V("ngModelChange",function(i){return T(e),x(y(2).fitGeofenceToBounds=i)})("ngModelChange",function(){return T(e),x(y(2).filterOnRange())}),_()()(),m(12,"input",18),V("ngModelChange",function(i){return T(e),x(y(2).driversRange=i)})("ngModelChange",function(){return T(e),x(y(2).filterOnRange())}),_()()}if(2&t){const e=y(2);p(3),Dt("",R(4,6,"RANGE"),": ",e.driversRange/1e3,""),p(3),L("KM"),p(3),L(R(10,8,"FIT_TO_BOUNDS")),p(2),b("ngModel",e.fitGeofenceToBounds),p(1),b("ngModel",e.driversRange)}}function uB(t,n){if(1&t){const e=oe();m(0,"app-quick-order",19),V("formSubmitted",function(i){return T(e),x(y(2).sendOrder(i))})("fetchDeliveryCost",function(i){return T(e),x(y(2).fetchDeliveryCost(i))})("closeOrder",function(){return T(e),x(y(2).showQuickOrder=!1)}),_()}if(2&t){const e=y(2);b("cdkDragBoundary",".main-app-container")("areas",e.allAreas)("allSubAreas",e.allSubAreas)("users",e.allUsers)("userRole",e.userRole)("currentUserId",e.currentUserId)("deliveryCost",e.deliveryCost)("showCustomerDetails",e.showCustomerDetails)("showPaymentSection",e.showPaymentSection)("showNotesSection",e.showNotesSection)("showExtraCost",e.showExtraCost)}}function dB(t,n){if(1&t&&(m(0,"p",23),M(1),_()),2&t){const e=y(3);p(1),L(e.toastTitle)}}const hB=function(t){return{"background-color":t}};function fB(t,n){if(1&t&&(m(0,"div",20),S(1,dB,2,1,"p",21),m(2,"p",22),M(3),_()()),2&t){const e=y(2);b("ngStyle",Ne(3,hB,"success"==e.toastType?"green":"fail"==e.toastType?"red":"orange")),p(1),b("ngIf",e.toastTitle),p(2),L(e.toastMessage)}}function pB(t,n){if(1&t){const e=oe();m(0,"div",1),S(1,rB,1,1,"google-map",2),m(2,"app-map-dashboard",3),V("onClickDeliveryOverdueOrders",function(i){return T(e),x(y().goToOrders(i,"delivery_overdue",["DELIVERY_OVERDUE"]))})("onClickPickupOverdueOrders",function(i){return T(e),x(y().goToOrders(i,"pickup_overdue",["PICKUP_OVERDUE"]))}),_(),S(3,sB,2,30,"app-side-menu-component",4),S(4,aB,2,11,"app-driver-list",5),S(5,lB,1,6,"app-status-list",6),S(6,cB,13,10,"div",7),S(7,uB,1,11,"app-quick-order",8),S(8,fB,4,5,"div",9),_()}if(2&t){const e=y();p(1),b("ngIf",e.googleApiLoaded),p(1),b("deliveryOverdueOrders",e.deliveryOverdueOrders)("pickupOverdueOrders",e.pickupOverdueOrders),p(1),b("ngIf",e.userRole&&e.isUsersFetched),p(1),b("ngIf",e.showDriverList&&e.currentOrder),p(1),b("ngIf",e.isChooseStatusActive),p(1),b("ngIf",e.filteredOnRange),p(1),b("ngIf",e.showQuickOrder),p(1),b("ngIf",e.isToastVisible)}}const kw=["longitude","latitude","username","mobile_number","commercial_number","area_id","address","whatsapp_mobile","role_code","online","nearest_driver_orders","dispatcher_business_orders","order_ids","commercial_name","vehicle_type"];var as=["create_date","longitude","latitude","state","state_id","assign_to_business","business_mobile_number","business_whatsapp_mobile","assign_to_agent","picked_up_eta_message","delivered_eta_message","eta_for_delivered","eta_for_picked_up","tracking_url","customer_name","customer_mobile","customer_area","customer_sub_area","cus_whatsapp_mobile","agent_commercial_number","agent_mobile_number","agent_whatsapp_mobile","status_last_updated_on"];const gB=["create_date","write_date","create_uid","driver_id","locations","direction","order_ids","done_locations","status"],xl=["rb_delivery.role_driver","rb_delivery.role_business"],mB=["id","name","is_default"],_B=["id","name","parent_id"],vB=["id","name","title","next_state_ids","role_action_status_ids"];var df=[];let yB=(()=>{class t{constructor(e,r,i,o,s){this.renderer=e,this.http=r,this.translate=i,this.ngZone=o,this.cdr=s,this.allOrders=[],this.routes=[],this.sessionId="",this.googleApiKey="",this.usersMarkers=[],this.ordersMarkers=[],this.driverLocationordersMarkers=[],this.filteredUsers=[],this.allAreas=[],this.allSubAreas=[],this.groupedOrders={},this.showDriversList=!1,this.activeContent="orders",this.currentUsersDomain=["online","=",!0],this.showQuickOrder=!1,this.isToastVisible=!1,this.allStatuses=[],this.driversRange=5e3,this.fitGeofenceToBounds=!0,this.allUsers=[],this.timeDurationConfig=0,this.maxNumberOfShipments=0,this.fetchRecordErrorMessage="",this.isFilteredOnOrders=!1,this.assignToBusinessMap=[],this.counter=0,this.nearestDriverStatuses=[],this.deliveryCost=0,this.movingRecordIds=[],this.currentOrderDomain=[],this.orderFilterDomain=[],this.orderSearchDomain=[],this.visibleStatuses=[],this.showCustomerDetails=!1,this.showPaymentSection=!1,this.showNotesSection=!1,this.showExtraCost=!1,this.doneOrderMarkers=[],this.routeMarkers=[],this.doneRoutes=[],this.MatchDriverColorToLastOrderStatus=!1,this.checkedOrderIds=[],this.dispatcherOrderFilters={},this.orderDefaultFilters={},this.orderCardFields=[],this.orderCardButtons=[],this.showDriverList=!1,this.driversToSelect$=new Pe([]),this.showDriverStatusInSideMenuForBusiness=!1,this.playSuccessSound=()=>{new Audio("/olivery_dispatcher_map/static/src/dispatcher_map/assets/sounds/success.wav").play()},this.playDangerSound=()=>{new Audio("/olivery_dispatcher_map/static/src/dispatcher_map/assets/sounds/danger.wav").play()},this.playWarningrSound=()=>{new Audio("/olivery_dispatcher_map/static/src/dispatcher_map/assets/sounds/warning.wav").play()},this.translate.setDefaultLang("en"),this.urlParams=new URLSearchParams(window.location.search),this.context=JSON.parse(this.urlParams.get("context")),"tz"in this.context&&delete this.context.tz,this.translate.use(this.context.lang.split("_")[0]),("ar"==this.translate.currentLang||"he"==this.translate.currentLang)&&(document.documentElement.dir="rtl"),this.sessionId=this.urlParams.get("session"),this.googleApiKey=this.urlParams.get("google_api_key"),this.mapOptions={gestureHandling:"greedy",fullscreenControl:!1,mapTypeControl:!1,streetViewControl:!1,center:{lat:31.971568170785243,lng:35.12121661838731}},this.addGoogleMapsScript()}fetchAreas(){var e=this;return re(function*(){e.allAreas.length<1&&(e.allAreas=yield e.fetchRecords("rb_delivery.area",[[],mB],"search_read")),e.allSubAreas.length<1&&(e.allSubAreas=yield e.fetchRecords("rb_delivery.sub_area",[[],_B],"search_read"))})()}fetchAllUsers(){var e=this;return re(function*(){e.allUsers=yield e.fetchRecords("rb_delivery.user",[[["role_code","in",xl]],kw],"search_read"),e.fetchOrders(!0)})()}fetchUsersFiltered(e,r){var i=this;return re(function*(){i.filteredUsers=[],i.filteredUsers=yield i.fetchRecords("rb_delivery.user",[e,kw],"search_read"),i.isUsersFetched=!0,r&&(yield i.addUsersOnMap(i.filteredUsers))})()}fetchUserOrderFilters(){var e=this;return re(function*(){if("orders"===e.activeContent){let r=yield e.fetchRecords("rb_delivery.dispatcher_filter",[{model_name:"order"}],"get_user_filters"),i=r.default_filters?r.default_filters:{},o=r.all_filters?r.all_filters:{};e.dispatcherOrderFilters={...i,...o},e.orderDefaultFilters={...i},Object.keys(e.orderDefaultFilters).length>0&&e.updateDomain(Object.keys(e.orderDefaultFilters))}})()}fetchOrders(e,r=!0){var i=this;return re(function*(){let o=i.getOrderDomain();i.allOrders=yield i.fetchRecords("rb_delivery.order",[o,as],"search_read",r),e&&(i.removeOrdersFromMap(),i.addOrdersOnMap(i.allOrders))})()}removeOrdersFromMap(){if(this.usersMarkers.forEach(e=>{e.marker.setMap(null)}),this.ordersMarkers.forEach(e=>{e.marker.setMap(null)}),this.ordersMarkers.length>0){const e=this.getBounds(this.ordersMarkers);this.map.googleMap?.fitBounds(e,200)}}fetchRoutes(e=!0){var r=this;return re(function*(){let i=yield r.fetchRecords("rb_delivery.routes",[[],gB],"search_read",e);r.routes=i.filter(o=>"in_progress"==o.status),r.doneRoutes=i.filter(o=>"done"==o.status)})()}syncNumberOfOrders(e,r){var i=this;return re(function*(){let o=i.usersMarkers.filter(s=>s.userId==e.id);yield i.getUserMarker(e).then(function(){var s=re(function*(a){o[0]&&(o[0].marker.setIcon({url:"data:image/svg+xml;base64,"+a,scaledSize:new google.maps.Size(64,64),origin:new google.maps.Point(0,0),anchor:new google.maps.Point(32,64)}),o[0].marker.setLabel({text:"\u2003\u2003\u2003"+r,color:r>=i.maxNumberOfShipments&&"rb_delivery.role_driver"==e.role_code?"orange":"green",fontSize:"20px",fontWeight:"bolder"}))});return function(a){return s.apply(this,arguments)}}())})()}fetchRecords(e,r,i,o=!1){var s=this;return re(function*(){o&&(s.loading=!0);try{if(!s.sessionId)throw new Error("Session ID is missing.");let a=new tn({"Content-Type":"application/json; charset=utf-8"}).set("X-Openerp-Session-Id",s.sessionId);return s.http.post(window.location.origin+"/web/dataset/call_kw",{jsonrpc:"2.0",method:"call",params:{context:s.context,model:e,method:i,args:r,kwargs:{context:s.context}}},{headers:a}).pipe(ze(c=>(s.loading=!1,c&&c.result||"number"==typeof c.result||"boolean"==typeof c.result?c.result:(s.showToast(c.error.message,"fail",c.error.data.message),s.fetchRecordErrorMessage=c.error.data.message,!1)))).pipe(FD(c=>function QN(t,n){const e=se(t)?t:()=>t,r=i=>i.error(e());return new ve(n?i=>n.schedule(r,0,i):r)}(c))).toPromise()}catch(a){throw s.showToast(e,"fail",a),console.error(`Error while fetching records for model ${e}:`,a),a}})()}onOrdersChangeLitener(){var e=this;this.ordersEventSource=new EventSource("https://"+window.location.hostname+"/orders_stream/"+JSON.stringify({...this.context,fields:as}),{withCredentials:!0}),this.ordersEventSource.onmessage=r=>{let i=JSON.parse(r.data);this.ngZone.run(re(function*(){for(let o of i)e.updateOrderLocationOnMap(o),yield e.addOrUpdateOrder(o),e.checkOverDueOrder(o);e.allOrders=[...e.allOrders]}))}}addOrUpdateOrder(e){let r=this.allOrders.filter(a=>e.id==a.id),i=0==r.length,o=r.length>0,s=r&&this.areObjectsEqual(this.allOrders[this.allOrders.indexOf(r[0])],e,"notification_timer");i?(this.allOrders.push(e),this.showToast(this.translate.instant("SEQUENCE")+":"+e.sequence+"\n"+this.translate.instant("STATUS")+":"+e.state_id[1],"success",this.translate.instant("NEW_ORDER_ADDED"))):o&&!s&&(this.allOrders[this.allOrders.indexOf(r[0])]=e,(!this.currentOrder||this.currentOrder.id!=e.id)&&r[0].state_id[0]!=e.state_id[0]&&this.showToast(this.translate.instant("ORDER_OF_SEQUENCE")+": "+e.sequence+"\n "+this.translate.instant("HAS_BEEN_MOVED_TO_STATUS")+": "+e.state_id[1],"success",this.translate.instant("ORDER_CHANGED_STATUS")),this.currentOrder&&this.currentOrder.id==e.id&&(this.currentOrder=!1)),this.loading=!1}areObjectsEqual(e,r,i){if("object"!=typeof e||"object"!=typeof r)return!1;let o=Object.keys(e),s=Object.keys(r);if(i&&(o=o.filter(a=>a!=i||a==i&&!e[a]),s=s.filter(a=>a!=i||a==i&&!r[a])),o.length!==s.length)return!1;for(const a of o){if(!r.hasOwnProperty(a))return!1;if("object"==typeof e[a]&&"object"==typeof r[a]){if(!this.areObjectsEqual(e[a],r[a]))return!1}else if(e[a]!==r[a])return!1}return!0}addOrUpdateUser(e){let r=this.filteredUsers.filter(s=>e.id==s.id),o=r.length>0;0==r.length?this.filteredUsers.push(e):o&&(this.filteredUsers[this.filteredUsers.indexOf(r[0])]=e)}checkOverDueOrder(e){if("pickup_overdue"==e.state){let r=[];if(this.pickupOverdueOrders&&(r=this.pickupOverdueOrders.filter(i=>i.id==e.id)),r?.length>0)return;this.pickupOverdueOrders.push(e)}else if("delivery_overdue"==e.state){let r;if(this.deliveryOverdueOrders&&(r=this.deliveryOverdueOrders.filter(i=>i.id==e.id)),r)return;this.deliveryOverdueOrders.push(e)}}onUsersChangeListener(){var e=this;this.usersEventSource&&this.usersEventSource.close(),this.usersEventSource=new EventSource("https://"+window.location.hostname+"/users_stream/"+JSON.stringify(this.context),{withCredentials:!0}),this.usersEventSource.onmessage=r=>{let i=JSON.parse(r.data).changed_users,o=JSON.parse(r.data).drivers_to_hide;this.ngZone.run(re(function*(){let s=e.usersMarkers.filter(a=>o.includes(a.userId));for(let a of s)a.marker.setMap(null);e.usersMarkers=e.usersMarkers.filter(a=>!o.includes(a.userId));for(let a of i)yield e.updateUserLocationOnMap(a),yield e.addOrUpdateUser(a)}))}}changeActiveContent(e,r=[],i=[""]){var o=this;return re(function*(){o.resetMapMarkers(),o.activeContent=e,"orders"==o.activeContent?(r.length>0?(o.isFilteredOnOrders=!0,o.filterTexts=i,o.orderFilterDomain=r):(o.orderFilterDomain=[],o.isFilteredOnOrders=!1),yield o.fetchUserOrderFilters(),yield o.fetchOrders(!0),o.allOrders.length>0&&o.fitBoundsOnOrders()):"drivers"==o.activeContent?(o.currentOrder=void 0,yield o.updateDomain(["ALL"]),o.allUsers.filter(s=>"rb_delivery.role_driver"==s.role_code).length>0&&o.fitBoundsOnDrivers()):"senders"==o.activeContent?(o.currentOrder=void 0,yield o.updateDomain(["ALL"]),o.allUsers.filter(s=>"rb_delivery.role_business"==s.role_code).length>0&&o.fitBoundsOnSenders()):"all"==o.activeContent&&(o.currentOrder=void 0,yield o.updateDomain(["ALL"]),yield o.fetchUserOrderFilters(),yield o.fetchOrders(!0),o.allOrders.length>0&&o.fitBoundsOnOrders(),o.usersMarkers.forEach(s=>{s.marker.setMap(o.map.googleMap)}))})()}fitBoundsOnSenders(){let e=this.usersMarkers.filter(r=>!r.isDriver);if(this.usersMarkers.forEach(r=>{r.marker.setMap(null)}),this.ordersMarkers.forEach(r=>{r.marker.setMap(null)}),e.forEach(r=>{r.marker.setMap(this.map.googleMap)}),e.length>0){const r=this.getBounds(e);this.map.googleMap?.fitBounds(r,200)}}fitBoundsOnDrivers(){let e=this.usersMarkers.filter(r=>r.isDriver);if(this.usersMarkers.forEach(r=>{r.marker.setMap(null)}),this.ordersMarkers.forEach(r=>{r.marker.setMap(null)}),e.forEach(r=>{r.marker.setMap(this.map.googleMap)}),e.length>0){const r=this.getBounds(e);this.map.googleMap?.fitBounds(r,200)}}fitBoundsOnOrders(){if(this.usersMarkers.forEach(e=>{e.marker.setMap(null)}),this.ordersMarkers.forEach(e=>{e.marker.setMap(this.map.googleMap)}),this.ordersMarkers.length>0){const e=this.getBounds(this.ordersMarkers);this.map.googleMap?.fitBounds(e,200)}}search(e,r=[]){var i=this;return re(function*(){"orders"==i.activeContent?(i.orderSearchDomain=e&&""!=e?["|","|","|","|","|",["sequence","ilike",e],["assign_to_agent","ilike",e],["state","ilike",e],["customer_mobile","ilike",e],["assign_to_business","ilike",e],["customer_name","ilike",e]]:[],yield i.fetchOrders(!0)):("drivers"==i.activeContent||"senders"==i.activeContent)&&(e&&""!=e?(r=r.concat([["role_code","in",xl],"|","|",["username","ilike",e],["mobile_number","ilike",e],["commercial_name","ilike",e]]),i.currentUsersDomain.length>0&&r.push(i.currentUsersDomain),i.fetchUsersFiltered(r)):(r=r.concat([["role_code","in",xl]]),i.currentUsersDomain.length>0&&r.push(i.currentUsersDomain),i.fetchUsersFiltered(r)))})()}getOrderDomain(){return[["state_id","in",df]].concat(this.orderFilterDomain).concat(this.orderSearchDomain)}ngOnInit(){this.fetchAreas(),this.initMapMarkerIcons()}initMapMarkerIcons(){var e=this;return re(function*(){e.motorcycleIcon=yield e.convertImageToBase64("/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/driver.png"),e.carIcon=yield e.convertImageToBase64("/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/car.png"),e.businessIcon=yield e.convertImageToBase64("/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/business.png"),e.customerIcon=yield e.convertImageToBase64("/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/customer.png")})()}updateUserLocationOnMap(e){var r=this;return re(function*(){let i=r.usersMarkers.filter(o=>o.userId==e.id);return i.length>0?void r.moveMarker(i[0].marker,e,"U").then(()=>{r.addUserMarkerOnMap(e)}):r.addUserMarkerOnMap(e)})()}updateOrderLocationOnMap(e){let r=this.ordersMarkers.filter(i=>i.orderId==e.id);if(this.driverLocationordersMarkers.forEach(i=>{e.id==i.orderId&&!this.nearestDriverStatuses.includes(e.state_id[0])&&i.marker.setMap(null)}),this.nearestDriverStatuses.includes(e.state_id[0]))r.length>0?(this.getOrderMarker(e).then(i=>{r[0].marker.setIcon({url:"data:image/svg+xml;base64,"+i,scaledSize:new google.maps.Size(64,64),origin:new google.maps.Point(0,0),anchor:new google.maps.Point(32,64)})}),this.moveMarker(r[0].marker,e,"O"),this.updateOrderMarkerClickListener(r[0].marker,e)):this.addOrderMarkerOnMap(e);else if(r.length>0){let i=this.ordersMarkers.indexOf(r[0]);this.ordersMarkers.forEach(o=>{e.id==o.orderId&&o.marker.setMap(null)}),this.ordersMarkers.splice(i,1)}}addUserMarkerOnMap(e){var r=this;return re(function*(){return new Promise(function(){var i=re(function*(o){e.longitude&&e.latitude&&(yield r.getUserMarker(e).then(function(){var s=re(function*(a){let l=yield r.getNumberOfOrders(e),c=new google.maps.Marker({position:{lat:Number(e.latitude),lng:Number(e.longitude)},icon:{url:"data:image/svg+xml;base64,"+a,scaledSize:new google.maps.Size(64,64),origin:new google.maps.Point(0,0),anchor:new google.maps.Point(32,64)},label:"rb_delivery.role_driver"==e.role_code?{text:"\u2003\u2003\u2003"+l,color:l<r.maxNumberOfShipments?"green":"orange",fontSize:"20px",fontWeight:"bolder"}:"",clickable:!0});c.setMap(r.map.googleMap);let u={userId:e.id,marker:c,isDriver:"rb_delivery.role_driver"==e.role_code},d=r.usersMarkers.filter(h=>h.userId==e.id);if(0==d.length){r.usersMarkers.push(u),r.updateUserMarkerClickListener(c,e);const h=r.getBounds(r.usersMarkers);r.map.googleMap?.fitBounds(h,200)}else d[0].marker.setMap(null),d[0].marker=c,c.setMap(r.map.googleMap),r.updateUserMarkerClickListener(c,e)});return function(a){return s.apply(this,arguments)}}())),o(!0)});return function(o){return i.apply(this,arguments)}}())})()}addOrderMarkerOnMap(e){e.longitude&&e.latitude&&this.nearestDriverStatuses.includes(e.state_id[0])?this.getOrderMarker(e).then(r=>{let i=new google.maps.Marker({position:{lat:Number(e.latitude),lng:Number(e.longitude)},icon:{url:"data:image/svg+xml;base64,"+r,scaledSize:new google.maps.Size(64,64),origin:new google.maps.Point(0,0),anchor:new google.maps.Point(32,64)},clickable:!0});i.setMap(this.map.googleMap),this.ordersMarkers.push({orderId:e.id,marker:i}),this.updateOrderMarkerClickListener(i,e)}):this.ordersMarkers.forEach(r=>{e.id==r.orderId&&r.marker.setMap(null)})}updateOrderMarkerClickListener(e,r){google.maps.event.clearInstanceListeners(e),e.addListener("click",()=>{this.showOrderInfoWindow(e,r)})}updateUserMarkerClickListener(e,r){return new Promise((i,o)=>{google.maps.event.clearInstanceListeners(e),e.addListener("click",()=>{this.showUserInfoWindow(e,r)}),i(!0)}).catch(i=>{})}showUserInfoWindow(e,r){var i=this;return re(function*(){let o="red",s="",a="",l="";if("rb_delivery.role_driver"==r.role_code){let u=yield i.getNumberOfOrders(r);0==u&&(o="#3AB600"),a=`<image style="border-radius:12px; box-shadow: 0 0 10px #ccc;" src="${window.location.origin}/web/image/rb_delivery.user/${r.id}/user_image/70x70"/>`,l="",s=`<button id="noOfOrdersButton" style="background-color:${o} ; border-style: none; cursor:pointer; padding-block: 10px; padding-inline: 30px; border-radius: 8px; margin-inline-start: auto; color:white;"> ${u} ${i.translate.instant("ORDERS_ASSIGNED")} </button>`,l=r.commercial_number?`\n          <div style="display:flex; flex-direction:column; text-align:center;">\n            <p>${r.commercial_name}</p>\n            <p>${i.translate.instant("MOBILE_NUMBER")}: <a href="tel:${r.commercial_number}">${r.commercial_number}</a></p>\n          </div>\n          \n        `:`\n          <div style="display:flex; flex-direction:column; text-align:center;">\n            <p>${r.commercial_name}</p>\n            <p>${i.translate.instant("MOBILE_NUMBER")}: <a href="tel:${r.mobile_number}">${r.mobile_number}</a></p>\n          </div>\n          \n        `}else if("rb_delivery.role_business"==r.role_code){let u=yield i.getNumberOfOrders(r);o="#3AB600",a=`<image style="border-radius:12px; box-shadow: 0 0 10px #ccc;" src="${window.location.origin}/web/image/rb_delivery.user/${r.id}/user_image/70x70"/>`,l=`<p>${r.commercial_name}</p>`,s=`<button id="noOfOrdersButton" style="background-color:${o} ; border-style: cursor:pointer; none; padding-block: 10px; padding-inline: 30px; border-radius: 8px; margin-inline-start: auto; color:white;"> ${u} ${i.translate.instant("ORDERS")}</button>`}let c='\n    <div style="display:flex; flex-direction:row; gap:20px; padding:15px; align-items:center; min-width:250px;">\n      '+a+"\n      "+l+"\n      "+s+"\n    </div>\n    ";setTimeout(()=>{document.getElementsByClassName("gm-style-iw-a")[0].parentNode.addEventListener("click",d=>{d.target&&"noOfOrdersButton"===d.target.id&&("rb_delivery.role_business"==r.role_code?i.changeActiveContent("orders",[["assign_to_business","=",r.id]],r.commercial_name):"rb_delivery.role_driver"==r.role_code&&i.changeActiveContent("orders",[["assign_to_agent","=",r.id]],r.commercial_name))})},500),i.showInfoWindow(c,e)})()}dismissOverdueOrder(){this.overdueOrder=void 0}showOrderInfoWindow(e,r){let i=this.getStatusColor(r.state),o=`\n          <button id="statusButton" style="background-color:${i}; border-style: none; padding-block: 10px; cursor:pointer; padding-inline: 30px; border-radius: 8px; color:white;">\n            ${this.allStatuses.filter(d=>d.name==r.state)[0].title}\n          </button>\n        `,s=`<image style="border-radius:50%; width:70px; box-shadow: 0 0 10px #ccc;" src="${this.customerIcon}"/>`,a=`<p>${r.sequence}</p>`,l="";this.orderCardFields&&this.orderCardFields.length>0&&(l="<div style='display:flex; flex-direction:column; gap:5px;'>",this.orderCardFields.forEach(d=>{l+=`<p><strong>${d.field_description}:</strong> ${r[d.name]||"N/A"}</p>`}),l+="</div>");let c="";this.orderCardButtons&&this.orderCardButtons.length>0&&(1===this.orderCardButtons.length?c=`\n                    <button id="${this.orderCardButtons[0].action}" \n                        style="background-color: #3AB600; border-style: none; padding: 10px 20px; cursor: pointer; border-radius: 8px; color: white;">\n                        ${this.orderCardButtons[0].name}\n                    </button>\n                `:2===this.orderCardButtons.length&&(c=`\n                  <div style="display:flex; gap:5px;">\n                      ${this.orderCardButtons.map(d=>{let h="statusButton"===d.action?i:"#3AB600",f="statusButton"===d.action?this.allStatuses.filter(g=>g.name==r.state)[0].title:d.name;return`\n                              <button id="${d.action}" \n                                  style="background-color:${h}; border-style: none; padding: 10px 20px; cursor: pointer; border-radius: 8px; color: white;">\n                                  ${f}\n                              </button>\n                          `}).join("")}\n                  </div>\n              `)),this.showInfoWindow(`\n        <div style="display:flex; flex-direction:column; gap:10px; align-items:center;">\n          ${s}\n          ${a}\n          ${l}\n          ${c||o}\n        </div>\n        `,e),setTimeout(()=>{document.getElementsByClassName("gm-style-iw-a")[0].parentNode.addEventListener("click",h=>{if(h.target){let f=h.target.id;"statusButton"===f?this.changeActiveContent("orders",[["id","=",r.id]],r.sequence):"assignAgent"===f&&this.fetchAgentsSorted(r)}})},500)}showInfoWindow(e,r){this.infowindow&&this.infowindow.close(),this.infowindow=new google.maps.InfoWindow({content:e}),this.infowindow.open(this.map.googleMap,r),google.maps.event.addListener(this.infowindow,"closeclick",()=>{this.closeDriverList()})}goToOrders(e,r,i){this.changeActiveContent("orders",[["state","=",r]],i);let o=this.ordersMarkers.filter(s=>s.orderId==e.id);o&&(this.zoomOnMarker(o[0].marker),this.showOrderInfoWindow(o[0].marker,e))}zoomOnMarker(e){this.smoothZoom(18,this.map.googleMap?.getZoom()),this.map.googleMap?.panTo(e.getPosition())}getUserMarker(e){var r=this;return re(function*(){let i="#012d56",o=window.location.origin+"/web/image/rb_delivery.user/"+e.id+"/user_image/50x50";if(e.online){if(i="green","rb_delivery.role_driver"==e.role_code&&(yield r.getNumberOfOrders(e))>=r.maxNumberOfShipments)if(r.MatchDriverColorToLastOrderStatus){const l=r.allOrders.filter(c=>e.nearest_driver_orders.includes(c.id));if(l.length>0){l.sort((u,d)=>new Date(d.status_last_updated_on).getTime()-new Date(u.status_last_updated_on).getTime());const c=l[0];i=c&&"in_progress"==c.state?"#0000ff":"orange"}}else i="orange"}else i="red";const a=`\n    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">\n      \n      <image style="width:30px!important;height:30px !important;" x="17" y="12" href="${yield r.convertImageToBase64(o)}"/>\n      <path id="Vector" d="M 54.987 22.533 C 52.187 10.213 41.44 4.667 32 4.667 C 32 4.667 32 4.667 31.973 4.667 C 22.56 4.667 11.787 10.187 8.987 22.507 C 5.867 36.267 20.244 46.119 27.871 53.452 C 30.698 56.172 31.839 59.319 32 59.333 C 32.161 59.347 33.276 55.945 36.076 53.225 C 43.703 45.892 58.107 36.293 54.987 22.533 Z M 31.641 41.465 C 25.121 41.447 16.996 35.851 16.891 26.345 C 16.786 16.839 25.32 11.976 31.641 11.996 C 37.962 12.016 46.68 16.254 46.978 26.345 C 47.276 36.436 38.161 41.483 31.641 41.465 Z" fill="${i}"/>\n      <image style="width:30px!important;height:30px !important;" x="-1" y="-1" href="${"rb_delivery.role_driver"==e.role_code&&"car"==e.vehicle_type?r.carIcon:"rb_delivery.role_driver"==e.role_code&&"car"!=e.vehicle_type?r.motorcycleIcon:"rb_delivery.role_business"==e.role_code?r.businessIcon:""}"/>\n    </svg>\n    `;return window.btoa(a)})()}getOrderMarker(e){var r=this;return re(function*(){let i=r.getStatusColor(e.state);const s=`\n    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">\n      \n      <image style="width:30px!important;height:30px !important;" x="17" y="12" href="${yield r.convertImageToBase64(r.customerIcon)}"/>\n      <path id="Vector" d="M 54.987 22.533 C 52.187 10.213 41.44 4.667 32 4.667 C 32 4.667 32 4.667 31.973 4.667 C 22.56 4.667 11.787 10.187 8.987 22.507 C 5.867 36.267 20.244 46.119 27.871 53.452 C 30.698 56.172 31.839 59.319 32 59.333 C 32.161 59.347 33.276 55.945 36.076 53.225 C 43.703 45.892 58.107 36.293 54.987 22.533 Z M 31.641 41.465 C 25.121 41.447 16.996 35.851 16.891 26.345 C 16.786 16.839 25.32 11.976 31.641 11.996 C 37.962 12.016 46.68 16.254 46.978 26.345 C 47.276 36.436 38.161 41.483 31.641 41.465 Z" fill="${i}"/>\n    </svg>\n    `;return window.btoa(s)})()}getStatusColor(e){let r="#012d56";switch(e){case"waiting":r="grey";break;case"picking_up":r="orange";break;case"picked_up":r="light-green";break;case"pickup_overdue":case"delivery_overdue":r="red"}return r}moveMarker(e,r,i){return new Promise((o,s)=>{let a=r.id.toString()+i;if(this.movingRecordIds.includes(a))return;this.movingRecordIds.push(a);let l=e.getPosition()?.lat(),c=e.getPosition()?.lng(),u=(r.latitude-l)/100,d=(r.longitude-c)/100,f=this;for(let g=0;g<100;g++)setTimeout(function(){l+=u,c+=d,e.setPosition({lat:l,lng:c}),99==g&&(f.movingRecordIds.splice(f.movingRecordIds.indexOf(a),1),o(!0))},5*g)})}addGoogleMapsScript(){const e=this.renderer.createElement("script");e.src="https://maps.googleapis.com/maps/api/js?v=beta&key="+this.googleApiKey+"&language="+this.translate.currentLang+"&libraries=geometry,marker,maps,visualization",this.renderer.appendChild(document.head,e),setTimeout(()=>{this.googleApiLoaded=!0,this.fetchStatusesIds(),this.fetchQuickOrderConfiguration(),this.fetchClientConfiguration(),this.fetchOrderCardDate()},1e3)}fetchClientConfiguration(){var e=this;return re(function*(){e.MatchDriverColorToLastOrderStatus=yield e.fetchRecords("rb_delivery.client_configuration",["match_driver_color_to_last_order_status"],"get_param"),e.showDriverStatusInSideMenuForBusiness=yield e.fetchRecords("rb_delivery.client_configuration",["show_driver_status_in_side_menu_for_business"],"get_param")})()}fetchOrderCardDate(){var e=this;return re(function*(){let r=yield e.fetchRecords("rb_delivery.dispatcher_order_card",[],"get_card_fields");r&&(e.orderCardFields=r.fields||[],e.orderCardButtons=r.buttons||[])})()}fetchQuickOrderConfiguration(){var e=this;return re(function*(){e.showCustomerDetails=yield e.fetchRecords("rb_delivery.client_configuration",["show_customer_details_on_dispatcher_quick_order"],"get_param"),e.showPaymentSection=yield e.fetchRecords("rb_delivery.client_configuration",["show_payment_section_on_dispatcher_quick_order"],"get_param"),e.showNotesSection=yield e.fetchRecords("rb_delivery.client_configuration",["show_note_section_on_dispatcher_quick_order"],"get_param"),e.showExtraCost=yield e.fetchRecords("rb_delivery.client_configuration",["show_extra_cost_on_dispatcher_quick_order"],"get_param")})()}fetchStatusesIds(){var e=this;return re(function*(){e.fetchNearestDriverDurationConfig(),df=yield e.fetchRecords("rb_delivery.client_configuration",["default_status_olivery_dispatcher_map"],"get_param"),e.fetchStatuses(),e.fetchGroupingConfig(),yield e.fetchUserOrderFilters(),yield e.fetchAllUsers(),e.fetchCurrentUser(),e.fetchUsersFiltered([["role_code","in",xl]]),e.onUsersChangeListener(),e.onOrdersChangeLitener()})()}fetchGroupingConfig(){var e=this;return re(function*(){e.groupByFields=yield e.fetchRecords("rb_delivery.dispatcher_map_config",[],"get_order_grouping_config"),as=[...as,...e.groupByFields.map(r=>r[0]).filter(r=>!as.includes(r))]})()}fetchCurrentUser(){var e=this;return re(function*(){let r=yield e.fetchRecords("rb_delivery.user",[],"get_user_info");r.length>0?(e.groupId=r[0].group_id[0],e.userRole=r[0].role_code,e.currentUserId=r[0].id):e.userRole="admin",["admin","rb_delivery.role_super_manager","rb_delivery.role_manager"].includes(e.userRole)&&(yield e.fetchRoutes(),e.onRoutesChangeLitener())})()}fetchNearestDriverDurationConfig(){var e=this;return re(function*(){e.timeDurationConfig=yield e.fetchRecords("rb_delivery.nearest_driver",["one_by_one","timer_duration"],"get_param"),e.maxNumberOfShipments=yield e.fetchRecords("rb_delivery.nearest_driver",["one_by_one","number_of_shipments"],"get_param"),e.nearestDriverStatuses=yield e.fetchRecords("rb_delivery.nearest_driver",["one_by_one","shipment_statuses"],"get_param")})()}fetchStatuses(){var e=this;return re(function*(){e.allStatuses=yield e.fetchRecords("rb_delivery.status",[[["status_type","=","olivery_order"]],vB],"search_read"),e.visibleStatuses=e.allStatuses.filter(r=>df.includes(r.id))})()}convertImageToBase64(e){return new Promise((r,i)=>{const o=new Image;o.crossOrigin="anonymous",o.onload=()=>{const s=document.createElement("canvas"),a=s.getContext("2d");s.height=o.naturalHeight,s.width=o.naturalWidth,a?.drawImage(o,0,0);const l=s.toDataURL();r(l)},o.onerror=i,o.src=e})}addUsersOnMap(e){var r=this;return re(function*(){if(e&&e.length>0)for(let i of e)"rb_delivery.role_driver"==i.role_code&&!i.online||(yield r.updateUserLocationOnMap(i))})()}addOrdersOnMap(e){if(this.pickupOverdueOrders=e.filter(r=>"pickup_overdue"==r.state),this.deliveryOverdueOrders=e.filter(r=>"delivery_overdue"==r.state),e&&e.length>0)for(let r of e)this.addOrderMarkerOnMap(r)}getBounds(e){let r,i,o,s;for(const l of e)r=void 0!==r?Math.max(r,l.marker.getPosition()?.lat()):l.marker.getPosition()?.lat(),i=void 0!==i?Math.min(i,l.marker.getPosition()?.lat()):l.marker.getPosition()?.lat(),o=void 0!==o?Math.max(o,l.marker.getPosition()?.lng()):l.marker.getPosition()?.lng(),s=void 0!==s?Math.min(s,l.marker.getPosition()?.lng()):l.marker.getPosition()?.lng();return{north:r,south:i,east:o,west:s}}smoothZoom(e,r){let i=this;if(!(r>=e)){let o=google.maps.event.addListener(i.map.googleMap,"zoom_changed",function(s){google.maps.event.removeListener(o),i.smoothZoom(e,r+1)});setTimeout(function(){i.map.googleMap.setZoom(r)},80)}}showOrderOnMap(e,r){this.directionsRenderer&&this.removeDirectionsFromMap(),this.filteredOnAgent=void 0,this.filteredOnRoute=void 0,this.filteredOnSender=void 0;let i=[];if(this.filteredOnOrder!=e){this.filteredOnOrder=e;let o=this.ordersMarkers.filter(s=>s.orderId==e.id);o.length>0&&this.showOrderInfoWindow(o[0].marker,e),this.ordersMarkers.forEach(s=>{e.id!=s.orderId?s.marker.setMap(null):this.nearestDriverStatuses.includes(e.state_id[0])&&(s.marker.setMap(this.map.googleMap),i.push(s))}),this.driverLocationordersMarkers.forEach(s=>{e.id==s.orderId&&!this.nearestDriverStatuses.includes(e.state_id[0])&&s.marker.setMap(null)}),r&&(i=this.showOrderRelatedMarkers(e).concat(i))}else i=this.showAllMarkers();if(i.length>0&&r){(!e.longitude||!e.latitude)&&this.showToast(this.translate.instant("LOCATION_NOT_SET_FOR_THE_ORDER"),"warning",this.translate.instant("WARNING"));const o=this.getBounds(i);this.map.googleMap?.fitBounds(o,200),this.geofence&&this.geofence.setMap(null)}else r&&(this.showAllMarkers(),this.showToast(this.translate.instant("PLEASE_SET_LOCATIONS_FOR_THE_SELECTED_ITEM"),"fail",this.translate.instant("NO_AVAILABLE_LOCATIONS")))}showOrderRelatedMarkers(e){let r=[];return this.usersMarkers.forEach(i=>{i.userId!=e.assign_to_agent[0]&&i.userId!=e.assign_to_business[0]?i.marker.setMap(null):(i.marker.setMap(this.map.googleMap),r.push(i))}),"rb_delivery.role_business"==this.userRole&&this.driverLocationordersMarkers.forEach(i=>{e.id==i.orderId&&(i.marker.setMap(this.map.googleMap),r.push(i))}),r}showAllMarkers(){this.directionsRenderer&&this.removeDirectionsFromMap();let e=[];return this.resetMapMarkers(),this.ordersMarkers.forEach(r=>{r.marker.setMap(this.map.googleMap),e.push(r)}),this.usersMarkers.forEach(r=>{r.marker.setMap(this.map.googleMap),e.push(r)}),e}resetMapMarkers(){this.filteredOnAgent=void 0,this.filteredOnSender=void 0,this.filteredOnOrder=void 0,this.filteredOnRange=void 0,this.geofence&&this.geofence.setMap(null)}showAgentOnMap(e,r){this.directionsRenderer&&this.removeDirectionsFromMap(),this.filteredOnOrder=void 0,this.filteredOnSender=void 0;let i=[];if(this.filteredOnAgent!=e){let o=[];this.filteredOnAgent=e;let s=this.usersMarkers.filter(a=>a.userId==e.id);s.length>0&&this.showUserInfoWindow(s[0].marker,e),this.ordersMarkers.forEach(a=>{let l=this.allOrders.filter(c=>c.id==a.orderId)[0];e.id!=l.assign_to_agent[0]?a.marker.setMap(null):(a.marker.setMap(this.map.googleMap),i.push(a),o.push(l.assign_to_business[0]))}),r&&(i=this.showAgentRelatedMarkers(e,o).concat(i))}else i=this.showAllMarkers();if(i.length>0&&r){const o=this.getBounds(i);this.map.googleMap?.fitBounds(o,200),this.geofence&&this.geofence.setMap(null)}else r&&(this.showAllMarkers(),this.showToast(this.translate.instant("PLEASE_SET_LOCATIONS_FOR_THE_SELECTED_ITEM"),"fail",this.translate.instant("NO_AVAILABLE_LOCATIONS")))}onRoutesChangeLitener(){var e=this;this.routesEventSource&&this.routesEventSource.close(),this.routesEventSource=new EventSource("https://"+window.location.hostname+"/routes_stream/"+JSON.stringify(this.context),{withCredentials:!0}),this.routesEventSource.onmessage=r=>{let i=JSON.parse(r.data);this.ngZone.run(re(function*(){i.forEach(o=>{let s=e.routes.filter(a=>o.id==a.id);s.length>0?(e.routes[e.routes.indexOf(s[0])]=o,e.filteredOnRoute&&s[0].id==e.filteredOnRoute.id&&(e.showRouteOnMap(o),"done"==o.status&&(e.routes=e.routes.filter(l=>l.id!=o.id),e.doneRoutes.filter(l=>o.id==l.id).length>0||e.doneRoutes.push(o)))):"in_progress"==o.status?e.routes.push(o):e.doneRoutes.filter(l=>o.id==l.id).length>0||e.doneRoutes.push(o)})}))}}showRouteOnMap(e,r){this.removeDirectionsFromMap(),this.filteredOnOrder=void 0,this.filteredOnSender=void 0;let i=[];if(this.filteredOnRoute!=e){"string"==typeof e.direction&&(e.direction=JSON.parse(e.direction),e.locations=JSON.parse(e.locations),e.done_locations=JSON.parse(e.done_locations)),this.filteredOnRoute=e,r&&(i=this.showRouteRelatedMarkers(e).concat(i)),this.directionsRenderer||(this.directionsRenderer=new google.maps.DirectionsRenderer({preserveViewport:!0}));for(let o of this.filteredOnRoute.done_locations){let s=new google.maps.LatLng(o.location.lat,o.location.lng),a=new google.maps.Marker({position:s,map:this.map.googleMap,icon:"/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/delivered-marker.svg"});this.doneOrderMarkers.push(a),a.addListener("click",()=>{let l=this.allOrders.filter(c=>c.id==o.orderId);l.length>0&&this.showOrderInfoWindow(a,l[0])})}if(this.ordersMarkers.forEach(o=>{o.marker.setMap(null)}),this.filteredOnRoute.direction){this.directionsRenderer.setOptions({suppressMarkers:!0}),this.directionsRenderer.setDirections(this.filteredOnRoute.direction);let o=0;const s=(this.filteredOnRoute.direction?.routes[0]).legs,l=new Date(new Date),c=this.allOrders.filter(u=>this.filteredOnRoute.order_ids.includes(u.id));s.forEach((u,d)=>{o+=u.duration?.value||0;const h=new Date(l.getTime()+1e3*o),f=h.getHours().toString().padStart(2,"0"),g=h.getMinutes().toString().padStart(2,"0");if(0==d)return;const D=new google.maps.Marker({position:this.filteredOnRoute.locations[d].location,map:this.map.googleMap,icon:"/olivery_dispatcher_map/static/src/dispatcher_map/assets/icons/order-marker.svg",label:`${f}:${g}`,zIndex:9999});this.routeMarkers.push(D),i.push({marker:D}),D.addListener("click",()=>{this.showOrderInfoWindow(D,c[d])})}),this.directionsRenderer.setMap(this.map.googleMap)}else i=[],this.doneOrderMarkers.forEach(o=>{i.push({marker:o})})}else i=this.showAllMarkers(),this.filteredOnRoute=void 0;if(i.length>0&&r){const o=this.getBounds(i);this.map.googleMap?.fitBounds(o,200),this.geofence&&this.geofence.setMap(null)}else r&&(this.showAllMarkers(),this.showToast(this.translate.instant("PLEASE_SET_LOCATIONS_FOR_THE_SELECTED_ITEM"),"fail",this.translate.instant("NO_AVAILABLE_LOCATIONS")))}removeDirectionsFromMap(){this.directionsRenderer&&this.directionsRenderer.setMap(null),this.doneOrderMarkers.forEach((e,r)=>{this.doneOrderMarkers[r].setMap(null)}),this.doneOrderMarkers=[],this.routeMarkers.forEach(e=>{e.setMap(null)}),this.routeMarkers=[]}showAgentRelatedMarkers(e,r){let i=[];return this.usersMarkers.forEach(o=>{o.userId==e.id||r.includes(o.userId)?(o.marker.setMap(this.map.googleMap),i.push(o)):o.marker.setMap(null)}),i}showRouteRelatedMarkers(e){let r=[];return this.usersMarkers.forEach(i=>{i.userId!=e.driver_id[0]?i.marker.setMap(null):(i.marker.setMap(this.map.googleMap),r.push(i))}),r}fetchAgentsSorted(e){var r=this;return re(function*(){r.closeStatusList();let i=r.filteredUsers.filter(s=>"rb_delivery.role_driver"==s.role_code);if(!e)return r.currentOrder=void 0,void r.driversToSelect$.next(i);r.currentOrder=r.allOrders.find(s=>s.id==e.id);let o=yield r.fetchRecords("ir.module.module",[[["name","=","olivery_eta"],["state","=","installed"]]],"search_read");if(e.latitude&&0!=e.latitude&&e.longitude&&0!=e.longitude&&o&&1==o.length){let s=yield r.fetchRecords("rb_delivery.order",[e,i],"sort_agents_by_google_api");(!s||!s.length)&&(s=i.sort((a,l)=>Number(l.online)-Number(a.online))),r.driversToSelect$.next(s)}else r.driversToSelect$.next(i);r.showDriverList=!0,r.cdr.detectChanges()})()}closeDriverList(){this.driversToSelect$.next([]),this.showDriverList&&(this.showDriverList=!1,this.cdr.detectChanges()),this.infowindow&&this.infowindow.close()}activateChangeStatus(e){this.currentOrder=this.allOrders.filter(r=>r.id==e.id)[0],this.isChooseStatusActive=!0,this.closeDriverList()}closeStatusList(){this.isChooseStatusActive=!1}updateCurrentOrder(e){var r=this;return re(function*(){"state"in e&&(r.allOrders[r.allOrders.indexOf(r.currentOrder)].add_fade_animation=!0);let i=[[r.currentOrder.id],e];try{(yield r.fetchRecords("rb_delivery.order",i,"write"))?(r.closeDriverList(),r.closeStatusList()):"state"in e&&(r.allOrders[r.allOrders.indexOf(r.currentOrder)].add_fade_animation=!1)}catch(o){console.error("Error during driver assignment:",o)}})()}filterOnRange(){this.geofence?(this.geofence.setRadius(this.driversRange),this.geofence.setCenter(this.filteredOnRange)):this.geofence=new google.maps.Circle({strokeColor:"#0275ff",strokeOpacity:.8,strokeWeight:2,fillColor:"#0275ff",fillOpacity:.35,center:this.filteredOnRange,radius:this.driversRange}),this.geofence.setMap(this.map.googleMap),this.fitGeofenceToBounds&&this.map.googleMap?.fitBounds(this.geofence.getBounds()),this.usersMarkers.forEach(e=>{const r=e.marker.getPosition();google.maps.geometry.spherical.computeDistanceBetween(this.filteredOnRange,r)<=this.driversRange||!e.isDriver?e.marker.setMap(this.map.googleMap):e.marker.setMap(null)})}getTopOffset(e){if(!e)return"0px";let r=document.getElementById(e.id.toString())?.offsetTop-document.getElementById("groupContainer")?.scrollTop;return r+40*document.documentElement.clientHeight/100>document.documentElement.clientHeight&&(r=document.documentElement.clientHeight-40*document.documentElement.clientHeight/100),r.toString()+"px"}sendOrder(e){var r=this;return re(function*(){(yield r.fetchRecords("rb_delivery.order",[e],"create"))?(r.showQuickOrder=!1,r.showToast(r.translate.instant("ORDER_ADDED_SUCCESSFULLY"),"success")):r.showToast(r.translate.instant(r.fetchRecordErrorMessage?r.fetchRecordErrorMessage:"ERROR_WHILE_ADDING_ORDER"),"fail")})()}showToast(e,r,i){this.isToastVisible=!0,this.toastMessage=e,this.toastType=r,this.toastTitle=i,setTimeout(()=>{this.isToastVisible=!1,this.toastMessage=void 0,this.toastType=void 0,this.toastTitle=void 0},5e3),"success"==r?this.playSuccessSound():"fail"==r?this.playDangerSound():"warning"==r&&this.playWarningrSound(),this.cdr.detectChanges()}showSenderOnMap(e,r){this.directionsRenderer&&this.removeDirectionsFromMap(),this.filteredOnOrder=void 0,this.filteredOnAgent=void 0;let i=[];if(this.filteredOnSender!=e){this.filteredOnSender=e;let o=this.usersMarkers.filter(s=>s.userId==e.id);o.length>0&&this.showUserInfoWindow(o[0].marker,e),this.ordersMarkers.forEach(s=>{let a=this.allOrders.filter(l=>l.id==s.orderId)[0];e.id!=a.assign_to_business[0]?s.marker.setMap(null):(s.marker.setMap(this.map.googleMap),i.push(s))}),this.usersMarkers.forEach(s=>{s.userId!=e.id?s.marker.setMap(null):(s.marker.setMap(this.map.googleMap),i.push(s))})}else i=this.showAllMarkers();if(i.length>0&&r){const o=this.getBounds(i);this.map.googleMap?.fitBounds(o,200),this.geofence&&this.geofence.setMap(null)}else r&&(this.showAllMarkers(),this.showToast(this.translate.instant("PLEASE_SET_LOCATIONS_FOR_THE_SELECTED_ITEM"),"fail",this.translate.instant("NO_AVAILABLE_LOCATIONS")))}updateDomain(e){var r=this;return re(function*(){if(r.filterTypes=e,r.currentUsersDomain=[],r.orderFilterDomain=[],e.length>0){for(let i of e)if("ALL"===i){r.currentUsersDomain=[];let o=[];o.push("senders"==r.activeContent?"rb_delivery.role_business":"rb_delivery.role_driver"),yield r.fetchUsersFiltered([["role_code","in",o]],!0)}else if("OFFLINE_BUSINESSES"===i)r.currentUsersDomain.push(["online","=",!1]),yield r.fetchUsersFiltered([["role_code","=","rb_delivery.role_business"],...r.currentUsersDomain],!0);else if("ONLINE_BUSINESSES"===i)r.currentUsersDomain.push(["online","=",!0]),yield r.fetchUsersFiltered([["role_code","=","rb_delivery.role_business"],...r.currentUsersDomain],!0);else if("DEFAULT"===i)"orders"===r.activeContent?(r.orderFilterDomain=[],yield r.fetchOrders(!0)):"senders"===r.activeContent&&(r.currentUsersDomain.push(["online","=",!0]),yield r.fetchUsersFiltered([["role_code","=","rb_delivery.role_business"],...r.currentUsersDomain],!0));else if("TODAY_ORDERS"===i){let o=r.getTodayStartEnd();r.orderFilterDomain.push(["create_date",">=",o[0]],["create_date","<=",o[1]]),yield r.fetchOrders(!0)}else if(i in r.dispatcherOrderFilters){const o=JSON.parse(r.dispatcherOrderFilters[i]);r.orderFilterDomain.push(...o),yield r.fetchOrders(!0)}}else yield r.fetchOrders(!0);r.clearUsersOfMap()})()}getNextStatus(e){if(!e)return[];let r=this.allStatuses.filter(s=>s.id==e.state_id[0])[0].next_state_ids;return this.allStatuses.filter(s=>r.includes(s.id)).filter(s=>!this.groupId||s.role_action_status_ids.includes(this.groupId))}getTodayStartEnd(){const e=new Date;return[new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,0,0).toISOString(),new Date(e.getFullYear(),e.getMonth(),e.getDate(),23,59,59).toISOString()]}clearUsersOfMap(){let e=this.filteredUsers.map(r=>r.id);for(let r of this.usersMarkers)e.includes(r.userId)||r.marker.setMap(null)}fetchDeliveryCost(e){var r=this;return re(function*(){r.orderTypeId||(r.orderTypeId=1|(yield r.fetchRecords("rb_delivery.order_type",[[["default","=",!0]],["id"],0,1],"search_read"))[0]?.id),r.deliveryCost=yield r.fetchRecords("rb_delivery.pricelist",[{...e,order_type_id:r.orderTypeId}],"get_price")})()}getNumberOfOrders(e){return re(function*(){return"rb_delivery.role_business"==e.role_code?e.dispatcher_business_orders.length:"rb_delivery.role_driver"==e.role_code?e.nearest_driver_orders.length:0})()}setCheckedOrderIds(e){this.checkedOrderIds=e,this.checkedOrderIds.length>0?this.ordersEventSource.close():this.onOrdersChangeLitener()}handleActionOnSelectedOrders(e){var r=this;return re(function*(){if("assign_agent"==e.action){r.closeDriverList();try{let i=[r.checkedOrderIds,e.values];(yield r.fetchRecords("rb_delivery.order",i,"write"))&&(r.closeDriverList(),r.closeStatusList())}catch(i){console.error("Error during driver assignment:",i)}}})()}static#e=this.\u0275fac=function(r){return new(r||t)(C(Un),C(dh),C(Cr),C(U),C(Ei))};static#t=this.\u0275cmp=$t({type:t,selectors:[["app-root"]],viewQuery:function(r,i){if(1&r&&function Dy(t,n,e){const r=ce();r.firstCreatePass&&(wy(r,new yy(t,n,e),-1),2==(2&n)&&(r.staticViewQueries=!0)),by(r,E(),n)}(it,5),2&r){let o;Ci(o=Di())&&(i.map=o.first)}},decls:1,vars:1,consts:[["class","main-app-container",4,"ngIf"],[1,"main-app-container"],["height","100vh","width","calc(100vw - 570px)","style","position: absolute; right: 0; width: calc(100vw - 570px);",3,"options",4,"ngIf"],[3,"deliveryOverdueOrders","pickupOverdueOrders","onClickDeliveryOverdueOrders","onClickPickupOverdueOrders"],[3,"activeContent","groupByFields","isFilteredOnOrders","filterTexts","loading","sessionId","orders","users","routes","doneRoutes","currentOrder","statuses","subAreas","activeAgent","activeRoute","activeSender","aciveOrder","timeDurationConfig","userRole","maxNumberOfShipments","driversToSelect","dispatcherOrderFilters","orderDefaultFilters","showDriverStatusInSideMenuForBusiness","onClickAgent","onClickAssignDriver","onClickChooseStatus","onClickShowOnMap","onClickShowDriversInRange","onSearch","onActiveContentChange","onQuickOrderDismiss","onClickFilter","onGroupOrdersChecked","onAction","onShowToast",4,"ngIf"],["class","app-driver-container",3,"currentOrder","activeAgent","driversToSelect","loading","ngStyle","nextStatusList","onClickDriver","onClickShowOnMap","onClose",4,"ngIf"],["class","app-driver-container",3,"currentOrder","loading","ngStyle","nextStatusList","onClickStatus","onClose",4,"ngIf"],["class","app-slider-container",4,"ngIf"],["class","app-quick-order","cdkDrag","",3,"cdkDragBoundary","areas","allSubAreas","users","userRole","currentUserId","deliveryCost","showCustomerDetails","showPaymentSection","showNotesSection","showExtraCost","formSubmitted","fetchDeliveryCost","closeOrder",4,"ngIf"],["class","toast",3,"ngStyle",4,"ngIf"],["height","100vh","width","calc(100vw - 570px)",2,"position","absolute","right","0","width","calc(100vw - 570px)",3,"options"],[3,"activeContent","groupByFields","isFilteredOnOrders","filterTexts","loading","sessionId","orders","users","routes","doneRoutes","currentOrder","statuses","subAreas","activeAgent","activeRoute","activeSender","aciveOrder","timeDurationConfig","userRole","maxNumberOfShipments","driversToSelect","dispatcherOrderFilters","orderDefaultFilters","showDriverStatusInSideMenuForBusiness","onClickAgent","onClickAssignDriver","onClickChooseStatus","onClickShowOnMap","onClickShowDriversInRange","onSearch","onActiveContentChange","onQuickOrderDismiss","onClickFilter","onGroupOrdersChecked","onAction","onShowToast"],[1,"app-driver-container",3,"currentOrder","activeAgent","driversToSelect","loading","ngStyle","nextStatusList","onClickDriver","onClickShowOnMap","onClose"],[1,"app-driver-container",3,"currentOrder","loading","ngStyle","nextStatusList","onClickStatus","onClose"],[1,"app-slider-container"],[2,"display","flex","justify-content","space-between"],[2,"font-size","8px"],["type","checkbox",3,"ngModel","ngModelChange"],["step","100","type","range","min","1000","max","50000","value","5000","id","driversRange",1,"slider",3,"ngModel","ngModelChange"],["cdkDrag","",1,"app-quick-order",3,"cdkDragBoundary","areas","allSubAreas","users","userRole","currentUserId","deliveryCost","showCustomerDetails","showPaymentSection","showNotesSection","showExtraCost","formSubmitted","fetchDeliveryCost","closeOrder"],[1,"toast",3,"ngStyle"],["style","font-weight: bolder;",4,"ngIf"],[2,"font-size","15px"],[2,"font-weight","bolder"]],template:function(r,i){1&r&&S(0,pB,9,9,"div",0),2&r&&b("ngIf",i.googleApiLoaded)},dependencies:[Rn,_r,it,br,Nh,bh,$o,Xo,Ow,MV,OV,Tw,QV,nB,FC,Yn],styles:["#map[_ngcontent-%COMP%]{position:absolute;right:0;width:calc(100vw - 570px);height:100vh}.app-driver-container[_ngcontent-%COMP%]{flex-direction:column;display:flex;position:absolute;left:570px;background:white;border-radius:10px;box-shadow:0 0 10px #ccc;width:20vw;overflow:scroll;max-height:40vh;top:247px;padding:0}.app-slider-container[_ngcontent-%COMP%]{flex-direction:column;display:flex;position:absolute;right:calc(90vw - 570px - ((100vw - 570px)/2));background:white;border-radius:10px;box-shadow:0 0 10px #ccc;width:20vw;overflow:scroll;padding:25px 12px 12px;max-height:40vh;bottom:20px}.app-quick-order[_ngcontent-%COMP%]{flex-direction:column;position:absolute;height:80vh;left:40vw;background:#fcefe7;width:20vw;overflow:scroll;padding:12px;height:-moz-fit-content;height:fit-content;border-radius:15px;box-shadow:0 2px 10px #0003;transition:box-shadow .2s cubic-bezier(0,0,.2,1)}.toast[_ngcontent-%COMP%]{position:fixed;right:15px;top:15px;height:-moz-fit-content;height:fit-content;padding:15px;color:#fff;border-radius:10px;box-shadow:0 2px 10px #0003;z-index:1000;text-align:center;max-width:570px}  .cdk-virtual-scroll-content-wrapper{max-width:100%}"]})}return t})();class CB{http;prefix;suffix;constructor(n,e="/assets/i18n/",r=".json"){this.http=n,this.prefix=e,this.suffix=r}getTranslation(n){return this.http.get(`${this.prefix}${n}${this.suffix}`)}}let DB=(()=>{class t{static#e=this.\u0275fac=function(r){return new(r||t)};static#t=this.\u0275mod=et({type:t,bootstrap:[yB]});static#n=this.\u0275inj=We({imports:[zP,KN,vN,GF,JN.forRoot({loader:{provide:Uo,useFactory:bB,deps:[dh]}}),o2,WF,EL]})}return t})();function bB(t){return new CB(t,window.location.origin+"/olivery_dispatcher_map/static/src/dispatcher_map/assets/i18n/",".json")}jP().bootstrapModule(DB).catch(t=>console.error(t))}},se=>{se(se.s=404)}]);