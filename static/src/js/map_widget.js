
odoo.define('olivery_dispatcher_map.dispatcher_map_widget', function (require) {
	"use strict";
	var Widget = require('web.Widget');
	var core = require('web.core');
	var rpc = require('web.rpc');
	var session = require('web.session');
	
	var AngularWidget = Widget.extend({
		template: 'dispatcher_map_template',

		start: function () {
			var self = this
			return this._super.apply(this, arguments).then(function () {
				rpc.query({
					model: 'rb_delivery.user',
					method: 'get_session_id',
					args: [[]]
				}).then(session_id=>{
					rpc.query({
						model: 'rb_delivery.client_configuration',
						method: 'get_param',
						args: ['google_map_key']
					})
					.then(function (google_api_key) {
						self.$el.html(core.qweb.render('dispatcher_map_template', {
							session: session_id,
							google_api_key:google_api_key,
                            context: JSON.stringify(session.user_context)
						}));
					})
					
				})
			});
				
				
        },
	});

	core.action_registry.add('olivery_dispatcher_map.map_widget', AngularWidget);


	return AngularWidget;
});